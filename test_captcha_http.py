#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试HTTP请求模式处理滑块验证码
验证章节测验.py的修改是否正确
"""

import sys
import os

# 添加验证码处理模块路径
captcha_module_path = os.path.join(os.path.dirname(__file__), "250403超星学习验证码")
if captcha_module_path not in sys.path:
    sys.path.insert(0, captcha_module_path)

try:
    from CaptchaHandler import CaptchaHandler
    print("✓ CaptchaHandler模块导入成功")
except ImportError as e:
    print(f"✗ CaptchaHandler模块导入失败: {e}")
    sys.exit(1)

def test_captcha_handler():
    """测试验证码处理器"""
    print("\n开始测试验证码处理器...")
    
    try:
        # 创建验证码处理器
        captcha_handler = CaptchaHandler()
        print("✓ 验证码处理器创建成功")
        
        # 测试获取验证码图片
        print("测试获取验证码图片...")
        shade_image, cutout_image, token = captcha_handler.get_captcha_images()
        
        if shade_image and cutout_image and token:
            print("✓ 验证码图片获取成功")
            print(f"  - 背景图片大小: {len(shade_image)} bytes")
            print(f"  - 滑块图片大小: {len(cutout_image)} bytes")
            print(f"  - Token: {token[:20]}...")
            
            # 测试识别滑动距离
            print("测试识别滑动距离...")
            x_distance = captcha_handler.recognize_slide_distance(shade_image, cutout_image)
            
            if x_distance is not None:
                print(f"✓ 滑动距离识别成功: x = {x_distance}")
                
                # 测试验证验证码
                print("测试验证验证码...")
                success, validate_str = captcha_handler.verify_captcha(token, x_distance)
                
                if success:
                    print("✓ 验证码验证成功")
                    if validate_str:
                        print(f"  - Validate字符串: {validate_str[:20]}...")
                    return True
                else:
                    print("✗ 验证码验证失败")
                    return False
            else:
                print("✗ 滑动距离识别失败")
                return False
        else:
            print("✗ 验证码图片获取失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_solve():
    """测试完整的验证码解决方案"""
    print("\n开始测试完整的验证码解决方案...")
    
    try:
        captcha_handler = CaptchaHandler()
        success, validate_str = captcha_handler.solve_captcha(max_attempts=2)
        
        if success:
            print("✓ 完整验证码解决方案测试成功")
            if validate_str:
                print(f"  - Validate字符串: {validate_str[:20]}...")
            return True
        else:
            print("✗ 完整验证码解决方案测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("章节测验.py HTTP请求模式验证码处理测试")
    print("=" * 50)
    
    # 测试验证码处理器
    test1_result = test_captcha_handler()
    
    # 测试完整解决方案
    test2_result = test_complete_solve()
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"验证码处理器测试: {'✓ 通过' if test1_result else '✗ 失败'}")
    print(f"完整解决方案测试: {'✓ 通过' if test2_result else '✗ 失败'}")
    
    if test1_result or test2_result:
        print("\n✓ HTTP请求模式验证码处理功能正常")
        print("章节测验.py的修改应该可以正常工作")
    else:
        print("\n✗ HTTP请求模式验证码处理功能异常")
        print("可能需要检查验证码处理模块或网络连接")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
