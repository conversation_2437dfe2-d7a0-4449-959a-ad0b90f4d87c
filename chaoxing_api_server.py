#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通作业列表API服务
提供API接口，接收用户账号密码并返回作业列表
"""

import os
import re
import json
import base64
import pyaes
import requests
import time
import random
import concurrent.futures
from flask import Flask, request, jsonify
from concurrent.futures import ThreadPoolExecutor
from queue import Queue
import argparse

# 创建Flask应用
app = Flask(__name__)

# 全局配置
config = {
    "max_workers": 20,  # 默认课程线程池大小
    "max_page_workers": 10,  # 默认页面线程池大小
    "timeout": 15,  # 默认超时时间(秒)
    "max_connections": 100,  # 最大并发连接数
}

# 创建全局线程池，用于处理多账号请求
from concurrent.futures import ThreadPoolExecutor

global_executor = ThreadPoolExecutor(max_workers=config["max_connections"])

# 创建全局连接池配置
adapter = requests.adapters.HTTPAdapter(
    pool_connections=config["max_connections"],
    pool_maxsize=config["max_connections"],
    max_retries=3,
    pool_block=False,
)

# AES密钥
AES_KEY = "u2oh6Vu^HWe4_AES"

# 全局缓存
course_enc_cache = {}


# PKCS7填充
def pkcs7_padding(s, block_size=16):
    bs = block_size
    return s + (bs - len(s) % bs) * chr(bs - len(s) % bs).encode()


# 分割数据块
def split_to_data_blocks(byte_str, block_size=16):
    length = len(byte_str)
    j, y = divmod(length, block_size)
    blocks = []
    shenyu = j * block_size
    for i in range(j):
        start = i * block_size
        end = (i + 1) * block_size
        blocks.append(byte_str[start:end])
    stext = byte_str[shenyu:]
    if stext:
        blocks.append(stext)
    return blocks


# AES加密类
class AESCipher:
    def __init__(self):
        self.key = AES_KEY.encode("utf8")
        self.iv = AES_KEY.encode("utf8")

    def encrypt(self, plaintext: str):
        ciphertext = b""
        cbc = pyaes.AESModeOfOperationCBC(self.key, self.iv)
        plaintext = plaintext.encode("utf-8")
        blocks = split_to_data_blocks(pkcs7_padding(plaintext))
        for b in blocks:
            ciphertext = ciphertext + cbc.encrypt(b)
        base64_text = base64.b64encode(ciphertext).decode("utf8")
        return base64_text


# 超星学习通作业管理器类
class ChaoxingHomeworkManager:
    def __init__(self, username, password):
        """
        初始化超星学习通作业管理器
        :param username: 加密后的用户名
        :param password: 加密后的密码
        """
        self.username = username
        self.password = password
        self.session = requests.Session()
        # 禁用SSL验证，解决证书验证失败问题
        self.session.verify = False
        # 设置连接池参数，提高并发性能
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        # 设置默认超时时间
        self.session.timeout = config["timeout"]
        self.cookies = None
        self.base_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }
        # 初始化course_enc属性为None
        self.course_enc = None
        # 初始化stuenc属性为None
        self.stuenc = None
        # 过滤选项
        self.include_finished = False
        self.include_ended_courses = False

    def login(self):
        """
        登录超星学习通
        :return: 登录是否成功
        """
        try:
            url = "https://passport2.chaoxing.com/fanyalogin"

            payload = {
                "fid": "-1",
                "uname": self.username,
                "password": self.password,
                "refer": "https%3A%2F%2Fi.chaoxing.com",
                "t": "true",
                "forbidotherlogin": "0",
                "validate": "",
                "doubleFactorLogin": "0",
                "independentId": "0",
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Pragma": "no-cache",
                "Cache-Control": "no-cache",
                "sec-ch-ua-platform": '"Windows"',
                "X-Requested-With": "XMLHttpRequest",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "Origin": "https://passport2.chaoxing.com",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://passport2.chaoxing.com/login?fid=&newversion=true&refer=https%3A%2F%2Fi.chaoxing.com",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            }

            # 添加超时设置
            response = self.session.post(url, data=payload, headers=headers, timeout=15)

            try:
                result = json.loads(response.text)
                if result.get("status") and result.get("status") is True:
                    self.cookies = self.session.cookies
                    return True
                else:
                    error_msg = result.get("msg", "未知错误")
                    return False
            except json.JSONDecodeError:
                # 检查是否是HTML响应，可能是登录页面或验证码页面
                if "<html" in response.text.lower():
                    if "验证码" in response.text or "captcha" in response.text.lower():
                        return False
                    elif "登录" in response.text or "login" in response.text.lower():
                        return False
                return False
        except requests.exceptions.Timeout:
            return False
        except requests.exceptions.ConnectionError:
            return False
        except Exception:
            return False

    def get_course_list(self):
        """
        获取课程列表，过滤掉已结束的课程
        :return: 课程列表
        """
        try:
            import urllib3
            from bs4 import BeautifulSoup

            # 禁用SSL警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            url = "https://mooc2-ans.chaoxing.com/visit/courselistdata"

            payload = {
                "courseType": "1",  # 1表示学生课程
                "courseFolderId": "0",
                "query": "",
                "pageHeader": "",
                "single": "0",
                "superstarClass": "0",
            }

            headers = self.base_headers.copy()
            headers["Referer"] = "https://mooc2-ans.chaoxing.com/visit/interaction"
            headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"

            # 随机选择一个User-Agent
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
            ]
            headers["User-Agent"] = random.choice(user_agents)

            # 添加超时设置
            response = self.session.post(
                url, data=payload, headers=headers, verify=False, timeout=15
            )

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 查找课程列表
            course_items = soup.select(".learnCourse")

            # 如果没有找到课程，尝试其他选择器
            if not course_items:
                course_items = soup.select(".course-list .course")

            # 如果还是没有找到课程，尝试第三种选择器
            if not course_items:
                course_items = soup.select(".course-info-item")

            courses = []
            filtered_courses = 0
            for item in course_items:
                try:
                    # 检查课程是否已结束
                    course_status = item.select_one(".not-open-tip")
                    if (
                        not self.include_ended_courses
                        and course_status
                        and "课程已结束" in course_status.text
                    ):
                        filtered_courses += 1
                        continue  # 跳过已结束的课程

                    # 获取课程ID和班级ID
                    course_id_input = item.select_one(".courseId")
                    clazz_id_input = item.select_one(".clazzId")
                    person_id_input = item.select_one(".curPersonId")

                    if course_id_input and clazz_id_input and person_id_input:
                        course_id = course_id_input.get("value")
                        clazz_id = clazz_id_input.get("value")
                        person_id = person_id_input.get("value")

                        # 获取课程名称
                        course_name_span = item.select_one(".course-name")
                        course_name = (
                            course_name_span.text.strip()
                            if course_name_span
                            else "未知课程"
                        )

                        # 获取教师名称
                        teacher_name_p = item.select_one(".line2.color3")
                        teacher_name = (
                            teacher_name_p.text.strip()
                            if teacher_name_p
                            else "未知教师"
                        )

                        courses.append(
                            {
                                "course_name": course_name,
                                "teacher_name": teacher_name,
                                "course_id": course_id,
                                "clazz_id": clazz_id,
                                "person_id": person_id,
                            }
                        )
                except Exception:
                    pass

            # 如果没有找到任何课程，尝试替代URL
            if not courses:
                try:
                    alt_url = "https://mooc1.chaoxing.com/mooc2/student/course/list"
                    alt_response = self.session.get(
                        alt_url, headers=headers, verify=False, timeout=15
                    )
                    alt_soup = BeautifulSoup(alt_response.text, "html.parser")

                    # 查找课程列表
                    alt_course_items = alt_soup.select(".courseItem")

                    for item in alt_course_items:
                        try:
                            # 获取课程ID、班级ID和个人ID
                            course_url = item.select_one("a")
                            if course_url and course_url.get("href"):
                                href = course_url.get("href")
                                course_id_match = re.search(r"courseid=(\d+)", href)
                                clazz_id_match = re.search(r"clazzid=(\d+)", href)
                                cpi_match = re.search(r"cpi=(\d+)", href)

                                if course_id_match and clazz_id_match and cpi_match:
                                    course_id = course_id_match.group(1)
                                    clazz_id = clazz_id_match.group(1)
                                    person_id = cpi_match.group(1)

                                    # 获取课程名称
                                    course_name_elem = item.select_one(".course-name")
                                    course_name = (
                                        course_name_elem.text.strip()
                                        if course_name_elem
                                        else "未知课程"
                                    )

                                    # 获取教师名称
                                    teacher_name_elem = item.select_one(".teacher-name")
                                    teacher_name = (
                                        teacher_name_elem.text.strip()
                                        if teacher_name_elem
                                        else "未知教师"
                                    )

                                    courses.append(
                                        {
                                            "course_name": course_name,
                                            "teacher_name": teacher_name,
                                            "course_id": course_id,
                                            "clazz_id": clazz_id,
                                            "person_id": person_id,
                                        }
                                    )
                        except Exception:
                            pass
                except Exception:
                    pass

            return courses
        except requests.exceptions.Timeout:
            return []
        except requests.exceptions.ConnectionError:
            return []
        except Exception:
            return []

    def get_course_enc(self, course_id, clazz_id, person_id):
        """
        通过访问课程详情页面，获取作业列表的enc参数
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: enc参数字典，包含stuenc和enc两个参数
        """
        try:
            # 构建课程详情页链接
            course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"

            headers = self.base_headers.copy()

            # 访问课程详情页，可能会重定向
            response = self.session.get(
                course_url, headers=headers, verify=False, allow_redirects=True
            )

            # 解析页面内容，获取作业按钮的相关信息
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(response.text, "html.parser")

            # 首先尝试从页面中获取enc和stuenc参数
            enc = None
            stuenc = None

            # 从隐藏输入框中获取
            enc_input = soup.select_one("#enc")
            if enc_input and enc_input.get("value"):
                enc = enc_input.get("value")

            # 尝试获取workEnc参数，这可能是作业相关的enc参数
            work_enc_input = soup.select_one("#workEnc")
            if work_enc_input and work_enc_input.get("value"):
                work_enc = work_enc_input.get("value")
                # 如果找到workEnc参数，优先使用它作为enc参数
                enc = work_enc

            # 尝试获取其他可能的enc参数
            old_enc_input = soup.select_one("#oldenc")
            if old_enc_input and old_enc_input.get("value"):
                old_enc = old_enc_input.get("value")

            # 尝试获取stuenc参数（可能不存在）
            stuenc_input = soup.select_one("#stuenc")
            if stuenc_input and stuenc_input.get("value"):
                stuenc = stuenc_input.get("value")

            # 如果没有找到stuenc参数，可以尝试使用userId作为stuenc参数
            if not stuenc:
                user_id_input = soup.select_one("#userId")
                if user_id_input and user_id_input.get("value"):
                    user_id = user_id_input.get("value")

            # 如果已经获取到了enc参数，但没有stuenc参数，可以尝试使用enc作为stuenc
            if enc and not stuenc:
                stuenc = enc

            # 如果已经获取到了参数，可以直接返回
            if enc:  # 只要有enc参数就可以返回，stuenc可以为空
                return {"stuenc": stuenc or enc, "enc": enc}

            # 如果没有直接获取到参数，继续尝试通过点击作业按钮获取
            # 获取所有导航按钮，用于后续分析
            nav_buttons = soup.select(".nav-content ul li")

            # 首先尝试找到考试按钮(dataname='ks')
            exam_button_li = None
            for btn in nav_buttons:
                if btn.get("dataname") == "ks":
                    exam_button_li = btn
                    break

            if exam_button_li:
                # 获取考试按钮的链接
                exam_button = exam_button_li.select_one("a")
                if exam_button:
                    # 优先使用data-url属性
                    exam_url = exam_button.get("data-url")
                    if not exam_url:
                        exam_url = exam_button.get("href")

                    if exam_url:
                        # 如果是相对路径，转换为绝对路径
                        if exam_url.startswith("/"):
                            exam_url = f"https://mooc1.chaoxing.com{exam_url}"

                        # 如果是javascript:void(0)这样的链接，需要特殊处理
                        if exam_url.startswith("javascript:"):
                            # 尝试构建作业列表URL
                            exam_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}"

                        # 访问考试按钮链接(实际是作业页面)
                        exam_response = self.session.get(
                            exam_url, headers=headers, verify=False
                        )

                        # 从响应URL中提取参数
                        stuenc_match = re.search(r"stuenc=([^&]+)", exam_response.url)
                        enc_match = re.search(r"enc=([^&]+)", exam_response.url)

                        if stuenc_match:
                            stuenc = stuenc_match.group(1)

                        if enc_match:
                            enc = enc_match.group(1)

                        # 检查pageHeader参数，确认是否为作业页面(pageHeader=8)
                        pageheader_match = re.search(
                            r"pageHeader=(\d+)", exam_response.url
                        )

                        # 尝试直接访问作业列表页面
                        if stuenc:
                            work_list_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}&stuenc={stuenc}"
                            if enc:
                                work_list_url += f"&enc={enc}"

                            list_response = self.session.get(
                                work_list_url, headers=headers, verify=False
                            )

                            # 从响应URL中提取最终的enc参数
                            final_enc_match = re.search(
                                r"enc=([^&]+)", list_response.url
                            )
                            if final_enc_match:
                                enc = final_enc_match.group(1)

                            # 检查作业列表页面内容，查找可能包含enc参数的隐藏输入框
                            list_soup = BeautifulSoup(list_response.text, "html.parser")
                            enc_inputs = list_soup.select(
                                "input[type='hidden'][name='enc'], input[type='hidden'][id='enc']"
                            )
                            for input_elem in enc_inputs:
                                if input_elem.get("value"):
                                    enc = input_elem.get("value")
                                    break

                            if stuenc and enc:
                                return {"stuenc": stuenc, "enc": enc}

            # 如果没有找到考试按钮或者没有获取到参数，尝试查找作业按钮
            work_button_li = None
            for btn in nav_buttons:
                if btn.get("dataname") == "zy":
                    work_button_li = btn
                    break

            if work_button_li:
                # 获取作业按钮的链接
                work_button = work_button_li.select_one("a")
                if work_button:
                    # 优先使用data-url属性
                    work_url = work_button.get("data-url")
                    if not work_url:
                        work_url = work_button.get("href")

                    if work_url:
                        # 如果是相对路径，转换为绝对路径
                        if work_url.startswith("/"):
                            work_url = f"https://mooc1.chaoxing.com{work_url}"

                        # 如果是javascript:void(0)这样的链接，需要特殊处理
                        if work_url.startswith("javascript:"):
                            # 尝试构建作业列表URL
                            work_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}"

                        # 访问作业按钮链接
                        work_response = self.session.get(
                            work_url, headers=headers, verify=False
                        )

                        # 从响应URL中提取参数
                        stuenc_match = re.search(r"stuenc=([^&]+)", work_response.url)
                        enc_match = re.search(r"enc=([^&]+)", work_response.url)

                        if stuenc_match:
                            stuenc = stuenc_match.group(1)

                        if enc_match:
                            enc = enc_match.group(1)

                        if stuenc and enc:
                            # 尝试直接访问作业列表页面
                            work_list_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}&stuenc={stuenc}&enc={enc}"

                            list_response = self.session.get(
                                work_list_url, headers=headers, verify=False
                            )

                            # 从响应URL中提取最终的enc参数
                            final_enc_match = re.search(
                                r"enc=([^&]+)", list_response.url
                            )
                            if final_enc_match:
                                enc = final_enc_match.group(1)

                            return {"stuenc": stuenc, "enc": enc}

            # 如果所有方法都失败了，尝试直接构建作业列表URL
            work_list_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}"

            list_response = self.session.get(
                work_list_url, headers=headers, verify=False
            )

            stuenc_match = re.search(r"stuenc=([^&]+)", list_response.url)
            enc_match = re.search(r"enc=([^&]+)", list_response.url)

            if stuenc_match:
                stuenc = stuenc_match.group(1)

            if enc_match:
                enc = enc_match.group(1)

            if stuenc and enc:
                return {"stuenc": stuenc, "enc": enc}

            # 如果URL中没有参数，尝试从页面内容中提取
            list_soup = BeautifulSoup(list_response.text, "html.parser")

            # 检查是否有错误信息
            error_msg = list_soup.select_one(".word_null p")

            # 尝试从页面中的隐藏输入框中提取enc参数
            enc_inputs = list_soup.select(
                "input[type='hidden'][name='enc'], input[type='hidden'][id='enc']"
            )
            for input_elem in enc_inputs:
                if input_elem.get("value"):
                    enc = input_elem.get("value")
                    break

            # 如果所有方法都失败了，但至少有一个参数，尝试返回
            if stuenc or enc:
                return {"stuenc": stuenc or enc, "enc": enc or stuenc}

            return None
        except Exception as e:
            return None

    def get_homework_list(self, course_id, clazz_id, person_id):
        """
        获取作业列表，过滤掉已完成的作业
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: 作业列表
        """
        try:
            import urllib3
            from bs4 import BeautifulSoup
            import concurrent.futures

            # 禁用SSL警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # 生成时间戳
            timestamp = int(time.time() * 1000)

            # 首先检查是否有预先获取的enc参数
            enc = None
            stuenc = None
            if hasattr(self, "course_enc") and self.course_enc:
                enc = self.course_enc

            # 检查是否有预先获取的stuenc参数
            if hasattr(self, "stuenc") and self.stuenc:
                stuenc = self.stuenc

            # 如果没有获取到参数，尝试重新获取
            if not stuenc or not enc:
                # 尝试获取课程enc参数
                course_enc_info = self.get_course_enc(course_id, clazz_id, person_id)

                if course_enc_info:
                    stuenc = course_enc_info.get("stuenc")
                    enc = course_enc_info.get("enc")
                    # 保存获取到的参数，供后续使用
                    self.stuenc = stuenc
                    self.course_enc = enc
                    # 同时更新全局缓存
                    cache_key = f"{course_id}_{clazz_id}_{person_id}"
                    course_enc_cache[cache_key] = course_enc_info
                else:
                    # 尝试直接访问课程详情页面，从页面中提取参数
                    try:
                        course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"

                        response = self.session.get(
                            course_url,
                            headers=self.base_headers,
                            verify=False,
                            allow_redirects=True,
                            timeout=config["timeout"],
                        )

                        # 从响应URL中提取enc参数
                        enc_match = re.search(r"enc=([^&]+)", response.url)
                        if enc_match:
                            enc = enc_match.group(1)
                            self.course_enc = enc
                            stuenc = enc  # 默认使用相同的值
                            self.stuenc = stuenc
                            # 更新全局缓存
                            cache_key = f"{course_id}_{clazz_id}_{person_id}"
                            course_enc_cache[cache_key] = {"stuenc": stuenc, "enc": enc}
                        else:
                            # 尝试从页面中的隐藏输入框获取enc参数
                            soup = BeautifulSoup(response.text, "html.parser")
                            enc_input = soup.select_one("#enc")
                            if enc_input and enc_input.get("value"):
                                enc = enc_input.get("value")
                                self.course_enc = enc
                                stuenc = enc
                                self.stuenc = enc
                                # 更新全局缓存
                                cache_key = f"{course_id}_{clazz_id}_{person_id}"
                                course_enc_cache[cache_key] = {
                                    "stuenc": stuenc,
                                    "enc": enc,
                                }
                            else:
                                return []
                    except Exception:
                        return []

            # 随机User-Agent列表
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.83 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
            ]

            # 设置请求头
            list_headers = self.base_headers.copy()
            list_headers["Referer"] = (
                f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"
            )
            list_headers["User-Agent"] = random.choice(user_agents)

            # 存储所有作业
            all_homeworks = []

            # 获取第一页，同时检查总页数
            page_num = 1
            total_pages = 1

            # 构建第一页URL
            if stuenc and enc:
                url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}&stuenc={stuenc}&enc={enc}&pageNum={page_num}"
            else:
                url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}&pageNum={page_num}"

            # 发送请求
            response = self.session.get(
                url, headers=list_headers, verify=False, timeout=config["timeout"]
            )

            # 检查是否遇到反爬验证
            if "antispiderShowVerify" in response.url:
                # 如果遇到反爬验证，更换User-Agent并返回空列表
                list_headers["User-Agent"] = random.choice(user_agents)
                return []

            # 解析页面
            soup = BeautifulSoup(response.text, "html.parser")

            # 检查是否有错误提示
            error_msg = soup.select_one(".word_null p")
            if error_msg:
                # 尝试使用替代URL
                alt_url = f"https://mooc1.chaoxing.com/work/getAllWork?classId={clazz_id}&courseId={course_id}&isdisplaytable=2&mooc=1&ut=s&cpi={person_id}&t={timestamp}&pageNum={page_num}"
                response = self.session.get(
                    alt_url,
                    headers=list_headers,
                    verify=False,
                    timeout=config["timeout"],
                )
                soup = BeautifulSoup(response.text, "html.parser")
                error_msg = soup.select_one(".word_null p")
                if error_msg:
                    # 如果替代URL也失败，返回空列表
                    return []

            # 获取作业列表项
            homework_items = soup.select(".bottomList li")
            if not homework_items:
                return []

            # 解析第一页作业
            first_page_homeworks = self._parse_homework_items(
                homework_items, course_id, clazz_id, person_id
            )
            all_homeworks.extend(first_page_homeworks)

            # 获取总页数（仅在第一页时）
            # 查找分页信息
            page_script = soup.select_one("script:contains('pageNum:')")
            if page_script:
                page_match = re.search(r"pageNum:\s*(\d+)", page_script.text)
                if page_match:
                    total_pages = int(page_match.group(1))

            # 如果有多页，使用线程池并发获取剩余页面
            if total_pages > 1:
                # 创建线程安全的队列存储结果
                page_results = []

                # 使用线程池并发获取剩余页面，增加最大并发数
                max_page_workers = min(config["max_page_workers"], total_pages - 1)
                with concurrent.futures.ThreadPoolExecutor(
                    max_workers=max_page_workers
                ) as executor:
                    # 提交所有页面的获取任务
                    future_to_page = {}
                    for page in range(2, total_pages + 1):
                        future = executor.submit(
                            self._get_homework_page,
                            course_id,
                            clazz_id,
                            person_id,
                            page,
                            stuenc,
                            enc,
                            list_headers.copy(),  # 为每个请求创建独立的headers副本
                        )
                        future_to_page[future] = page

                    # 收集所有页面的结果
                    for future in concurrent.futures.as_completed(future_to_page):
                        page_homeworks = future.result()
                        if page_homeworks:
                            page_results.extend(page_homeworks)

                # 将所有页面的作业合并
                all_homeworks.extend(page_results)

            return all_homeworks
        except Exception:
            return []

    def _parse_homework_items(self, homework_items, course_id, clazz_id, person_id):
        """
        解析作业列表项
        :param homework_items: 作业列表项
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: 作业列表
        """
        homeworks = []

        for item in homework_items:
            try:
                # 获取作业状态
                status_elem = item.select_one(".status")
                status = status_elem.text.strip() if status_elem else "未知状态"

                # 过滤掉已完成的作业（如果需要）
                if not self.include_finished and status == "已完成":
                    continue

                # 获取作业标题
                title_elem = item.select_one(".overHidden2")
                title = title_elem.text.strip() if title_elem else "未知作业"

                # 获取作业链接
                link = item.get("data") or ""

                # 获取作业ID和答案ID
                work_id = None
                answer_id = None
                if link:
                    work_id_match = re.search(r"workId=(\d+)", link)
                    if work_id_match:
                        work_id = work_id_match.group(1)

                    answer_id_match = re.search(r"answerId=(\d+)", link)
                    if answer_id_match:
                        answer_id = answer_id_match.group(1)

                # 从链接中提取enc参数
                enc_param = ""
                if link:
                    enc_match = re.search(r"enc=([^&]+)", link)
                    if enc_match:
                        enc_param = enc_match.group(1)

                # 构建作业信息
                homework_info = {
                    "title": title,
                    "status": status,
                    "link": link,
                    "work_id": work_id,
                    "answer_id": answer_id,
                    "course_id": course_id,
                    "clazz_id": clazz_id,
                    "cpi": person_id,
                    "enc": enc_param,
                }

                homeworks.append(homework_info)
            except Exception:
                pass

        return homeworks

    def _get_homework_page(
        self, course_id, clazz_id, person_id, page_num, stuenc, enc, headers
    ):
        """
        获取指定页码的作业列表
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :param page_num: 页码
        :param stuenc: stuenc参数
        :param enc: enc参数
        :param headers: 请求头
        :return: 作业列表
        """
        try:
            from bs4 import BeautifulSoup

            # 生成时间戳
            timestamp = int(time.time() * 1000)

            # 构建URL
            if stuenc and enc:
                url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}&stuenc={stuenc}&enc={enc}&pageNum={page_num}"
            else:
                url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}&pageNum={page_num}"

            # 发送请求
            response = self.session.get(
                url, headers=headers, verify=False, timeout=config["timeout"]
            )

            # 检查是否遇到反爬验证
            if "antispiderShowVerify" in response.url:
                return []

            # 解析页面
            soup = BeautifulSoup(response.text, "html.parser")

            # 获取作业列表项
            homework_items = soup.select(".bottomList li")
            if not homework_items:
                # 尝试使用替代URL
                alt_url = f"https://mooc1.chaoxing.com/work/getAllWork?classId={clazz_id}&courseId={course_id}&isdisplaytable=2&mooc=1&ut=s&cpi={person_id}&t={timestamp}&pageNum={page_num}"
                response = self.session.get(
                    alt_url, headers=headers, verify=False, timeout=config["timeout"]
                )
                soup = BeautifulSoup(response.text, "html.parser")
                homework_items = soup.select(".bottomList li")

            # 解析作业列表项
            return self._parse_homework_items(
                homework_items, course_id, clazz_id, person_id
            )
        except Exception:
            return []


# 获取作业列表，支持重试
def get_homework_list_with_retry(
    manager, course_id, clazz_id, person_id, max_retries=3
):
    """
    获取作业列表，支持重试
    :param manager: ChaoxingHomeworkManager实例
    :param course_id: 课程ID
    :param clazz_id: 班级ID
    :param person_id: 个人ID
    :param max_retries: 最大重试次数
    :return: 作业列表
    """
    # 随机User-Agent列表
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
    ]

    # 尝试获取作业列表，支持重试
    all_homeworks = []

    # 尝试不同的URL模式
    url_patterns = [
        # 标准URL模式
        lambda: f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}",
        # 替代URL模式1
        lambda: f"https://mooc1.chaoxing.com/work/getAllWork?classId={clazz_id}&courseId={course_id}&isdisplaytable=2&mooc=1&ut=s&cpi={person_id}&t={int(time.time() * 1000)}",
        # 替代URL模式2
        lambda: f"https://mooc1.chaoxing.com/mooc2/work/list-v2?courseId={course_id}&classId={clazz_id}&cpi={person_id}&t={int(time.time() * 1000)}",
    ]

    for retry in range(max_retries):
        try:
            # 更换User-Agent，降低被反爬的可能性
            if hasattr(manager, "base_headers"):
                manager.base_headers["User-Agent"] = random.choice(user_agents)

            # 尝试使用manager的方法获取
            homeworks = manager.get_homework_list(course_id, clazz_id, person_id)
            if homeworks:
                all_homeworks.extend(homeworks)
                # 如果成功获取到作业，不需要继续尝试其他URL模式
                break

            # 如果没有获取到作业，尝试使用不同的URL模式直接获取
            if retry < max_retries - 1:
                # 尝试重新获取课程enc参数
                course_enc_info = manager.get_course_enc(course_id, clazz_id, person_id)
                if course_enc_info:
                    manager.course_enc = course_enc_info.get("enc")
                    manager.stuenc = course_enc_info.get("stuenc")

                # 使用不同的URL模式
                for url_pattern in url_patterns:
                    try:
                        from bs4 import BeautifulSoup

                        # 构建URL
                        url = url_pattern()

                        # 设置请求头
                        headers = manager.base_headers.copy()
                        headers["User-Agent"] = random.choice(user_agents)

                        # 发送GET请求
                        response = manager.session.get(
                            url, headers=headers, verify=False, timeout=10
                        )

                        # 检查是否遇到反爬验证
                        if "antispiderShowVerify" in response.url:
                            continue

                        # 解析响应内容
                        soup = BeautifulSoup(response.text, "html.parser")

                        # 获取作业列表项
                        homework_items = soup.select(".bottomList li")
                        if not homework_items:
                            continue

                        # 解析作业列表
                        pattern_homeworks = []
                        for item in homework_items:
                            try:
                                # 获取作业状态
                                status_elem = item.select_one(".status")
                                status = (
                                    status_elem.text.strip()
                                    if status_elem
                                    else "未知状态"
                                )

                                # 过滤掉已完成的作业（如果需要）
                                if not manager.include_finished and status == "已完成":
                                    continue

                                # 获取作业标题
                                title_elem = item.select_one(".overHidden2")
                                title = (
                                    title_elem.text.strip()
                                    if title_elem
                                    else "未知作业"
                                )

                                # 获取作业链接
                                link = item.get("data") or ""

                                # 获取作业ID和答案ID
                                work_id = None
                                answer_id = None
                                if link:
                                    work_id_match = re.search(r"workId=(\d+)", link)
                                    if work_id_match:
                                        work_id = work_id_match.group(1)

                                    answer_id_match = re.search(r"answerId=(\d+)", link)
                                    if answer_id_match:
                                        answer_id = answer_id_match.group(1)

                                # 从链接中提取enc参数
                                enc = ""
                                if link:
                                    enc_match = re.search(r"enc=([^&]+)", link)
                                    if enc_match:
                                        enc = enc_match.group(1)

                                # 构建作业信息
                                homework_info = {
                                    "title": title,
                                    "status": status,
                                    "link": link,
                                    "work_id": work_id,
                                    "answer_id": answer_id,
                                    "course_id": course_id,
                                    "clazz_id": clazz_id,
                                    "cpi": person_id,
                                    "enc": enc,
                                }

                                pattern_homeworks.append(homework_info)
                            except Exception:
                                pass

                        # 如果成功获取到作业，添加到结果
                        if pattern_homeworks:
                            all_homeworks.extend(pattern_homeworks)
                            # 找到有效的URL模式后，不再尝试其他模式
                            break
                    except Exception:
                        continue

                # 如果已经获取到作业，不再继续重试
                if all_homeworks:
                    break

                # 添加随机延迟，避免触发反爬
                time.sleep(random.uniform(0.5, 1.5))
        except Exception as e:
            if retry < max_retries - 1:
                # 指数退避策略，但减小延迟时间
                delay = (1.5**retry) * random.uniform(0.5, 1.5)
                time.sleep(delay)

    # 返回所有获取到的作业
    return all_homeworks


def process_course_homework(manager, course, result_queue):
    """
    处理单个课程的作业列表
    :param manager: ChaoxingHomeworkManager实例
    :param course: 课程信息
    :param result_queue: 结果队列
    """
    try:
        course_name = course["course_name"]
        teacher_name = course["teacher_name"]
        course_id = course["course_id"]
        clazz_id = course["clazz_id"]
        person_id = course["person_id"]

        # 创建课程专用的session，避免会话冲突
        course_manager = ChaoxingHomeworkManager(manager.username, manager.password)
        course_manager.session = requests.Session()
        course_manager.session.cookies = manager.session.cookies
        course_manager.base_headers = manager.base_headers.copy()
        course_manager.include_finished = manager.include_finished
        course_manager.include_ended_courses = manager.include_ended_courses

        # 设置更短的超时时间，避免单个课程阻塞过久
        course_manager.session.timeout = config["timeout"]

        # 设置连接池参数，提高并发性能
        course_manager.session.mount("http://", adapter)
        course_manager.session.mount("https://", adapter)

        # 获取课程的enc参数，使用缓存机制
        cache_key = f"{course_id}_{clazz_id}_{person_id}"

        # 使用全局缓存
        course_enc_info = None
        if cache_key in course_enc_cache:
            course_enc_info = course_enc_cache[cache_key]
            course_manager.course_enc = course_enc_info.get("enc")
            course_manager.stuenc = course_enc_info.get("stuenc")
        else:
            # 获取课程enc参数
            course_enc_info = course_manager.get_course_enc(
                course_id, clazz_id, person_id
            )
            if course_enc_info:
                # 设置manager对象的属性
                course_manager.course_enc = course_enc_info.get("enc")
                course_manager.stuenc = course_enc_info.get("stuenc")
                # 缓存enc参数
                course_enc_cache[cache_key] = course_enc_info

        # 使用增强版的作业列表获取函数
        homeworks = get_homework_list_with_retry(
            course_manager, course_id, clazz_id, person_id, max_retries=3
        )

        # 将结果放入队列
        result_queue.put((f"{course_name} ({teacher_name})", homeworks))

    except Exception as e:
        # 记录错误但不中断流程
        print(f"处理课程 '{course.get('course_name', '未知课程')}' 时出错: {e}")
        # 确保即使出错也添加一个空结果
        result_queue.put(
            (
                f"{course.get('course_name', '未知课程')} ({course.get('teacher_name', '未知教师')})",
                [],
            )
        )


# 获取所有课程的作业列表
def get_all_homeworks(
    username, password, include_finished=False, include_ended_courses=False
):
    # 加密用户名和密码
    cipher = AESCipher()
    encrypted_username = cipher.encrypt(username)
    encrypted_password = cipher.encrypt(password)

    # 创建作业管理器
    manager = ChaoxingHomeworkManager(encrypted_username, encrypted_password)
    manager.include_finished = include_finished
    manager.include_ended_courses = include_ended_courses

    # 登录
    if not manager.login():
        return {"error": "登录失败，请检查账号密码"}

    # 获取课程列表
    courses = manager.get_course_list()
    if not courses:
        return {"error": "未找到课程"}

    # 使用线程池并发获取作业列表
    result = {}
    result_queue = Queue()

    # 增加线程池大小，提高并发性能
    max_workers = min(
        config["max_workers"], len(courses)
    )  # 最多使用配置的线程数，但不超过课程数量

    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_course = {
            executor.submit(
                process_course_homework, manager, course, result_queue
            ): course
            for course in courses
        }

        # 等待所有任务完成
        for future in concurrent.futures.as_completed(future_to_course):
            course = future_to_course[future]
            try:
                # 获取任务结果（如果有异常会在这里抛出）
                future.result()
            except Exception as e:
                # 记录错误但继续处理其他课程
                course_name = course.get("course_name", "未知课程")
                teacher_name = course.get("teacher_name", "未知教师")
                print(f"处理课程 '{course_name}' 时出错: {e}")

    # 从结果队列中获取所有作业
    while not result_queue.empty():
        course_name, homeworks = result_queue.get()
        if homeworks:  # 只添加有作业的课程
            result[course_name] = homeworks

    return result


# API路由：获取作业列表
@app.route("/get_homeworks", methods=["POST"])
def api_get_homeworks():
    # 获取请求参数
    data = request.json
    if not data:
        return jsonify({"error": "请求参数错误，需要JSON格式数据"}), 400

    # 获取必要参数
    username = data.get("username")
    password = data.get("password")
    include_finished = data.get("include_finished", False)
    include_ended_courses = data.get("include_ended_courses", False)

    # 验证参数
    if not username or not password:
        return jsonify({"error": "账号和密码不能为空"}), 400

    try:
        # 设置全局超时
        socket_timeout = data.get(
            "timeout", config["timeout"]
        )  # 使用全局配置的超时时间作为默认值
        import socket

        socket.setdefaulttimeout(socket_timeout)

        # 获取作业列表
        result = get_all_homeworks(
            username, password, include_finished, include_ended_courses
        )

        # 处理结果
        if "error" in result:
            error_msg = result["error"]
            return jsonify({"error": error_msg}), 400

        # 转换为简化的输出格式
        simplified_result = []
        total_homeworks = 0

        for course_name, homeworks in result.items():
            for homework in homeworks:
                # 提取必要信息
                title = homework.get("title", "未知作业")
                status = homework.get("status", "未知状态")
                course_id = homework.get("course_id", "")
                clazz_id = homework.get("clazz_id", "")
                cpi = homework.get("cpi", "")
                work_id = homework.get("work_id", "")
                answer_id = homework.get("answer_id", "")
                enc = homework.get("enc", "")

                # 构建作业参数
                homework_params = (
                    f"{course_id}|{clazz_id}|{cpi}|{work_id}|{answer_id}|{enc}"
                )

                # 添加到结果列表
                simplified_result.append(
                    {
                        "title": f"{course_name} - {title}",
                        "status": status,
                        "params": homework_params,
                    }
                )
                total_homeworks += 1

        # 返回结果，包含总数统计
        return (
            jsonify(
                {
                    "success": True,
                    "count": total_homeworks,
                    "courses_count": len(result),
                    "homeworks": simplified_result,
                }
            ),
            200,
        )

    except Exception as e:
        error_msg = f"服务器内部错误: {str(e)}"
        return jsonify({"error": error_msg}), 500


# API路由：批量获取多个账号的作业列表
@app.route("/batch_get_homeworks", methods=["POST"])
def api_batch_get_homeworks():
    # 获取请求参数
    data = request.json
    if not data:
        return jsonify({"error": "请求参数错误，需要JSON格式数据"}), 400

    # 获取账号列表
    accounts = data.get("accounts")
    if not accounts or not isinstance(accounts, list):
        return jsonify({"error": "accounts参数必须是包含账号信息的数组"}), 400

    # 获取全局参数
    include_finished = data.get("include_finished", False)
    include_ended_courses = data.get("include_ended_courses", False)
    socket_timeout = data.get("timeout", config["timeout"])
    max_concurrent = data.get("max_concurrent", config["max_connections"])

    # 设置全局超时
    import socket

    socket.setdefaulttimeout(socket_timeout)

    # 创建结果字典
    results = {}
    errors = {}

    # 使用全局线程池并发处理所有账号
    futures = {}

    # 限制最大并发数
    actual_concurrent = min(max_concurrent, len(accounts), config["max_connections"])

    # 使用自定义线程池处理批量请求
    with ThreadPoolExecutor(max_workers=actual_concurrent) as batch_executor:
        # 提交所有账号的任务
        for account in accounts:
            # 验证账号参数
            username = account.get("username")
            password = account.get("password")

            if not username or not password:
                errors[username or f"未知账号_{len(errors)}"] = "账号和密码不能为空"
                continue

            # 提交任务到线程池
            future = batch_executor.submit(
                get_all_homeworks,
                username,
                password,
                include_finished,
                include_ended_courses,
            )
            futures[future] = username

        # 收集所有任务的结果
        for future in concurrent.futures.as_completed(futures):
            username = futures[future]
            try:
                result = future.result()
                if "error" in result:
                    errors[username] = result["error"]
                else:
                    # 转换为简化的输出格式
                    simplified_result = []
                    total_homeworks = 0

                    for course_name, homeworks in result.items():
                        for homework in homeworks:
                            # 提取必要信息
                            title = homework.get("title", "未知作业")
                            status = homework.get("status", "未知状态")
                            course_id = homework.get("course_id", "")
                            clazz_id = homework.get("clazz_id", "")
                            cpi = homework.get("cpi", "")
                            work_id = homework.get("work_id", "")
                            answer_id = homework.get("answer_id", "")
                            enc = homework.get("enc", "")

                            # 构建作业参数
                            homework_params = f"{course_id}|{clazz_id}|{cpi}|{work_id}|{answer_id}|{enc}"

                            # 添加到结果列表
                            simplified_result.append(
                                {
                                    "title": f"{course_name} - {title}",
                                    "status": status,
                                    "params": homework_params,
                                }
                            )
                            total_homeworks += 1

                    # 保存结果
                    results[username] = {
                        "success": True,
                        "count": total_homeworks,
                        "courses_count": len(result),
                        "homeworks": simplified_result,
                    }
            except Exception as e:
                errors[username] = f"处理账号时出错: {str(e)}"

    # 返回结果
    return (
        jsonify(
            {
                "success": True,
                "total_accounts": len(accounts),
                "successful_accounts": len(results),
                "failed_accounts": len(errors),
                "results": results,
                "errors": errors,
            }
        ),
        200,
    )


# 主页
@app.route("/")
def index():
    return (
        """
    <html>
        <head>
            <title>超星学习通作业列表API服务</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
                h1 { color: #333; }
                pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
                .endpoint { background-color: #e0f0ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>超星学习通作业列表API服务</h1>
            <p>此服务提供超星学习通作业列表获取API。</p>
            
            <div class="endpoint">
                <h2>获取作业列表</h2>
                <p><strong>端点:</strong> /get_homeworks</p>
                <p><strong>方法:</strong> POST</p>
                <p><strong>请求格式:</strong></p>
                <pre>
{
    "username": "你的超星账号",
    "password": "你的超星密码",
    "include_finished": false,
    "include_ended_courses": false,
    "timeout": 15
}
                </pre>
                <p><strong>响应格式:</strong></p>
                <pre>
{
    "success": true,
    "count": 10,
    "courses_count": 5,
    "homeworks": [
        {
            "title": "课程名称 (教师名称) - 作业标题",
            "status": "作业状态",
            "params": "课程ID|班级ID|个人ID|作业ID|答案ID|enc参数"
        },
        ...
    ]
}
                </pre>
            </div>
            
            <div class="endpoint">
                <h2>批量获取作业列表</h2>
                <p><strong>端点:</strong> /batch_get_homeworks</p>
                <p><strong>方法:</strong> POST</p>
                <p><strong>请求格式:</strong></p>
                <pre>
{
    "accounts": [
        {
            "username": "超星账号1",
            "password": "超星密码1"
        },
        {
            "username": "超星账号2",
            "password": "超星密码2"
        },
        ...
    ],
    "include_finished": false,
    "include_ended_courses": false,
    "timeout": 15,
    "max_concurrent": 50
}
                </pre>
                <p><strong>响应格式:</strong></p>
                <pre>
{
    "success": true,
    "total_accounts": 2,
    "successful_accounts": 2,
    "failed_accounts": 0,
    "results": {
        "超星账号1": {
            "success": true,
            "count": 10,
            "courses_count": 5,
            "homeworks": [...]
        },
        "超星账号2": {
            "success": true,
            "count": 8,
            "courses_count": 4,
            "homeworks": [...]
        }
    },
    "errors": {}
}
                </pre>
            </div>
            
            <div class="endpoint">
                <h2>服务器配置</h2>
                <p>当前服务器配置:</p>
                <pre>
最大课程线程池大小: """
        + str(config["max_workers"])
        + """
最大页面线程池大小: """
        + str(config["max_page_workers"])
        + """
最大并发连接数: """
        + str(config["max_connections"])
        + """
默认超时时间: """
        + str(config["timeout"])
        + """秒
                </pre>
                <p>可以通过命令行参数调整配置，例如:</p>
                <pre>python chaoxing_api_server.py --threads 20 --page-threads 10 --timeout 15 --max-connections 100</pre>
            </div>
        </body>
    </html>
    """
    )


# 启动服务器
if __name__ == "__main__":
    import argparse

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="超星学习通作业列表API服务")
    parser.add_argument(
        "--host", default="0.0.0.0", help="服务器监听地址 (默认: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", type=int, default=666, help="服务器监听端口 (默认: 666)"
    )
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument(
        "--threads", type=int, default=20, help="课程线程池大小 (默认: 20)"
    )
    parser.add_argument(
        "--page-threads", type=int, default=10, help="页面线程池大小 (默认: 10)"
    )
    parser.add_argument(
        "--timeout", type=int, default=15, help="请求超时时间(秒) (默认: 15)"
    )
    parser.add_argument(
        "--max-connections", type=int, default=100, help="最大并发连接数 (默认: 100)"
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 更新全局配置
    config["max_workers"] = args.threads
    config["max_page_workers"] = args.page_threads
    config["timeout"] = args.timeout
    config["max_connections"] = args.max_connections

    # 重新创建全局线程池
    global_executor = ThreadPoolExecutor(max_workers=config["max_connections"])

    # 设置全局超时
    import socket

    socket.setdefaulttimeout(args.timeout)

    # 安装依赖
    try:
        import bs4
    except ImportError:
        import os

        print("正在安装依赖库...")
        os.system("pip install beautifulsoup4 pyaes flask requests")
        print("依赖库安装完成")

    # 配置服务器
    from werkzeug.serving import run_simple

    print(f"启动超星学习通作业列表API服务...")
    print(f"监听地址: {args.host}:{args.port}")
    print(f"调试模式: {'启用' if args.debug else '禁用'}")
    print(f"课程线程池大小: {config['max_workers']}")
    print(f"页面线程池大小: {config['max_page_workers']}")
    print(f"最大并发连接数: {config['max_connections']}")
    print(f"请求超时: {config['timeout']}秒")
    print(f"API地址: http://{args.host}:{args.port}/get_homeworks")
    print(f"批量API地址: http://{args.host}:{args.port}/batch_get_homeworks")
    print("按Ctrl+C停止服务")

    # 启动Flask应用
    if args.debug:
        app.run(host=args.host, port=args.port, debug=True)
    else:
        # 生产模式，使用werkzeug的run_simple，支持多线程
        run_simple(
            args.host,
            args.port,
            app,
            threaded=True,
            processes=1,
            use_reloader=False,
            use_debugger=False,
        )
