---
description: 
globs: 
alwaysApply: false
---
# 错误处理规则

## 异常层次结构

项目中应该定义清晰的异常层次结构，所有自定义异常都应该继承自基础异常类。

```python
# api/exceptions.py
class ChaoxingBaseError(Exception):
    """超星学习通自动化工具基础异常类"""
    pass

class LoginError(ChaoxingBaseError):
    """登录失败异常"""
    pass

class InputFormatError(ChaoxingBaseError):
    """输入格式错误异常"""
    pass

class MaxRollBackExceeded(ChaoxingBaseError):
    """超出最大回滚次数异常"""
    pass
```

## 异常捕获原则

### 精确捕获
捕获特定的异常类型，而不是捕获所有异常。除非在顶层异常处理中，否则避免使用裸露的 `except:` 或 `except Exception:`。

```python
# 推荐
try:
    response = _session.post(_url, headers=_headers, data=_data)
    response.raise_for_status()
except requests.exceptions.HTTPError as e:
    logger.error(f"HTTP请求错误: {e}")
except requests.exceptions.ConnectionError as e:
    logger.error(f"连接错误: {e}")
except requests.exceptions.Timeout as e:
    logger.error(f"请求超时: {e}")

# 不推荐
try:
    response = _session.post(_url, headers=_headers, data=_data)
    response.raise_for_status()
except Exception as e:
    logger.error(f"请求错误: {e}")
```

### 异常处理范围
异常处理的范围应该尽可能小，只包含可能引发异常的代码。

```python
# 推荐
try:
    data = json.loads(response.text)
except json.JSONDecodeError:
    logger.error("JSON解析错误")
    data = {}

process_data(data)

# 不推荐
try:
    data = json.loads(response.text)
    process_data(data)
except json.JSONDecodeError:
    logger.error("JSON解析错误")
except OtherError:
    logger.error("处理数据时出错")
```

## 重试机制

对于网络请求等可能暂时失败的操作，应该实现重试机制。

```python
def with_retry(max_retries=3, delay=1):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except (requests.exceptions.RequestException, ConnectionError) as e:
                    retries += 1
                    if retries >= max_retries:
                        logger.error(f"已达到最大重试次数 {max_retries}，操作失败: {e}")
                        raise MaxRetryExceeded(f"已达到最大重试次数 {max_retries}") from e
                    logger.warning(f"操作失败，正在进行第 {retries} 次重试: {e}")
                    time.sleep(delay * retries)  # 指数退避
        return wrapper
    return decorator
```

## 日志记录

异常处理应该与日志记录结合，确保所有异常都被适当记录。

```python
try:
    # 执行操作
    pass
except SomeSpecificError as e:
    logger.error(f"发生错误: {e}")
    # 可能的恢复操作
    raise  # 如果需要向上传播异常
```

## 用户友好的错误信息

向用户展示的错误信息应该友好且有帮助，而不是直接显示技术细节或堆栈跟踪。

```python
try:
    # 执行操作
    pass
except DatabaseError:
    logger.exception("数据库操作失败")  # 记录详细的技术信息
    print("无法连接到服务器，请稍后再试")  # 向用户显示友好信息
```

## 资源清理

使用 `try...finally` 或上下文管理器（`with` 语句）确保资源正确清理。

```python
# 使用 with 语句（推荐）
with open("file.txt", "r") as f:
    content = f.read()

# 使用 try...finally
f = open("file.txt", "r")
try:
    content = f.read()
finally:
    f.close()
```

## 异常转换

在适当的情况下，将底层库的异常转换为应用程序特定的异常，以提供更有意义的上下文。

```python
try:
    response = requests.get(url)
    response.raise_for_status()
except requests.exceptions.HTTPError as e:
    if e.response.status_code == 401:
        raise LoginError("认证失败，请检查用户名和密码") from e
    elif e.response.status_code == 404:
        raise ResourceNotFoundError(f"找不到资源: {url}") from e
    else:
        raise NetworkError(f"HTTP错误: {e}") from e
```

