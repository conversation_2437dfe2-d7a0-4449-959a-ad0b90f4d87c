---
description: 
globs: 
alwaysApply: false
---
# 导入顺序规则

## 标准库优先
标准库导入应该始终放在第一位，然后是第三方库，最后是本地模块。

```python
# 标准库导入
import os
import sys
import time
import random
import configparser

# 第三方库导入
import requests
import httpx
from openai import OpenAI

# 本地模块导入
from api.logger import logger
from api.base import Chaoxing, Account
from api.answer import Tiku
from api.notification import Notification
```

## 分组导入
每个导入组之间应该有一个空行分隔，并且按照字母顺序排序。

## 相对导入
对于相对导入，应该使用明确的相对导入语法，避免隐式相对导入。

```python
# 推荐
from .module import Class
from ..subpackage import function

# 不推荐
from module import Class
```

## 通配符导入
避免使用通配符导入（`from module import *`），除非在特定情况下（如在`__init__.py`文件中公开API）。

## 导入别名
当导入的模块名称过长或可能与其他模块冲突时，可以使用别名：

```python
import numpy as np
import pandas as pd
```

## 单行多导入
对于同一个模块的多个导入，可以在一行中完成：

```python
from api.exceptions import LoginError, InputFormatError, MaxRollBackExceeded
```

## 避免循环导入
设计模块时应避免循环导入，必要时使用延迟导入（在函数内部导入）。

