---
description:
globs:
alwaysApply: false
---
# Python Import Order Rules

## 导入顺序规则

Python文件中的导入语句应按以下顺序排列：

1. 标准库导入
2. 相关第三方导入
3. 本地应用/库特定导入

## 具体规则

```python
# 1. 标准库导入
import os
import sys
import time
import re
import json
import logging
import urllib3

# 2. 相关第三方导入
import requests
from bs4 import BeautifulSoup

# 3. 本地应用/库特定导入
from batch_fill_homework import get_course_enc
```

## 导入格式规则

- 使用绝对导入而非相对导入
- 每个导入应该独占一行
- 导入分组之间应有空行
- 避免使用通配符导入（例如 `from module import *`）
- 导入应放在文件顶部，在模块注释和文档字符串之后，在全局变量和常量之前

## 例外情况

某些情况下，导入可能需要放在函数或类内部：
- 避免循环导入
- 减少启动时间
- 导入的模块只在特定条件下使用

这些情况下，导入语句应该放在函数的开始处，并附带注释说明原因。
