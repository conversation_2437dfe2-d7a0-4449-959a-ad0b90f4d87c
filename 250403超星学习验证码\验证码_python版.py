"""
Python实现超星学习通滑块验证码自动识别与破解
纯Python版本，不依赖Node.js环境
"""
# 导入数据请求模块
import requests
# 导入时间模块
import time
# 导入正则表达式模块
import re
# 导入验证码识别模块
import ddddocr
# 导入加密模块
import hashlib
# 导入随机模块
import random
# 导入JSON模块
import json

# 模拟JS中的随机ID生成函数
def generate_random_id():
    chars = "0123456789abcdef"
    result = [''] * 36
    for i in range(36):
        if i in [8, 13, 18, 23]:
            result[i] = '-'
        elif i == 14:
            result[i] = '4'
        elif i == 19:
            result[i] = chars[random.randint(0, 7) | 8]
        else:
            result[i] = chars[random.randint(0, 15)]
    return ''.join(result)

# 模拟JS中的GetSign函数
def get_sign(timestamp):
    captcha_id = 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv'
    random_id = generate_random_id()
    
    # 生成captchaKey
    captcha_key = hashlib.md5(f"{timestamp}{random_id}".encode()).hexdigest()
    
    # 生成token
    token_str = f"{timestamp}{captcha_id}slide{captcha_key}"
    token = f"{hashlib.md5(token_str.encode()).hexdigest()}:{int(timestamp) + 300000}"
    
    # 生成IV
    iv = hashlib.md5(f"{captcha_id}slide{int(time.time() * 1000)}{generate_random_id()}".encode()).hexdigest()
    
    return {
        'captchaKey': captcha_key,
        'token': token,
        'IV': iv
    }

def main():
    try:
        # 模拟浏览器
        headers = {
            'referer': 'https://v8.chaoxing.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'
        }
        
        # 请求网址
        url = 'https://captcha.chaoxing.com/captcha/get/verification/image'
        
        # 获取当前时间戳
        timestamp = int(time.time() * 1000)
        
        # 获取加密参数
        sign_data = get_sign(timestamp)
        print("生成的签名数据:", sign_data)
        
        # 查询参数
        params = {
            'callback': 'cx_captcha_function',
            'captchaId': 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv',
            'type': 'slide',
            'version': '1.1.20',
            'captchaKey': sign_data['captchaKey'],
            'token': sign_data['token'],
            'referer': 'https://v8.chaoxing.com/',
            'iv': sign_data['IV'],
            '_': timestamp,
        }
        
        # 发送请求获取验证码图片
        response = requests.get(url=url, params=params, headers=headers)
        print("验证码接口响应:", response.text[:200] + "..." if len(response.text) > 200 else response.text)
        
        # 提取验证图片 shadeImage(缺口图) cutoutImage(滑块图)
        match = re.findall('"shadeImage":"(.*?)","cutoutImage":"(.*?)"', response.text)
        if not match:
            print("未找到验证码图片URL")
            return
            
        shadeImage, cutoutImage = match[0]
        
        # 提取token
        token_match = re.findall('"token":"(.*?)"', response.text)
        if not token_match:
            print("未找到token")
            return
            
        token = token_match[0]
        
        # 获取缺口图片内容
        print("下载背景图片...")
        shadeImage_content = requests.get(url=shadeImage, headers=headers).content
        with open('bg.jpg', 'wb') as f:
            f.write(shadeImage_content)
            
        # 获取滑块图片内容
        print("下载滑块图片...")
        cutoutImage_content = requests.get(url=cutoutImage, headers=headers).content
        with open('fg.jpg', 'wb') as f:
            f.write(cutoutImage_content)
            
        # 实例化对象
        print("开始识别滑块位置...")
        det = ddddocr.DdddOcr(det=False, ocr=True)
        
        # 验证图片识别
        res = det.slide_match(shadeImage_content, cutoutImage_content, simple_target=True)
        
        # 获取x轴距离
        x = res['target'][0]
        print(f"识别结果: x轴距离 = {x}")
        
        # 验证链接
        link = 'https://captcha.chaoxing.com/captcha/check/verification/result'
        current_time = int(time.time() * 1000)
        
        # 请求参数
        link_data = {
            'callback': 'cx_captcha_function',
            'captchaId': 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv',
            'type': 'slide',
            'token': token,
            'textClickArr': f'[{{"x":{x}}}]',
            'coordinate': '[]',
            'runEnv': '10',
            'version': '1.1.20',
            't': 'a',
            'iv': 'dcac328825997752b5099050a3b4c9ea',
            '_': current_time,
        }
        
        print("提交验证结果...")
        print("请求参数:", link_data)
        
        # 发送验证请求
        link_response = requests.get(url=link, params=link_data, headers=headers)
        print("验证结果状态码:", link_response.status_code)
        print("验证结果响应:", link_response.text)
        
        # 判断验证是否成功
        if "success" in link_response.text and "true" in link_response.text:
            print("验证码识别成功!")
        else:
            print("验证码识别失败，请检查参数或重试")
            
    except Exception as e:
        print(f"执行出错: {e}")

if __name__ == "__main__":
    main() 