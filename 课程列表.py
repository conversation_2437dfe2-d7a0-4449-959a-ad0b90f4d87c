import requests

url = "https://mooc2-ans.chaoxing.com/mooc2-ans/visit/courselistdata"

payload = {
  'courseType': "1",
  'courseFolderId': "0",
  'query': "",
  'pageHeader': "-1",
  'single': "0",
  'superstarClass': "0"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "text/html, */*; q=0.01",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'sec-ch-ua-platform': "\"Windows\"",
  'X-Requested-With': "XMLHttpRequest",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'Origin': "https://mooc2-ans.chaoxing.com",
  'Sec-Fetch-Site': "same-origin",
  'Sec-Fetch-Mode': "cors",
  'Sec-Fetch-Dest': "empty",
  'Referer': "https://mooc2-ans.chaoxing.com/visit/interaction?s=98f8277095f0aa78054e22abbb7a5178",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "fid=2790; _uid=221880762; _d=1751571984089; UID=221880762; vc3=N7PYn1ccOk95R1RGBk7kV4hsDTUjThs7NMMMLLWoA7IU5EqHuXWw5EpuvQdCF2s%2Bep%2B4pa0E5%2BAT4%2BdarArNtj%2F%2F1dSOoKeC23eRvEDnYL5n8kGTVaTVkTEph2wwgXRFi%2BUpBSrc5Ic7Em%2FQhtOmFeXL%2BN4jU%2BFZma37nCUoBq8%3Ddc4a84ddd84950c63ea3a0ce43045de9; uf=da0883eb5260151ed5cd77328d409813eb3dd150f62496a8c8dcfef3f8d40f995356231313def97d7c8df60eda4333a681a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba0790730943c5c914713028f1ec42bf71b1188854805578cc2106fa8f4cb89a32495abbea4f287068f0e141065c1607ec57d41b348be728289b998bb557a444ba4df7ff280fcb29d10d8a4c92b12beb4bb83ba03bdc750abf611d23740bfe9b8768a63fc9f8959a8ee7fafd565af53bf2; cx_p_token=bb565ee1adf936291dbcd6933470a1e7; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyMjE4ODA3NjIiLCJsb2dpblRpbWUiOjE3NTE1NzE5ODQwOTEsImV4cCI6MTc1MjE3Njc4NH0.E-pNj50L_o5YUVPKhH1ntIlbAPkQDs6Hhvp0kCWceJE; xxtenc=c4885b4f5d02c8246c0260b0959ca202; DSSTASH_LOG=C_38-UN_1612-US_221880762-T_1751571984091; k8s=1751603734.045.47053.41278; route=84f2d941b886b65760d51e89c5e0797c; source=\"\"; spaceFid=2790; spaceRoleId=3; tl=1; jrose=C26A5FCAB214C9B88F47229FC7AB7B58.mooc2-2215134336-kz4qp"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)