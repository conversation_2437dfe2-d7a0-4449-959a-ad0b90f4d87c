// ==UserScript==
// @name                蜜雪学习通助手｜超星学习通｜💯自动答题｜▶️自动刷课｜⚡一键操作｜✨字体解密｜📝最新题库自动更新）｜支持AI搜题｜🔔课桌通知
// @version             1.0.0
// @description         蜜雪学习通助手强大的浏览器脚本，旨在帮助用户更高效地完成超星学习通平台上的学习任务。本项目基于开源技术，使用ChatGPT智能引擎进行答题，蜜雪冰城官方题库。一键安装，一键使用，内置详细使用教程，课后测验，期末考试等，本脚本仅供个人研究学习使用，请勿用于非法用途，产生一切法律责任用户自行承担。具体的功能请查看脚本悬浮窗中的教程页面，群聊1：********** 群聊2：********** 蜜雪冰城官网题库 https://tk.mixuelo.cc/
// <AUTHOR> @namespace           https://github.com/MiXue-Lo/MiXue-ChaoXing
// @license             MIT
// @supportURL          https://github.com/MiXue-Lo/MiXue-ChaoXing/issues
// @match               *://*.chaoxing.com/*
// @match               *://*.edu.cn/*
// @match               *://*.nbdlib.cn/*
// @match               *://*.uooc.net.cn/*
// @connect             tk.mixuelo.cc
// @connect             tk.mixuelo.cc
// @run-at              document-end
// @grant               unsafeWindow
// @grant               GM_xmlhttpRequest
// @grant               GM_setValue
// @grant               GM_getValue
// @grant               GM_info
// @grant               GM_getResourceText
// @grant               GM_notification
// @grant               GM_registerMenuCommand
// @grant               GM_openInTab
// @grant               GM_addStyle
// @icon                https://mx.mixuelo.cc/index/pengzi/images/思考2.gif
// @require          https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js
// @require            https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32
// @require  		 https://code.jquery.com/jquery-3.7.1.js5@0.8.3/src/md5.min.js
// @compatible          chrome 80以上版本
// @compatible          firefox 75以上版本
// @compatible          edge 最新版本
// ==/UserScript==

// 定义API基础URL，方便统一管理和修改
const API_BASE_URL = (() => {
  // 确保API URL使用与当前页面相同的协议（避免混合内容问题）
  const baseUrl = "tk.mixuelo.cc/api.php";
  const protocol = window.location.protocol; // 当前页面的协议
  if (protocol === 'https:') {
    return "https://" + baseUrl;
  } else {
    return "http://" + baseUrl;
  }
})();

/*
 * 蜜雪学习通智能助手
 * 版本: 1.0.0
 * 
 * 功能特色:
 * 1. 智能AI答题系统 - 使用先进算法匹配选项
 * 2. 人工智能匹配 - 多种方式识别正确答案
 * 3. 桌面通知系统 - 任务完成提醒
 * 4. 自定义界面 - 可拖拽面板
 * 5. 优化的字体解密功能
 * 6. 支持大部分高校超星系统
 * 
 * 答题优先级说明:
 * 1. 系统首先尝试在题库中查找答案
 * 2. 若题库中未找到答案，根据设置尝试使用AI回答
 * 3. 若AI未开启或无法提供答案，会尝试随机答题(如已开启)
 * 4. 所有方法都失败时，将提示用户手动选择答案
 * 
 * 更新日志:
 * v1.0.0 - 首次发布
 * 
 * 项目主页: https://github.com/MiXue-Lo/MiXue-ChaoXing
 */

/*********************************自定义配置区******************************************************** */
var setting = {
  // 基础界面设置
  showBox: 1,             // 显示脚本浮窗，0为关闭，1为开启
  darkMode: 0,            // 深色模式，0为关闭，1为开启
  panelPosition: 'right', // 控制面板位置，可选 'left', 'right'

  // 任务处理设置
  task: 0,                // 只处理任务点任务，0为关闭，1为开启
  taskInterval: 3000,     // 任务切换间隔时间，默认3秒

  // 媒体处理设置
  video: 1,               // 处理视频，0为关闭，1为开启
  audio: 1,               // 处理音频，0为关闭，1为开启
  rate: 2,                // 视频/音频倍速，0为秒过，1为正常速率，最高16倍
  muteMedia: 0,           // 静音播放，0为关闭，1为开启
  review: 0,              // 复习模式，0为关闭，1为开启可以补挂视频时长

  // 答题设置
  work: 1,                // 测验自动处理，0为关闭，1为开启
  time: 1000,             // 答题时间间隔，默认1s=1000ms
  randomTime: 0,          // 随机答题时间，0为关闭，1为开启，在time基础上随机±500ms
  sub: 0,                 // 测验自动提交，0为关闭,1为开启
  force: 0,               // 测验强制提交，0为关闭，1为开启
  share: 0,               // 自动收录答案，0为关闭，1为开启
  decrypt: 1,             // 字体解密，0为关闭，1为开启

  // 考试设置
  examTurn: 0,            // 考试自动跳转下一题，0为关闭，1为开启
  examTurnTime: 0,        // 考试自动跳转下一题随机间隔时间(3-7s)之间，0为关闭，1为开启
  goodStudent: 1,         // 好学生模式,不自动选择答案,仅提示答案
  alterTitle: 1,          // 修改题目,将AI回复的答案插入题目中

  // AI设置
  aiMode: 'smart',        // AI模式: 'smart'-智能匹配, 'letter'-优先识别字母, 'content'-优先内容匹配
  aiConfidence: 80,       // AI匹配置信度，低于此值会提示可能不准确，范围0-100

  // 通知设置
  desktopNotify: 1,       // 桌面通知，0为关闭，1为开启
  soundNotify: 0,         // 声音通知，0为关闭，1为开启

  // 登录设置
  autoLogin: 0,           // 自动登录，0为关闭，1为开启
  phone: '',              // 登录手机号/超星号
  password: ''            // 登录密码
}

// 高级设置 - 请勿修改除非您知道您在做什么
var advancedSetting = {
  apiEndpoint: 'http://tk.mixuelo.cc/api/v1',  // API接口地址
  apiTimeout: 10000,                        // API请求超时时间(ms)
  apiRetry: 2,                              // API请求失败重试次数
  debugMode: 0                              // 调试模式，0为关闭，1为开启
}

var _w = unsafeWindow,
  _l = location,
  _d = _w.document,
  $ = _w.jQuery || top.jQuery,
  md5 = md5 || window.md5,
  UE = _w.UE,
  Swal = Swal || window.Swal,
  _host = "";

var _host = "http://tk.mixuelo.cc";
var _mlist, _defaults, _domList, $subBtn, $saveBtn, $frame_c;
var reportUrlChange = 0;

// 添加样式
const style = document.createElement('style');
style.id = 'gpt-box-styles';
style.textContent = `
    .gpt-box {
        position: fixed !important;
        top: 80px !important;  /* 调整为距离顶部更远，避免遮挡内容 */
        right: 10px !important;
        width: 300px !important;
        max-height: 400px !important;
        overflow-y: auto !important;
        background: rgba(255, 255, 255, 0.95) !important;
        border-radius: 10px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        z-index: 2147483647 !important; /* 最大可能的z-index值 */
        padding: 15px !important;
        font-family: "Microsoft YaHei", sans-serif !important;
        transition: all 0.3s ease !important;
        animation: slideIn 0.5s ease !important;
        cursor: move !important; /* 添加移动光标 */
        transform: none !important;
        margin: 0 !important;
    }
    
    /* 拖动条样式 */
    .gpt-box-header {
        cursor: move;
        padding: 5px 0;
        margin-bottom: 10px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .gpt-box-title {
        font-weight: bold;
        color: #FC3A72;
    }
    
    .gpt-box-actions {
        display: flex;
        gap: 8px;
    }
    
    .gpt-box-actions button {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 12px;
        color: #888;
        padding: 2px 5px;
        border-radius: 3px;
    }
    
    .gpt-box-actions button:hover {
        background: #f5f5f5;
        color: #FC3A72;
    }
    
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .gpt-box:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }
    
    .gpt-box::-webkit-scrollbar {
        width: 6px;
    }
    .gpt-box::-webkit-scrollbar-thumb {
        background: #FC3A72;
        border-radius: 3px;
    }
    .gpt-message {
        margin: 8px 0;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        line-height: 1.5;
        word-break: break-all;
    }
    .gpt-message.pink {
        background: #fce4ec;
        color: #e91e63;
        border-left: 4px solid #e91e63;
    }
    .gpt-message.orange {
        background: #fff3e0;
        color: #ff9800;
        border-left: 4px solid #ff9800;
    }
    .gpt-message.red {
        background: #ffebee;
        color: #f44336;
        border-left: 4px solid #f44336;
    }
    .gpt-message.purple {
        background: #f3e5f5;
        color: #9c27b0;
        border-left: 4px solid #9c27b0;
    }
    .gpt-message.green {
        background: #e8f5e9;
        color: #4caf50;
        border-left: 4px solid #4caf50;
    }
    .gpt-message.blue {
        background: #e3f2fd;
        color: #2196f3;
        border-left: 4px solid #2196f3;
    }
    .gpt-messages-container {
        max-height: 350px;
        overflow-y: auto;
    }
`;
document.head.appendChild(style);

// 修改logger函数
function logger(str, color = 'black') {
  // 确定要添加到哪个文档中
  let targetDocument = document;
  let targetBody = document.body;

  try {
    // 尝试访问顶层文档
    if (window !== window.top && window.top.document) {
      targetDocument = window.top.document;
      targetBody = targetDocument.body;

      // 如果在iframe中，需要确保顶层文档有我们的样式
      if (!targetDocument.querySelector('style#gpt-box-styles')) {
        const topStyle = targetDocument.createElement('style');
        topStyle.id = 'gpt-box-styles';
        topStyle.textContent = style.textContent;
        targetDocument.head.appendChild(topStyle);
      }
    }
  } catch (e) {
    console.error('无法访问父文档，使用当前文档');
  }

  // 查找或创建日志框
  let box = targetDocument.querySelector('.gpt-box');
  if (!box) {
    // 创建日志框
    box = targetDocument.createElement('div');
    box.className = 'gpt-box';

    // 添加内联样式，确保样式不被覆盖
    const boxStyle = `
      position: fixed !important;
      top: 80px !important;
      right: 10px !important;
      left: auto !important;
      bottom: auto !important;
      z-index: 2147483647 !important;
      transform: none !important;
      margin: 0 !important;
      padding: 0 !important;
      background: rgba(255, 255, 255, 0.98) !important;
      border-radius: 10px !important;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
      width: 320px !important;
      height: auto !important;
      max-height: 500px !important;
      overflow: hidden !important;
      font-family: "Microsoft YaHei", sans-serif !important;
      cursor: move !important;
      user-select: none !important;
      transition: box-shadow 0.3s ease !important;
      border: 1px solid rgba(0, 0, 0, 0.1) !important;
      display: block !important;
    `;

    // 使用setAttribute设置style，确保所有样式都被应用
    box.setAttribute('style', boxStyle);

    // 再次使用style.setProperty确保关键样式不被覆盖
    box.style.setProperty('position', 'fixed', 'important');
    box.style.setProperty('z-index', '2147483647', 'important');
    box.style.setProperty('top', '80px', 'important');
    box.style.setProperty('right', '10px', 'important');
    box.style.setProperty('left', 'auto', 'important');
    box.style.setProperty('display', 'block', 'important');

    // 创建标题栏 - 使用更现代的设计
    const header = targetDocument.createElement('div');
    header.className = 'gpt-box-header';
    header.style.cssText = `
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      padding: 10px 15px !important;
      background: linear-gradient(135deg, #56cabf 0%, #4a90e2 100%) !important;
      color: white !important;
      border-radius: 10px 10px 0 0 !important;
      cursor: move !important;
      user-select: none !important;
    `;

    const title = targetDocument.createElement('div');
    title.className = 'gpt-box-title';
    title.textContent = '蜜雪助手';
    title.style.cssText = `
      font-weight: bold !important;
      font-size: 16px !important;
      color: white !important;
      display: flex !important;
      align-items: center !important;
    `;

    // 添加小图标
    const icon = targetDocument.createElement('span');
    icon.innerHTML = '📝';
    icon.style.cssText = `
      margin-right: 8px !important;
      font-size: 18px !important;
    `;
    title.prepend(icon);

    const actions = targetDocument.createElement('div');
    actions.className = 'gpt-box-actions';
    actions.style.cssText = `
      display: flex !important;
      gap: 8px !important;
    `;

    const clearBtn = targetDocument.createElement('button');
    clearBtn.textContent = '清空';
    clearBtn.title = '清空日志';
    clearBtn.style.cssText = `
      background: rgba(255, 255, 255, 0.2) !important;
      border: none !important;
      border-radius: 4px !important;
      padding: 3px 8px !important;
      color: white !important;
      cursor: pointer !important;
      font-size: 12px !important;
      transition: background 0.3s !important;
    `;
    clearBtn.onmouseover = function () {
      this.style.background = 'rgba(255, 255, 255, 0.3) !important';
    };
    clearBtn.onmouseout = function () {
      this.style.background = 'rgba(255, 255, 255, 0.2) !important';
    };
    clearBtn.onclick = function (e) {
      e.stopPropagation(); // 阻止事件冒泡
      const container = box.querySelector('.gpt-messages-container');
      if (container) {
        container.innerHTML = '';
        logger('日志已清空', 'green');
      }
    };

    const minBtn = targetDocument.createElement('button');
    minBtn.textContent = '隐藏';
    minBtn.title = '隐藏面板';
    minBtn.style.cssText = `
      background: rgba(255, 255, 255, 0.2) !important;
      border: none !important;
      border-radius: 4px !important;
      padding: 3px 8px !important;
      color: white !important;
      cursor: pointer !important;
      font-size: 12px !important;
      transition: background 0.3s !important;
    `;
    minBtn.onmouseover = function () {
      this.style.background = 'rgba(255, 255, 255, 0.3) !important';
    };
    minBtn.onmouseout = function () {
      this.style.background = 'rgba(255, 255, 255, 0.2) !important';
    };
    minBtn.onclick = function (e) {
      e.stopPropagation(); // 阻止事件冒泡
      box.style.display = 'none';
      localStorage.setItem('GPTJsSetting.hideGptBox', 'true');
    };

    actions.appendChild(clearBtn);
    actions.appendChild(minBtn);

    header.appendChild(title);
    header.appendChild(actions);

    // 创建消息容器 - 改进样式
    const messagesContainer = targetDocument.createElement('div');
    messagesContainer.className = 'gpt-messages-container';
    messagesContainer.style.cssText = `
      padding: 10px 15px !important;
      max-height: 400px !important;
      overflow-y: auto !important;
      background: white !important;
      border-radius: 0 0 10px 10px !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      color: #333 !important;
    `;

    box.appendChild(header);
    box.appendChild(messagesContainer);

    // 检查是否需要隐藏gpt-box
    if (localStorage.getItem('GPTJsSetting.hideGptBox') === 'true') {
      box.style.display = 'none';
    }

    // 添加到文档
    targetBody.appendChild(box);

    // 恢复保存的位置
    try {
      const savedPosition = localStorage.getItem('GPTJsSetting.boxPosition');
      if (savedPosition) {
        const position = JSON.parse(savedPosition);
        if (position.left && position.top) {
          box.style.setProperty('left', position.left, 'important');
          box.style.setProperty('top', position.top, 'important');
          box.style.setProperty('right', 'auto', 'important');
        }
      }
    } catch (err) {
      console.error('无法恢复日志框位置', err);
    }

    // 拖拽功能增强 - 使用更强大的事件捕获和处理
    let isDragging = false;
    let dragOffsetX, dragOffsetY;

    // 使用事件捕获阶段，确保先捕获事件
    box.addEventListener('mousedown', function (e) {
      // 如果点击的是按钮，不触发拖动
      if (e.target.tagName === 'BUTTON') {
        return;
      }

      isDragging = true;
      dragOffsetX = e.clientX - box.getBoundingClientRect().left;
      dragOffsetY = e.clientY - box.getBoundingClientRect().top;

      // 拖动时禁用过渡效果
      box.style.setProperty('transition', 'none', 'important');

      // 确保拖动时日志框在最顶层
      box.style.setProperty('z-index', '2147483647', 'important');

      // 拖动时改变视觉效果
      box.style.setProperty('box-shadow', '0 12px 48px rgba(0, 0, 0, 0.4)', 'important');
      box.style.setProperty('opacity', '0.95', 'important');

      // 防止选中文本
      e.preventDefault();

      // 防止事件传播到iframe内部或外部
      e.stopPropagation();
    }, true);

    // 使用顶层文档的mousemove和mouseup事件，并使用捕获阶段
    const mouseMoveHandler = function (e) {
      if (isDragging) {
        const newLeft = e.clientX - dragOffsetX;
        const newTop = e.clientY - dragOffsetY;

        // 确保不超出屏幕边界
        const maxX = window.innerWidth - box.offsetWidth;
        const maxY = window.innerHeight - box.offsetHeight;

        // 使用!important确保样式不被覆盖
        box.style.setProperty('left', Math.max(0, Math.min(newLeft, maxX)) + 'px', 'important');
        box.style.setProperty('top', Math.max(0, Math.min(newTop, maxY)) + 'px', 'important');
        box.style.setProperty('right', 'auto', 'important');
        box.style.setProperty('bottom', 'auto', 'important');

        // 防止事件传播
        e.stopPropagation();
        e.preventDefault();
      }
    };

    const mouseUpHandler = function (e) {
      if (isDragging) {
        isDragging = false;

        // 恢复过渡效果和视觉样式
        box.style.setProperty('transition', 'all 0.3s ease', 'important');
        box.style.setProperty('box-shadow', '0 8px 32px rgba(0, 0, 0, 0.3)', 'important');
        box.style.setProperty('opacity', '1', 'important');

        // 防止事件传播
        e.stopPropagation();

        // 保存位置到localStorage
        try {
          localStorage.setItem('GPTJsSetting.boxPosition', JSON.stringify({
            left: box.style.left,
            top: box.style.top
          }));
        } catch (err) {
          console.error('无法保存日志框位置', err);
        }
      }
    };

    // 添加事件监听器到文档和iframe
    targetDocument.addEventListener('mousemove', mouseMoveHandler, true);
    targetDocument.addEventListener('mouseup', mouseUpHandler, true);

    // 尝试添加到所有可能的iframe
    try {
      const frames = targetDocument.querySelectorAll('iframe');
      frames.forEach(frame => {
        try {
          const frameDoc = frame.contentDocument || frame.contentWindow.document;
          frameDoc.addEventListener('mousemove', mouseMoveHandler, true);
          frameDoc.addEventListener('mouseup', mouseUpHandler, true);
        } catch (e) {
          // 跨域iframe无法访问
        }
      });
    } catch (e) {
      console.error('无法为iframe添加事件监听器', e);
    }

    // 添加键盘快捷键显示/隐藏面板
    targetDocument.addEventListener('keydown', function (e) {
      if (e.key === 'F9' || e.keyCode === 120) {
        box.style.display = box.style.display === 'none' ? 'block' : 'none';
      }
    });
  }

  // 优化日志内容显示
  let displayMessage = str;

  // 检查是否是发送请求数据的日志
  if (str.includes('发送请求数据:')) {
    try {
      // 提取并简化 JSON 数据显示
      const jsonStr = str.substring(str.indexOf('{'));
      const jsonData = JSON.parse(jsonStr);
      const model = jsonData.model;
      const question = jsonData.messages.find(m => m.role === 'user')?.content || '';
      displayMessage = `发送请求：使用模型 ${model}，问题："${question.substring(0, 50)}${question.length > 50 ? '...' : ''}"`;
    } catch (e) {
      // 解析失败，保持原始消息
    }
  }
  // 检查是否是收到响应的日志
  else if (str.includes('收到响应:')) {
    try {
      // 提取并简化响应显示
      const jsonStr = str.substring(str.indexOf('{'), str.lastIndexOf('}') + 1);
      const jsonData = JSON.parse(jsonStr);

      if (jsonData.code === 200) {
        displayMessage = `收到响应：请求成功，服务器状态正常`;
      } else {
        displayMessage = `收到响应：${jsonData.msg || "服务器返回未知状态"}`;
      }
    } catch (e) {
      // 解析失败，简化为通用消息
      displayMessage = "收到服务器响应";
    }
  }

  // 创建消息元素，使用更美观的样式
  const message = targetDocument.createElement('div');
  message.className = `gpt-message ${color}`;

  // 设置基本样式
  const baseStyle = `
    margin-bottom: 8px !important;
    padding: 6px 10px !important;
    border-radius: 6px !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    word-break: break-all !important;
    border-left: 3px solid ${color === 'black' ? '#333' : color} !important;
    background-color: #f8f8f8 !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important;
  `;

  // 根据不同颜色设置不同的样式
  let specificStyle = '';
  switch (color) {
    case 'red':
      specificStyle = `
        color: #d32f2f !important;
        background-color: #ffebee !important;
      `;
      break;
    case 'green':
      specificStyle = `
        color: #388e3c !important;
        background-color: #e8f5e9 !important;
      `;
      break;
    case 'blue':
      specificStyle = `
        color: #1976d2 !important;
        background-color: #e3f2fd !important;
      `;
      break;
    case 'orange':
      specificStyle = `
        color: #e65100 !important;
        background-color: #fff3e0 !important;
      `;
      break;
    default:
      specificStyle = `
        color: #333333 !important;
        background-color: #f5f5f5 !important;
      `;
  }

  // 应用样式
  message.style.cssText = baseStyle + specificStyle;

  // 添加时间戳
  const timestamp = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  const timeSpan = targetDocument.createElement('span');
  timeSpan.style.cssText = `
    color: #888 !important;
    font-size: 11px !important;
    margin-right: 6px !important;
  `;
  timeSpan.textContent = `[${timestamp}] `;

  // 添加消息内容
  const contentSpan = targetDocument.createElement('span');
  contentSpan.innerHTML = displayMessage;

  message.appendChild(timeSpan);
  message.appendChild(contentSpan);

  // 将消息添加到消息容器中
  const messagesContainer = box.querySelector('.gpt-messages-container') || box;
  messagesContainer.appendChild(message);
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

window.onload = function () {
  // 界面状态已在创建时设置，无需重复设置
  // 这样可以避免考试自动跳转时界面短暂显示的问题

  //监听模型改变
  // 获取<select>元素
  const selectElement = $('#modelSelect');
  // 添加change事件监听器
  selectElement.on('change', function () {
    // 获取选定的值
    const selectedModel = selectElement.val();
    // 将选定的值存储到localStorage中
    localStorage.setItem('GPTJsSetting.model', selectedModel);
  });
  // 从localStorage中获取上次选定的模型并设置为<select>的默认值
  const lastSelectedModel = localStorage.getItem('GPTJsSetting.model');
  if (lastSelectedModel) {
    selectElement.val(lastSelectedModel);
  }

  // 增强ne-21box的用户体验
  enhanceNe21BoxExperience();
};

// 增强ne-21box用户体验的函数
function enhanceNe21BoxExperience() {
  setTimeout(function () {
    const neBox = document.getElementById('ne-21box');
    if (neBox) {
      // 添加悬停效果
      neBox.addEventListener('mouseover', function () {
        this.style.boxShadow = '0 12px 42px 0 rgba(31, 38, 135, 0.5)';
      });

      neBox.addEventListener('mouseout', function () {
        this.style.boxShadow = '0 8px 32px 0 rgba(31, 38, 135, 0.37)';
      });

      // 确保初始位置正确
      if (!neBox.style.left || neBox.style.left === '') {
        neBox.style.right = 'auto';
        neBox.style.left = '20px';
        neBox.style.top = '20px';
      }

      // 确保z-index足够高
      neBox.style.setProperty('z-index', '2147483646', 'important');
    }
  }, 1500); // 给予足够时间等待DOM元素加载
}

// F9显示隐藏界面按键事件监听
$(document).keydown(function (e) {
  if (e.keyCode == 120 && $('#ne-21notice')[0] != undefined) {
    // 切换显示状态并更新 localStorage 的值
    if (localStorage.getItem('GPTJsSetting.showBox') == 'hide') {
      $('#ne-21box').css('display', show = 'block');
      $('#ne-21box').css('opacity', '1');
      localStorage.setItem('GPTJsSetting.showBox', 'show');
    } else {
      $('#ne-21box').css('display', show = 'none');
      localStorage.setItem('GPTJsSetting.showBox', 'hide');
    }
  }
});


$('.navshow').find('a:contains(体验新版)')[0] ? $('.navshow').find('a:contains(体验新版)')[0].click() : '';

setting.decrypt ? convertEncryptedFont() : '';

if (_l.hostname == 'i.mooc.chaoxing.com' || _l.hostname == "i.chaoxing.com") {
  // showTips();
} else if (_l.pathname == '/login' && setting.autoLogin) {
  showBox()
  setTimeout(() => { autoLogin() }, 3000)
} else if (_l.pathname.includes('/mycourse/studentstudy')) {
  showBox()
  $('#ne-21log', window.parent.document).html('初始化完毕！')
} else if (_l.pathname.includes('/knowledge/cards')) {
  var params = getTaskParams()
  if (params == null || params == '$mArg' || $.parseJSON(params)['attachments'].length <= 0) {
    logger('无任务点可处理，即将跳转页面', 'red')
    toNext()
  } else {
    setTimeout(() => {
      top.checkJob ? top.checkJob = () => false : true
      _domList = []
      _mlist = $.parseJSON(params)['attachments']
      _defaults = $.parseJSON(params)['defaults']
      $.each($('.wrap .ans-cc .ans-attach-ct'), (i, t) => {
        _domList.push($(t).find('iframe'))
      })
      initializeTaskSystem()
    }, 3000)
  }
} else if (_l.pathname.includes('/exam/test/reVersionTestStartNew')) {
  // 检查是否应该隐藏界面，如果隐藏则延迟显示，避免短暂闪现
  const shouldHideBox = localStorage.getItem('GPTJsSetting.showBox') === 'hide';
  if (shouldHideBox) {
    // 如果应该隐藏，先不显示界面，等考试开始后再根据需要显示
    setTimeout(() => {
      showBox();
      missonExam();
    }, 3000);
  } else {
    showBox();
    setTimeout(() => { missonExam() }, 3000);
  }
} else if (_l.pathname.includes('/exam/test/reVersionPaperMarkContentNew')) {
  setting.share && (() => {
    showBox()
    setTimeout(() => { uploadExam() }, 3000)
  })()
} else if (_l.pathname.includes('/mooc2/work/dowork')) {
  // 检查是否应该隐藏界面，如果隐藏则延迟显示，避免短暂闪现
  const shouldHideBox = localStorage.getItem('GPTJsSetting.showBox') === 'hide';
  if (shouldHideBox) {
    // 如果应该隐藏，先不显示界面，等作业开始后再根据需要显示
    setTimeout(() => {
      showBox();
      missonHomeWork();
    }, 3000);
  } else {
    showBox();
    setTimeout(() => { missonHomeWork() }, 3000);
  }
} else if (_l.pathname.includes('/mooc2/work/view') || _l.pathname.includes('/mooc-ans/mooc2/work/view')) {
  // 移除setting.share条件限制，确保功能总是执行
  // 检查是否应该隐藏界面，如果隐藏则延迟显示，避免短暂闪现
  const shouldHideBox = localStorage.getItem('GPTJsSetting.showBox') === 'hide';
  if (!shouldHideBox) {
    showBox();
  }
  // 根据URL路径选择不同的处理函数
  if (_l.pathname.includes('/mooc-ans/mooc2/work/view')) {
    // 使用更短的延时，让框架加载功能生效
    setTimeout(() => { doWorkView() }, 1000) // 使用新函数处理作业页面
  } else {
    setTimeout(() => { uploadHomeWork() }, 3000) // 使用原有函数收集答案
  }
} else if (_l.pathname.includes('/mooc-ans/mooc2/work/')) {
  // 处理其他所有以/mooc-ans/mooc2/work/开头的路径
  // 检查是否应该隐藏界面，如果隐藏则延迟显示，避免短暂闪现
  const shouldHideBox = localStorage.getItem('GPTJsSetting.showBox') === 'hide';
  if (!shouldHideBox) {
    showBox();
  }
  // 使用更短的延时，让框架加载功能生效
  setTimeout(() => {
    if (shouldHideBox) {
      showBox();
    }
    doWorkView();
  }, 1000) // 使用新函数处理作业页面
} else if (_l.pathname.includes('/work/phone/doHomeWork')) {
  _oldal = _w.alert
  _w.alert = function (msg) {
    if (msg == '保存成功') {
      return;
    }
    return _oldal(msg)
  }
  _oldcf = _w.confirm
  _w.confirm = function (msg) {
    if (msg.includes('确认提交') || msg.includes('未做完')) {
      return true
    }
    return _oldcf(msg)
  }
} else if (_l.pathname.includes('/mooc2/exam/exam-list')) {
  // Swal.fire('ChatGPT学习通助手提示', '注意：请谨慎使用脚本考试，开始考试之前请确保该账号已激活脚本。', 'info')
} else if (_l.pathname == '/mycourse/stu') {
  checkBrowser()
} else {
  // console.log(_l.pathname)
}

function checkBrowser() {
  var userAgent = navigator.userAgent
  if (userAgent.indexOf('Chrome') == -1 || GM_info.scriptHandler != 'ScriptCat') {
    // Swal.fire('您使用的不是推荐运行环境(edge、谷歌浏览器+ScriptCat)，脚本运行可能会发生问题.')
  }
}

function http2https(url) {
  _url = url.replace(/^http:/, 'https:')
  return _url
}

function parseUrlParams() {
  let query = window.location.search.substring(1);
  let vars = query.split("&");
  let _p = {}
  for (let i = 0; i < vars.length; i++) {
    let pair = vars[i].split("=");
    _p[pair[0]] = pair[1]
  }
  return _p
}

function showTips() {
  GM_xmlhttpRequest({
    method: 'GET',
    url: _host + '/api/v1/tips',
    timeout: 5000,
    onload: function (xhr) {
      if (xhr.status == 200) {
        var obj = $.parseJSON(xhr.responseText) || {};
        var _msg = obj.data.msg;
        //Swal.fire('ChatGPT学习通助手提示', _msg, 'info');
      }
    },
    ontimeout: function () {
      var _msg = "链接不到云端服务器，可能是您使用的脚本版本过低，请尽快更新，最新脚本更新发布官";
      //Swal.fire('ChatGPT学习通助手提示', _msg, 'info');
    }
  });
}

function sleep(time) {
  var timeStamp = new Date().getTime();
  var endTime = timeStamp + time;
  while (true) {
    if (new Date().getTime() > endTime) {
      return;
    }
  }
}

/**
 * 安全地执行页面上的函数
 * @param {string} funcName - 要执行的函数名
 * @param {Array} args - 函数参数
 * @returns {boolean} - 是否成功执行
 */
function safeExecutePageFunction(funcName, args = []) {
  try {
    // 尝试直接通过window对象调用
    if (typeof window[funcName] === 'function') {
      window[funcName].apply(window, args);
      return true;
    }

    // 尝试通过eval执行
    if (typeof unsafeWindow !== 'undefined') {
      // 在油猴脚本中，可以使用unsafeWindow访问页面的函数
      if (typeof unsafeWindow[funcName] === 'function') {
        unsafeWindow[funcName].apply(unsafeWindow, args);
        return true;
      }
    }

    // 尝试通过注入脚本执行
    const script = document.createElement('script');
    script.textContent = `
      try {
        if(typeof ${funcName} === 'function') {
          ${funcName}();
          document.dispatchEvent(new CustomEvent('xxt_function_executed', { detail: { success: true, function: '${funcName}' } }));
        } else {
          document.dispatchEvent(new CustomEvent('xxt_function_executed', { detail: { success: false, function: '${funcName}', error: 'Function not found' } }));
        }
      } catch(e) {
        document.dispatchEvent(new CustomEvent('xxt_function_executed', { detail: { success: false, function: '${funcName}', error: e.message } }));
      }
    `;
    document.head.appendChild(script);
    script.remove();
    return true;
  } catch (e) {
    logger(`执行页面函数 ${funcName} 失败: ${e.message}`, 'red');
    return false;
  }
}

// 更多设置
var moreSettingsBtn = document.getElementById('moreSettingsBtn');
var moreSettings = document.getElementById('moreSettings');
var userInfo = document.getElementById('userInfo');
var isSettingsVisible = false;

moreSettingsBtn.addEventListener('click', function () {
  userInfo.style.display = isSettingsVisible ? 'block' : 'none';
  moreSettings.style.display = isSettingsVisible ? 'none' : 'block';
  // 确保在打开设置时关闭功能面板和教程面板
  newFeaturePanel.style.display = 'none';
  tutorialPanel.style.display = 'none';
  // 更新按钮文字
  moreSettingsBtn.textContent = isSettingsVisible ? '设置' : '返回';
  // 如果从设置面板返回，重置新功能按钮和教程按钮文字
  if (isSettingsVisible) {
    newFeatureBtn.textContent = 'AI功能';
    tutorialBtn.textContent = '教程';
  }
  isSettingsVisible = !isSettingsVisible;
  isFeatureVisible = false; // 重置功能面板状态
  isTutorialVisible = false; // 重置教程面板状态
});

// 新功能按钮
var newFeatureBtn = document.getElementById('newFeatureBtn');
var newFeaturePanel = document.getElementById('newFeaturePanel');
var isFeatureVisible = false;

newFeatureBtn.addEventListener('click', function () {
  userInfo.style.display = isFeatureVisible ? 'block' : 'none';
  newFeaturePanel.style.display = isFeatureVisible ? 'none' : 'block';
  // 确保在打开功能面板时关闭设置面板和教程面板
  moreSettings.style.display = 'none';
  tutorialPanel.style.display = 'none';
  // 更新按钮文字
  newFeatureBtn.textContent = isFeatureVisible ? 'AI功能' : '返回';
  // 如果从功能面板返回，重置设置按钮和教程按钮文字
  if (isFeatureVisible) {
    moreSettingsBtn.textContent = '设置';
    tutorialBtn.textContent = '教程';
  }
  isFeatureVisible = !isFeatureVisible;
  isSettingsVisible = false; // 重置设置面板状态
  isTutorialVisible = false; // 重置教程面板状态
});

// 教程按钮
var tutorialBtn = document.getElementById('tutorialBtn');
var tutorialPanel = document.getElementById('tutorialPanel');
var isTutorialVisible = false;

tutorialBtn.addEventListener('click', function () {
  userInfo.style.display = isTutorialVisible ? 'block' : 'none';
  tutorialPanel.style.display = isTutorialVisible ? 'none' : 'block';
  // 确保在打开教程面板时关闭设置面板和功能面板
  moreSettings.style.display = 'none';
  newFeaturePanel.style.display = 'none';
  // 更新按钮文字
  tutorialBtn.textContent = isTutorialVisible ? '教程' : '返回';
  // 如果从教程面板返回，重置设置按钮和新功能按钮文字
  if (isTutorialVisible) {
    moreSettingsBtn.textContent = '设置';
    newFeatureBtn.textContent = 'AI功能';
  }
  isTutorialVisible = !isTutorialVisible;
  isSettingsVisible = false; // 重置设置面板状态
  isFeatureVisible = false; // 重置功能面板状态
});

// 循环添加事件监听器
['sub', 'force', 'examTurn', 'goodStudent', 'alterTitle', 'hideGptBox', 'notification', 'skipTest', 'useAI', 'randomAnswer', 'useTiku', 'autoSave', 'autoSubmit'].forEach(function (settingId) {
  var checkbox = document.getElementById('GPTJsSetting.' + settingId);
  checkbox.addEventListener('change', updateLocalStorage);
  checkbox.checked = localStorage.getItem('GPTJsSetting.' + settingId) === 'true';

  // 检查本地存储中的设置是否为空，如果为空，则设置默认值
  if (localStorage.getItem('GPTJsSetting.' + settingId) === null) {
    // 根据setting对象中的配置设置默认值
    if (settingId === 'sub') {
      localStorage.setItem('GPTJsSetting.' + settingId, setting.sub ? 'true' : 'false');
      checkbox.checked = setting.sub === 1;
    } else if (settingId === 'force') {
      localStorage.setItem('GPTJsSetting.' + settingId, setting.force ? 'true' : 'false');
      checkbox.checked = setting.force === 1;
    } else if (settingId === 'alterTitle') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'true');
    } else if (settingId === 'hideGptBox') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'false');
    } else if (settingId === 'notification') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'true');
    } else if (settingId === 'skipTest') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'false');
    } else if (settingId === 'useAI') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'false');
    } else if (settingId === 'randomAnswer') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'false');
    } else if (settingId === 'useTiku') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'true');
    } else if (settingId === 'autoSave') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'true'); // 默认开启自动保存
    } else if (settingId === 'autoSubmit') {
      localStorage.setItem('GPTJsSetting.' + settingId, 'false'); // 默认关闭自动提交
    }
  }

  // 初始化时处理hideGptBox设置
  if (settingId === 'hideGptBox' && checkbox.checked) {
    const gptBoxes = document.querySelectorAll('.gpt-box');
    gptBoxes.forEach(box => {
      box.style.display = 'none';
    });
  }
});
// 更新本地存储
function updateLocalStorage(event) {
  var checkbox = event.target;
  localStorage.setItem(checkbox.id, checkbox.checked);

  // 处理隐藏答案盒子的设置
  if (checkbox.id === 'GPTJsSetting.hideGptBox') {
    const gptBoxes = document.querySelectorAll('.gpt-box');
    gptBoxes.forEach(box => {
      box.style.display = checkbox.checked ? 'none' : 'block';
    });
  }

  // 处理自动提交作业的设置
  if (checkbox.id === 'GPTJsSetting.autoSubmit') {
    if (checkbox.checked) {
      logger('已启用自动提交作业功能', 'green');
    } else {
      logger('已禁用自动提交作业功能', 'blue');
    }
  }
}

/**
 * 初始化所有设置复选框的状态
 */
function initCheckboxSettings() {
  // 初始化所有设置复选框
  const settingCheckboxes = [
    'GPTJsSetting.sub',
    'GPTJsSetting.force',
    'GPTJsSetting.hideGptBox',
    'GPTJsSetting.examTurn',
    'GPTJsSetting.goodStudent',
    'GPTJsSetting.alterTitle',
    'GPTJsSetting.notification',
    'GPTJsSetting.skipTest',
    'GPTJsSetting.useAI',
    'GPTJsSetting.randomAnswer',
    'GPTJsSetting.useTiku',
    'GPTJsSetting.autoSave',
    'GPTJsSetting.autoSubmit'
  ];

  // 设置自动提交作业的默认值（如果localStorage中没有）
  if (localStorage.getItem('GPTJsSetting.autoSubmit') === null) {
    localStorage.setItem('GPTJsSetting.autoSubmit', 'false');
  }

  // 遍历所有设置复选框，从localStorage读取它们的状态
  settingCheckboxes.forEach(function (checkboxId) {
    const checkbox = document.getElementById(checkboxId);
    if (checkbox) {
      // 从localStorage读取状态，如果没有则使用默认值
      const savedValue = localStorage.getItem(checkboxId);
      if (savedValue !== null) {
        checkbox.checked = savedValue === 'true';
      }

      // 添加事件监听器，当复选框状态改变时更新localStorage
      checkbox.addEventListener('change', updateLocalStorage);

      // 对于自动提交作业选项，特别记录日志
      if (checkboxId === 'GPTJsSetting.autoSubmit' && checkbox.checked) {
        logger('已启用自动提交作业功能', 'green');
      }
    }
  });
}

// 初始化AI模型选择
const modelSelect = document.getElementById('GPTJsSetting.model');
if (modelSelect) {
  // 从localStorage中获取上次选定的模型并设置为<select>的默认值
  const savedModel = localStorage.getItem('GPTJsSetting.model');
  if (savedModel) {
    modelSelect.value = savedModel;
  } else {
    // 如果没有保存过模型，设置默认值并保存
    localStorage.setItem('GPTJsSetting.model', modelSelect.value);
  }

  // 添加change事件监听器
  modelSelect.addEventListener('change', function () {
    localStorage.setItem('GPTJsSetting.model', this.value);
    logger('AI模型已更改为: ' + this.value, '#1890ff');
  });
}

// 初始化AI模型选择
const modelSelect2 = document.getElementById('GPTJsSetting.model');
if (modelSelect2) {
  // 从localStorage中获取上次选定的模型并设置为<select>的默认值
  const savedModel = localStorage.getItem('GPTJsSetting.model');
  if (savedModel) {
    modelSelect2.value = savedModel;
  } else {
    // 如果没有保存过模型，设置默认值并保存
    localStorage.setItem('GPTJsSetting.model', modelSelect2.value);
  }

  // 添加change事件监听器
  modelSelect2.addEventListener('change', function () {
    localStorage.setItem('GPTJsSetting.model', this.value);
    logger('AI模型已更改为: ' + this.value, '#1890ff');
  });
}

function showBox() {
  const box = document.querySelector('.gpt-box');
  if (box) {
    box.style.display = box.style.display === 'none' ? 'block' : 'none';
  }

  // 确定添加到哪个文档中，处理可能的iframe嵌套
  let targetDocument = document;

  try {
    // 尝试访问顶层文档
    if (window !== window.top && window.top.document) {
      targetDocument = window.top.document;
    }
  } catch (e) {
    logger('无法访问父文档，使用当前文档', 'orange');
  }

  // 检查targetDocument是否已有界面元素
  const existingBox = targetDocument.querySelector('#ne-21box');
  if (existingBox) {
    logger('界面已存在，不重复创建', 'blue');

    // 初始化复选框状态
    setTimeout(function () {
      initCheckboxSettings();
    }, 500);

    return;
  }

  //公告&充值
  if (setting.showBox) {
    // 检查localStorage状态，决定初始显示状态
    const shouldHide = localStorage.getItem('GPTJsSetting.showBox') === 'hide';
    const initialDisplay = shouldHide ? 'none' : 'block';
    const initialOpacity = shouldHide ? '0' : '1';
    var box_html = `<div id="ne-21box" style="box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            opacity: ${initialOpacity};
            width: 330px;
            position: fixed;
            top: 20px;
            left: 20px;
            right: auto;
            z-index: 99999;
            overflow-x: auto;
            display: ${initialDisplay};
            border-radius: 10px;
            cursor: move;
            user-select: none;
            transition: box-shadow 0.3s ease;">
            <div class="ne-header" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
              padding: 10px;
                background: rgba(255, 255, 255, 0.75);
                border-radius: 10px 10px 0 0;
                border-bottom: 2px solid #e8eaf6;">
                
                <div class="ne-title" style="
                    display: flex;
                    align-items: center;
                    gap: 10px;">
                    <img src="https://mx.mixuelo.cc/index/pengzi/images/思考2.gif" style="width: 24px; height: 24px;">
                  
                    <h3 style="
                        margin: 0;
                        color: #56cabf;
                        font-size: 18px;
                        font-weight: 600;
                        font-family: 'Microsoft YaHei', sans-serif;">
                        蜜雪学习通助手
                    </h3>
                </div>
                
                <div id="ne-21close" style="
                    color: #9fa8da;
                    font-size: 14px;
                    cursor: pointer;
                    padding: 5px 10px;
                    border-radius: 5px;
                    transition: all 0.3s ease;
                    background: #e8eaf6;
                    font-weight: 500;"
                    onmouseover="this.style.background='#c5cae9'"
                    onmouseout="this.style.background='#e8eaf6'"
                    title="按F9键即可恢复面板">
                    F9显隐面板
                </div>
            </div>
            <div style="padding: 10px;">
            
            <div id="ne-21notice" style="
                margin: 10px 0;
                padding: 10px;
                background: #f5f5f5;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.5;"></div>

            <div id="userInfo" style="
                margin: 10px 0;
                padding: 10px;
                background: #e3f2fd;
                border-radius: 8px;
                font-size: 14px;
                color: #1976d2;"></div>

            <div id="moreSettings" style="
                display: none;
                margin: 10px 0;
                padding: 15px;
                background: #fafafa;
                border-radius: 8px;">
                
                <div style="margin-bottom: 15px;">
                    <div style="margin-bottom: 10px;">
                        <label for="GPTJsSetting.key" style="color: #555;">Key:</label>
                        <input type="text" id="GPTJsSetting.key" style="
                            width: 200px;
                            padding: 5px;
                            border: 1px solid #ddd;
                            border-radius: 4px;">
                        <button id="saveKeyBtn" style="
                            margin-left: 10px;
                            padding: 5px 10px;
                            background-color: #FC3D74;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            ">
                            保存
                        </button>
                        <div id="saveKeyMsg" style="
                            margin-top: 8px;
                            padding: 6px 10px;
                            border-radius: 4px;
                            font-size: 13px;
                            color: white;
                            background-color: #FC3A72;
                            display: none;
                            opacity: 0;
                            transform: translateY(-10px);
                            transition: all 0.3s ease;
                        "></div>
                    </div>
                </div>

                <div style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    font-size: 14px;">
                    <div>
                        <input type="checkbox" id="GPTJsSetting.sub">
                        <label for="GPTJsSetting.sub">测验自动提交</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.force">
                        <label for="GPTJsSetting.force">测验强制提交</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.hideGptBox">
                        <label for="GPTJsSetting.hideGptBox">隐藏答案盒子</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.examTurn">
                        <label for="GPTJsSetting.examTurn">考试自动跳转</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.goodStudent">
                        <label for="GPTJsSetting.goodStudent">答案加粗不选择</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.alterTitle" checked>
                        <label for="GPTJsSetting.alterTitle">答案插入题目后</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.notification" checked>
                        <label for="GPTJsSetting.notification">桌面通知</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.skipTest">
                        <label for="GPTJsSetting.skipTest">不做测验</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.useAI">
                        <label for="GPTJsSetting.useAI">AI自动答题</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.randomAnswer">
                        <label for="GPTJsSetting.randomAnswer">随机答题</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.useTiku" checked>
                        <label for="GPTJsSetting.useTiku">题库答题</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.autoSave" checked>
                        <label for="GPTJsSetting.autoSave">自动保存作业答案</label>
                    </div>
                    <div>
                        <input type="checkbox" id="GPTJsSetting.autoSubmit">
                        <label for="GPTJsSetting.autoSubmit">自动提交作业</label>
                    </div>
                </div>
            </div>

            <div style="margin-top: 15px;">
                <label for="GPTJsSetting.model" style="color: #555;">AI模型:</label>
                <select id="GPTJsSetting.model" style="
                    width: 200px;
                    padding: 5px;
                    border: 1px solid #ddd;
                    border-radius: 4px;">
                    <option value="gpt-3.5-turbo-16k">GPT-3.5-Turbo</option>
                    <option value="gpt-4o-mini">GPT-4o-Mini</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="deepseek-chat">DeepSeek</option>
                    <option value="glm-4-flash">智谱GLM-4</option>
                </select>
            </div>

            <!-- 新增功能面板 -->
            <div id="newFeaturePanel" style="
                display: none;
                margin: 10px 0;
                padding: 15px;
                background: #fff0f6;
                border-radius: 8px;">
                
                <h4 style="
                    margin-top: 0;
                    color: #fc3d74;
                    border-bottom: 1px solid #ffd6e7;
                    padding-bottom: 8px;
                ">
                    AI 助手
                </h4>
                
                <div style="margin-bottom: 12px;">
                    <div style="color: #555; font-size: 13px; margin-bottom: 5px;">选择模型：</div>
                    <select style="width: 100%; border: 1px solid #ffadd2; border-radius: 4px; padding: 6px; font-size: 14px;" id="modelSelect">
                        <option value="gpt-3.5-turbo-16k">GPT-3.5-Turbo (经济实用)</option>
                        <option value="deepseek-chat">DeepSeek-Chat (推荐)</option>
                        <option value="gpt-4o-mini">GPT-4o-Mini (高性价比)</option>
                        <option value="gpt-4">GPT-4 (高精度)</option>
                        <option value="glm-4-flash">GLM-4-Flash (速度优先)</option>
                    </select>
                </div>
                
                <div class="ai-question-section" style="margin-bottom: 10px;">
                    <div style="color: #555; font-size: 13px; margin-bottom: 5px;">输入问题：</div>
                    <textarea id="ai-question" style="
                        width: 95%;
                        min-height: 60px;
                        padding: 8px;
                        border: 1px solid #ffadd2;
                        border-radius: 4px;
                        resize: vertical;
                        font-size: 14px;
                        background-color: #fff;
                        color: #333;
                        margin-bottom: 8px;
                    "></textarea>
                    <div style="display: flex; justify-content: flex-end;">
                        <button id="ai-send-btn" style="
                            padding: 6px 12px;
                            background-color: #FC3D74;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: all 0.3s ease;
                        ">获取答案</button>
                    </div>
                </div>
                
                <div class="ai-answer-section">
                    <div style="
                        color: #555; 
                        font-size: 13px; 
                        margin-bottom: 5px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <span>AI 回答：</span>
                        <button id="ai-copy-btn" style="
                            padding: 3px 8px;
                            background-color: #722ed1;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">复制答案</button>
                    </div>
                    <div id="ai-answer" style="
                       
                        min-height: 100px;
                        max-height: 250px;
                        padding: 10px;
                        border: 1px solid #d3adf7;
                        border-radius: 4px;
                        background-color: #f9f0ff;
                        overflow-y: auto;
                        font-size: 14px;
                        line-height: 1.5;
                        color: #333;
                        margin-bottom: 10px;
                    ">AI 助手已准备就绪，请输入您的问题...</div>
                    
                    <!-- 添加日志控制按钮 -->
                    <div style="margin-top: 10px; display: flex; justify-content: space-between; align-items: center;">
                        <button id="ai-log-toggle" style="
                            padding: 6px 12px;
                            background-color: #8c8c8c;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: all 0.3s ease;
                        ">显示日志</button>
                        <span style="font-size: 12px; color: #888;">控制日志窗口显示/隐藏</span>
                    </div>
                </div>
            </div>

            <!-- 添加教程面板 -->
            <div id="tutorialPanel" style="
                display: none;
                margin: 10px 0;
                padding: 15px;
                background: #e6f7f5;
                border-radius: 8px;">
                
                <!-- 使用教程部分 -->
                <div id="usageTutorial" style="display: none;">
                    <h4 style="
                        margin-top: 0;
                        color: #56CABF;
                        border-bottom: 1px solid #a8e6e0;
                        padding-bottom: 8px;
                    ">
                        脚本使用教程
                    </h4>
                    
                    <div style="margin-bottom: 12px; font-size: 14px; line-height: 1.6; color: #333;">
                        <p style="margin-bottom: 10px;"><strong>脚本功能完全免费，不存在付费情况，进入相应页面即可使用。</strong></p>
                        <p style="margin-bottom: 10px;">脚本没有开发自己的题库，而是接入了多个第三方题库，如需填写密钥，依次操作：[1] 点击标签页"答题" --> [2] 在文本框内填写 --> [3] 刷新</p>
                        <p style="margin-bottom: 10px; color: #ff4d4f;"><strong>注意事项：</strong></p>
                        <ul style="margin-left: 20px; color: #666;">
                            <li style="margin-bottom: 8px;">脚本出现相关问题，请在脚本反馈区反馈，或者私信作者修复。</li>
                            <li style="margin-bottom: 8px;">题库密钥请确认能够搜索到题目再获取，题库均为网络收集的第三方题库，出现任何问题与脚本无关。如果你是程序员，可以自行接入自己的题库，这里不提供任何教程，也不会回复任何询问，请自行查看源代码修改即可，不会改的绕道。</li>
                        </ul>
                    </div>
                </div>

                <!-- 协议部分 -->
                <div id="agreement" style="display: none;">
                    <h4 style="
                        margin-top: 0;
                        color: #56CABF;
                        border-bottom: 1px solid #a8e6e0;
                        padding-bottom: 8px;
                    ">
                        免责声明
                    </h4>
                    
                    <div style="margin-bottom: 12px; font-size: 14px; line-height: 1.6; color: #333;">
                        <p style="margin-bottom: 10px;">1、本脚本仅供学习和研究目的使用，并应在24小时内删除。脚本的使用不应违反任何法律法规及学术道德标准。</p>
                        <p style="margin-bottom: 10px;">2、用户在使用脚本时，必须遵守所有适用的法律法规。任何由于使用脚本而引起的违法行为或不当行为，其产生的一切后果由用户自行承担。</p>
                        <p style="margin-bottom: 10px;">3、开发者不对用户使用脚本所产生的任何直接或间接后果负责。用户应自行评估使用脚本的风险，并对任何可能的负面影响承担全责。</p>
                        <p style="margin-bottom: 10px;">4、本声明的目的在于提醒用户注意相关法律法规与风险，确保用户在明智、合法的前提下使用脚本。</p>
                        <p style="margin-bottom: 10px;">5、如用户在使用脚本的过程中有任何疑问，建议立即停止使用，并删除所有相关文件。</p>
                        <p style="margin-bottom: 10px;">6、本免责声明的最终解释权归脚本开发者所有。</p>
                    </div>
                </div>

                <!-- 切换按钮 -->
                <div style="display: flex; gap: 10px;">
                    <button onclick="document.getElementById('usageTutorial').style.display='block';document.getElementById('agreement').style.display='none';" 
                            style="padding: 5px 10px; background: #56CABF; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        使用教程
                    </button>
                    <button onclick="document.getElementById('agreement').style.display='block';document.getElementById('usageTutorial').style.display='none';" 
                            style="padding: 5px 10px; background: #56CABF; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        免责声明
                    </button>
                </div>
            </div>

            <div id="ne-21log" style="
                max-height: 200px;
                overflow-y: auto;
                margin-top: 10px;
                padding: 10px;
                background: #fff;
                border-radius: 8px;
                border: 1px solid #e0e0e0;"></div>
        </div>`;

    // 将元素添加到正确的文档中
    try {
      if (targetDocument === document) {
        $(box_html).appendTo('body');
      } else {
        // 尝试添加到父文档
        $(targetDocument).find('body').append(box_html);
      }
    } catch (e) {
      // 如果添加到父文档失败，则添加到当前文档
      logger('添加到目标文档失败，使用当前文档: ' + e.message, 'orange');
      $(box_html).appendTo('body');
    }
    $('#ne-21close').click(function () {
      let show = $('#ne-21box').css('display');
      $('#ne-21box').css('display', show == 'block' ? 'none' : 'block');

      // 同步更新localStorage状态，确保页面跳转后保持隐藏状态
      localStorage.setItem('GPTJsSetting.showBox', show == 'block' ? 'hide' : 'show');

      // 当隐藏ne-21box时，显示状态指示器
      if (show == 'block') {
        $('.tiku-settings-btn').parent().css('display', 'flex');
        // 重置所有按钮文字
        moreSettingsBtn.textContent = '设置';
        newFeatureBtn.textContent = 'AI功能';
        tutorialBtn.textContent = '教程';
        // 重置所有面板状态
        isSettingsVisible = false;
        isFeatureVisible = false;
        isTutorialVisible = false;
      } else {
        $('.tiku-settings-btn').parent().css('display', 'none');
      }
    })

    // 确定要操作的文档
    const $doc = (targetDocument === document) ? $ : function (selector) { return $(targetDocument).find(selector); };

    // 初始化key的值
    $doc('#GPTJsSetting\\.key').val(localStorage.getItem('GPTJsSetting.key') || '');

    // 初始化通知开关状态
    const notificationEnabled = localStorage.getItem('GPTJsSetting.notification') !== 'false';
    $doc('#GPTJsSetting\\.notification').prop('checked', notificationEnabled);

    // 添加通知开关的事件监听
    $doc('#GPTJsSetting\\.notification').change(function () {
      localStorage.setItem('GPTJsSetting.notification', this.checked);

      // 显示状态变更提示
      const saveKeyMsg = document.getElementById('saveKeyMsg');
      saveKeyMsg.innerText = this.checked ? '桌面通知已开启' : '桌面通知已关闭';
      saveKeyMsg.style.backgroundColor = this.checked ? '#4CAF50' : '#FF9800';
      saveKeyMsg.style.display = 'block';

      // 使用setTimeout创建动画效果
      setTimeout(function () {
        saveKeyMsg.style.opacity = '1';
        saveKeyMsg.style.transform = 'translateY(0)';
      }, 10);

      // 3秒后隐藏提示
      setTimeout(function () {
        saveKeyMsg.style.opacity = '0';
        saveKeyMsg.style.transform = 'translateY(-10px)';
        setTimeout(function () {
          saveKeyMsg.style.display = 'none';
        }, 300);
      }, 3000);
    });

    // 添加保存按钮的点击事件
    $doc('#saveKeyBtn').click(function () {
      const key = $doc('#GPTJsSetting\\.key').val().trim();
      if (!key) {
        // 显示错误提示
        const saveKeyMsg = document.getElementById('saveKeyMsg');
        saveKeyMsg.innerText = '请输入Key！';
        saveKeyMsg.style.backgroundColor = '#f44336';
        saveKeyMsg.style.display = 'block';

        setTimeout(function () {
          saveKeyMsg.style.opacity = '1';
          saveKeyMsg.style.transform = 'translateY(0)';
        }, 10);

        showDesktopNotification('请输入Key！', '请输入Key！', '');

        setTimeout(function () {
          saveKeyMsg.style.opacity = '0';
          saveKeyMsg.style.transform = 'translateY(-10px)';
          setTimeout(function () {
            saveKeyMsg.style.display = 'none';
          }, 300);
        }, 3000);
        return;
      }

      // 发送API请求验证key
      GM_xmlhttpRequest({
        url: API_BASE_URL + "?act=verify_key",
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        },
        data: "key=" + encodeURIComponent(key),
        onload: function (response) {
          try {
            const result = JSON.parse(response.responseText);
            const saveKeyMsg = document.getElementById('saveKeyMsg');

            if (result.code === 1) {
              // 验证成功，保存key
              localStorage.setItem('GPTJsSetting.key', key);
              localStorage.setItem('tiku_key', key);

              // 显示成功提示
              saveKeyMsg.innerText = 'API Key 保存成功！';
              saveKeyMsg.style.backgroundColor = '#4CAF50';
              saveKeyMsg.style.display = 'block';

              setTimeout(function () {
                saveKeyMsg.style.opacity = '1';
                saveKeyMsg.style.transform = 'translateY(0)';
              }, 10);

              showDesktopNotification('API Key 保存成功！', '您的API Key已成功保存', '');
            } else {
              // 验证失败
              saveKeyMsg.innerText = result.msg || 'Key验证失败';
              saveKeyMsg.style.backgroundColor = '#f44336';
              saveKeyMsg.style.display = 'block';

              setTimeout(function () {
                saveKeyMsg.style.opacity = '1';
                saveKeyMsg.style.transform = 'translateY(0)';
              }, 10);

              showDesktopNotification('Key验证失败', result.msg || 'Key验证失败', '');
            }

            // 3秒后隐藏提示
            setTimeout(function () {
              saveKeyMsg.style.opacity = '0';
              saveKeyMsg.style.transform = 'translateY(-10px)';
              setTimeout(function () {
                saveKeyMsg.style.display = 'none';
              }, 300);
            }, 3000);
          } catch (e) {
            alert('验证请求失败，请稍后重试');
          }
        },
        onerror: function () {
          alert('验证请求失败，请检查网络连接');
        }
      });
    });
  } else {
    $('#ne-21log', window.parent.document).html('')
  }
  let _u = getCk('_uid') || getCk('UID')
  $('#ne-21notice').html(`<div>
    <div>当前学习通账号UID:`+ _u + `</div>
     <div style="color: #56CABF; font-size: 12px; margin-top: 5px;">挂机不是挖矿，所以不建议长时间最小化窗口</div>
    <a target="_blank" href="`+ _host + `?uid=` + _u + `"><button
            style="display: inline-block; padding: 4px 8px; font-size: 10px; border-radius: 4px; text-align: center; text-decoration: none; cursor: pointer; transition: background-color 0.3s ease; color: #fff; background-color: #56CABF; border: none;"
            onmouseover="this.style.backgroundColor='#FC3D74'" onmouseout="this.style.backgroundColor='#3A8BFF'"
            onmousedown="this.style.backgroundColor='#3e8e41'"
            onmouseup="this.style.backgroundColor='#3A8BFF'">蜜雪题库官网</button></a>
    <button id="moreSettingsBtn"
        style="display: inline-block; padding: 4px 8px; font-size: 10px; border-radius: 4px; text-align: center; text-decoration: none; cursor: pointer; transition: background-color 0.3s ease; color: #fff; background-color:rgb(64, 158, 255); border: none;transform: translateX(5px);">设置</button>
    <button id="newFeatureBtn"
        style="display: inline-block; padding: 4px 8px; font-size: 10px; border-radius: 4px; text-align: center; text-decoration: none; cursor: pointer; transition: background-color 0.3s ease; color: #fff; background-color:#FC3D74; border: none;transform: translateX(10px);">AI功能</button>
    <button id="tutorialBtn"
        style="display: inline-block; padding: 4px 8px; font-size: 10px; border-radius: 4px; text-align: center; text-decoration: none; cursor: pointer; transition: background-color 0.3s ease; color: #fff; background-color:#56CABF; border: none;transform: translateX(15px);">教程</button>
</div>`);
  //公告
  GM_xmlhttpRequest({
    method: 'GET',
    url: _host + '/api/v1/auth?uid=' + _u + '&v=' + GM_info['script']['version'],
    timeout: 10000,
    onload: function (xhr) {
      if (xhr.status == 200) {
        var obj = $.parseJSON(xhr.responseText) || {};
        var notice = obj.data.notice;
        var score = obj.data.score;
        $('#userInfo').html(notice + "积分余额:" + score);
        if (obj.data.models) {
          var selectedValue = $('#modelSelect').val();
          $('#modelSelect').html(obj.data.models);
          $('#modelSelect').val(selectedValue);
        }
      }
    },
    ontimeout: function () {
      $('#userInfo').html("欢迎使用，获取服务器公告超时！");
    }
  });

  // 初始化复选框状态
  setTimeout(function () {
    initCheckboxSettings();
  }, 500);
}

function getStr(str, start, end) {
  let res = str.match(new RegExp(`${start}(.*?)${end}`))
  return res ? res[1] : null
}

function getTaskParams() {
  try {
    var _iframeScripts = _d.scripts,
      _p = null;
    for (let i = 0; i < _iframeScripts.length; i++) {
      if (_iframeScripts[i].innerHTML.indexOf('mArg = "";') != -1 && _iframeScripts[i].innerHTML.indexOf('==UserScript==') == -1) {
        _p = getStr(_iframeScripts[i].innerHTML.replace(/\s/g, ""), 'try{mArg=', ';}catch');
        return _p
      }
    }
    return _p
  } catch (e) {
    return null
  }

}

function getCk(name) {
  return document.cookie.match(`[;\s+]?${name}=([^;]*)`)?.pop();
}


function autoLogin() {
  logger('用户已设置自动登录', 'green')
  if (setting.phone.length <= 0 || setting.password.length <= 0) {
    logger('用户未设置登录信息', 'red')
    return
  }
  setTimeout(() => {
    $('#phone').val(setting.phone)
    $('#pwd').val(setting.password)
    $('#loginBtn').click()
  }, 3000)
}

function toNext() {
  refreshCourseList().then((res) => {
    if (setting.review || !setting.work) {
      setTimeout(() => {
        $('#ne-21log', window.parent.document).html('')
        if (top.document.querySelector('#mainid > .prev_next.next') == undefined) {
          top.document.querySelector('#prevNextFocusNext').click();
          return
        }
        top.document.querySelector('#mainid > .prev_next.next').click();
      }, 5000)
      return
    }
    let _t = []
    $.each($(res).find('li'), (_, t) => {
      let curid = $(t).find('.posCatalog_select').attr('id'),
        status = $(t).find('.prevHoverTips').text(),
        name = $(t).find('.posCatalog_name').attr('title');
      if (curid.indexOf('cur') != -1) {
        _t.push({ 'curid': curid, 'status': status, 'name': name })
      }
    })

    let _curChaterId = $('#coursetree', window.parent.document).find('.posCatalog_active').attr('id')
    let _curIndex = _t.findIndex((item) => item['curid'] == _curChaterId)
    for (_curIndex; _curIndex < _t.length - 1; _curIndex++) {
      if (_t[_curIndex]['status'].indexOf('待完成') != -1) {
        let c_tabs = top.document.querySelectorAll('#prev_tab li')
        let c_active_tab = top.document.querySelector('#prev_tab li.active')
        if (c_tabs && c_active_tab) {
          let c_active_tab_id = c_active_tab.getAttribute("id").replace(/dct/, '')
          if (c_active_tab_id != c_tabs.length) {
            setTimeout(() => {
              $('#ne-21log', window.parent.document).html('')
              if (top.document.querySelector('#mainid > .prev_next.next') == undefined) {
                top.document.querySelector('#prevNextFocusNext').click();
                return
              }
              top.document.querySelector('#mainid > .prev_next.next').click();
            }, 5000)
            return
          }
        }
      }
      let t = _t[_curIndex + 1]
      if (t['status'].indexOf('待完成') != -1) {
        // 添加桌面通知
        showDesktopNotification('准备切换下一个任务', `即将切换到: ${t['name']}`, '');

        setTimeout(() => {
          $('#ne-21log', window.parent.document).html('')
          if (top.document.querySelector('#mainid > .prev_next.next') == undefined) {
            top.document.querySelector('#prevNextFocusNext').click();
            return
          }
          top.document.querySelector('#mainid > .prev_next.next').click();
          showBox()
        }, 5000)
        return
      } else if (t['status'].indexOf('闯关') != -1) {
        logger('当前为闯关模式，存在未完成任务点，脚本已暂停运行，请手动完成并点击下一章节', 'red')
        return
      } else if (t['status'].indexOf('开放') != -1) {
        logger('章节未开放', 'red')
        return
      } else {
        //  console.log(t)
      }
    }
    // 添加桌面通知
    showDesktopNotification('课程完成', '此课程所有任务点已处理完毕', '');
    logger('此课程处理完毕', 'green')
    return
  })
}

function initializeTaskSystem() {
  try {
    showBox()
    if (_mlist.length <= 0) {
      // 添加桌面通知
      showDesktopNotification('任务点完成', '此页面所有任务点已处理完毕，准备跳转页面', '');
      logger('此页面任务处理完毕，准备跳转页面', 'green')
      return toNext()
    }
    let _type = _mlist[0]['type'],
      _dom = _domList[0],
      _task = _mlist[0];
    if (_type == undefined) {
      _type = _mlist[0]['property']["module"]
    }

    logger('正在处理任务类型: ' + _type, 'blue');

    switch (_type) {
      case "video":
        if (_mlist[0]['property']['module'] == 'insertvideo') {
          logger('开始处理视频', 'purple')
          processVideoTask(_dom, _task)
          break
        } else if (_mlist[0]['property']['module'] == 'insertaudio') {
          logger('开始处理音频', 'purple')
          processAudioTask(_dom, _task)
          break
        } else {
          logger('未知类型任务，请联系作者，跳过', 'red')
          switchMission()
          break
        }
      case "workid":
        logger('开始处理测验', 'purple')
        // 检查是否设置了不做测验
        if (localStorage.getItem('GPTJsSetting.skipTest') === 'true') {
          logger('已设置不做测验，跳过测验任务', 'orange')
          return toNext()
        } else {
          missonWork(_dom, _task)
        }
        break
      case "document":
        logger('开始处理文档', 'purple')
        missonDoucument(_dom, _task)
        break
      case "read":
        logger('开始处理阅读', 'purple')
        missonRead(_dom, _task)
        break
      case "insertbook":
        logger('开始处理读书', 'purple')
        missonBook(_dom, _task)
        break
      default:
        let GarbageTasks = ['insertimage']
        if (GarbageTasks.indexOf(_type) != -1) {
          logger('发现无需处理任务，跳过。', 'red')
          switchMission()
        } else {
          logger('暂不支持处理此类型:' + _type + '，跳过。', 'red')
          switchMission()
        }
    }
  } catch (e) {
    logger('初始化任务系统出错: ' + e, 'red');

    // 尝试恢复过程
    try {
      if (_mlist && _mlist.length > 0) {
        _mlist.splice(0, 1);
      }
      if (_domList && _domList.length > 0) {
        _domList.splice(0, 1);
      }

      // 延迟后重试
      logger('将在5秒后尝试继续执行任务系统', 'orange');
      setTimeout(() => {
        try {
          initializeTaskSystem();
        } catch (e2) {
          logger('无法恢复任务系统，请刷新页面: ' + e2, 'red');
        }
      }, 5000);
    } catch (e2) {
      logger('恢复过程失败，请刷新页面: ' + e2, 'red');
    }
  }
}


function processAudioTask(dom, obj) {
  if (!setting.audio) {
    logger('用户设置不处理音频任务，准备开始下一个任务。', 'red')
    try {
      // 尝试直接调用switchMission
      switchMission();
    } catch (e) {
      logger('音频任务切换失败: ' + e, 'red');
      // 如果直接调用失败，延迟后重试
      setTimeout(() => {
        try {
          switchMission();
        } catch (e2) {
          // 最后尝试直接调用initializeTaskSystem
          logger('使用initializeTaskSystem作为备选', 'orange');
          setTimeout(initializeTaskSystem, 2000);
        }
      }, 3000);
    }
    return
  }
  let isDo;
  if (setting.task) {
    logger("当前只处理任务点任务", 'red')
    if (obj['jobid'] == undefined ? false : true) {
      isDo = true
    } else {
      isDo = false
    }
  } else {
    logger("当前默认处理所有任务（包括非任务点任务）", 'red')
    isDo = true
  }
  if (isDo) {
    let classId = _defaults['clazzId'],
      userId = _defaults['userid'],
      fid = _defaults['fid'],
      reportUrl = _defaults['reportUrl'],
      isPassed = obj['isPassed'],
      otherInfo = obj['otherInfo'],
      jobId = obj['property']['_jobid'],
      name = obj['property']['name'],
      objectId = obj['property']['objectid'];
    if (setting.maskImg) {
      let ifs = $(dom).attr('style');
      $(dom).contents().find('body').find('.main').attr('style', 'visibility:hidden;')
      $(dom).contents().find('body').prepend('<img src="https://pic.521daigua.cn/bg.jpg!/format/webp" style="' + ifs + 'display:block;width:100%;"/>')
    }
    if (!setting.review && isPassed == true) {
      logger('音频：' + name + '检测已完成，准备处理下一个任务', 'green')
      switchMission()
      return
    } else if (setting.review) {
      logger('已开启复习模式，开始处理音频：' + name, 'pink')
    }
    $.ajax({
      url: _l.protocol + '//' + _l.host + "/ananas/status/" + objectId + '?k=' + fid + '&flag=normal&_dc=' + String(Math.round(new Date())),
      type: "GET",
      success: function (res) {
        try {
          let duration = res['duration'],
            dtoken = res['dtoken'],
            clipTime = '0_' + duration,
            playingTime = 0,
            isdrag = 3;
          var _rt = 0.9;
          if (setting.rate == 0) {
            logger('已开启音频秒过，99.9%会导致进度重置、挂科等问题。', 'red')
            logger('已开启音频秒过，请等待5秒！！！', 'red')
          } else if (setting.rate > 1 && setting.rate <= 16) {
            logger('已开启音频倍速，当前倍速：' + setting.rate + ',99.9%会导致进度重置、挂科等问题。', 'red')
            logger('已开启音频倍速，进度40秒更新一次，请等待！', 'red')
          } else if (setting.rate > 16) {
            setting.rate = 1
            logger('超过允许设置的最大倍数，已重置为1倍速。', 'red')
          } else {
            logger("音频进度每隔40秒更新一次，请等待耐心等待...", 'blue')
          }
          logger("音频：" + name + "开始播放")
          updateAudio(reportUrl, dtoken, classId, playingTime, duration, clipTime, objectId, otherInfo, jobId, userId, isdrag, _rt).then((status) => {
            switch (status) {
              case 1:
                logger("音频：" + name + "已播放" + String((playingTime / duration) * 100).slice(0, 4) + '%', 'purple')
                isdrag = 0
                break
              case 3:
                _rt = 1
                break
              default:
                console.log(status)
            }
          })
          let _loop = setInterval(() => {
            playingTime += 40 * setting.rate
            if (playingTime >= duration || setting.rate == 0) {
              clearInterval(_loop)
              playingTime = duration
              isdrag = 4
            } else if (rt = 1 && playingTime == 40 * setting.rate) {
              isdrag = 3
            } else {
              isdrag = 0
            }
            updateAudio(reportUrl, dtoken, classId, playingTime, duration, clipTime, objectId, otherInfo, jobId, userId, isdrag, _rt).then((status) => {
              switch (status) {
                case 0:
                  playingTime -= 40
                  break
                case 1:
                  logger("音频：" + name + "已播放" + String((playingTime / duration) * 100).slice(0, 4) + '%', 'purple')
                  break
                case 2:
                  clearInterval(_loop)
                  logger("音频：" + name + "检测播放完毕，准备处理下一个任务。", 'green')
                  switchMission()
                  break
                case 3:
                  playingTime -= 40
                  _rt = Number(_rt) == 1 ? 0.9 : 1
                  break
                default:
                  console.log(status)
              }
            })
          }, setting.rate == 0 ? 5000 : 40000)
        } catch (e) {
          logger('发生错误：' + e, 'red')
        }
      }
    });
  } else {
    logger('用户设置只处理属于任务点的任务，准备处理下一个任务', 'green')
    switchMission()
    return
  }
}

function processVideoTask(dom, obj) {
  if (!setting.video) {
    logger('用户设置不处理视频任务，准备开始下一个任务。', 'red')
    try {
      // 尝试直接调用switchMission
      switchMission();
    } catch (e) {
      logger('视频任务切换失败: ' + e, 'red');
      // 如果直接调用失败，延迟后重试
      setTimeout(() => {
        try {
          switchMission();
        } catch (e2) {
          // 最后尝试直接调用initializeTaskSystem
          logger('使用initializeTaskSystem作为备选', 'orange');
          setTimeout(initializeTaskSystem, 2000);
        }
      }, 3000);
    }
    return
  }
  let classId = _defaults["clazzId"], userId = _defaults["userid"], fid = _defaults["fid"], reportUrl = _defaults["reportUrl"], isPassed = obj["isPassed"], otherInfo = obj["otherInfo"], jobId = obj["property"]["_jobid"], name = obj["property"]["name"], objectId = obj["property"]["objectid"];
  if (!setting.review && isPassed == true) {
    logger("视频：" + name + "检测已完成，准备处理下一个任务", "green");
    switchMission();
    return;
  }
  const iframe = $("iframe").get(0);
  const iframeSrc = iframe.src;
  const iframeDocument = iframe.contentDocument;
  if (iframeSrc.includes("video")) {
    logger("发现一个视频，正在解析");
    let isExecuted = false;
    const intervalId = setInterval(() => {
      const video = iframeDocument.documentElement.querySelector("video");
      if (video && !isExecuted) {
        logger("播放成功");
        if (!video) return;
        video.pause();
        video.muted = true;

        // 设置视频播放倍速
        if (setting.rate > 1 && setting.rate <= 16) {
          video.playbackRate = setting.rate;
          logger(`视频倍速已设置为: ${setting.rate}x`, 'blue');
        } else if (setting.rate == 0) {
          video.playbackRate = 16; // 秒过模式使用最高倍速
          logger('视频秒过模式，倍速设置为16x', 'red');
        } else {
          video.playbackRate = 1;
        }

        video.play();
        const listener = () => {
          sleep(2000).then(() => {
            video.play();
          });
        };
        video.addEventListener("pause", listener);
        video.addEventListener("ended", () => {
          logger("视频已播放完成");
          video.removeEventListener("pause", listener);
          resolve();
        });
        isExecuted = true;
        clearInterval(intervalId);
      }
    }, 2500);
  } else if (iframeSrc.includes("audio")) {
    logger("发现一个音频，正在解析");
    let isExecuted = false;
    const intervalId = setInterval(() => {
      const audio = iframeDocument.documentElement.querySelector("audio");
      if (audio && !isExecuted) {
        logger("播放成功");
        if (!audio) return;
        audio.pause();
        audio.muted = true;

        // 设置音频播放倍速
        if (setting.rate > 1 && setting.rate <= 16) {
          audio.playbackRate = setting.rate;
          logger(`音频倍速已设置为: ${setting.rate}x`, 'blue');
        } else if (setting.rate == 0) {
          audio.playbackRate = 16; // 秒过模式使用最高倍速
          logger('音频秒过模式，倍速设置为16x', 'red');
        } else {
          audio.playbackRate = 1;
        }

        audio.play();
        const listener = () => {
          sleep(2000).then(() => {
            audio.play();
          });
        };
        audio.addEventListener("pause", listener);
        audio.addEventListener("ended", () => {
          logger("音频已播放完成");
          audio.removeEventListener("pause", listener);
          resolve();
        });
        isExecuted = true;
        clearInterval(intervalId);
      }
    }, 2500);
  }
}



function missonBook(dom, obj) {
  if (setting.task) {
    if (obj['jobid'] == undefined) {
      logger("当前只处理任务点任务,跳过", 'red')
      try {
        // 尝试直接调用switchMission
        switchMission();
      } catch (e) {
        logger('书籍任务切换失败: ' + e, 'red');
        // 如果直接调用失败，使用setTimeout
        setTimeout(() => {
          try {
            switchMission();
          } catch (e2) {
            // 最后尝试直接调用initializeTaskSystem
            logger('使用initializeTaskSystem作为备选', 'orange');
            setTimeout(initializeTaskSystem, 2000);
          }
        }, 3000);
      }
      return
    }
  }
  let jobId = obj['property']['jobid'],
    name = obj['property']['bookname'],
    jtoken = obj['jtoken'],
    knowledgeId = _defaults['knowledgeid'],
    courseId = _defaults['courseid'],
    clazzId = _defaults['clazzId'];
  if (obj['job'] == undefined) {
    logger('读书：' + name + '检测已完成，准备执行下一个任务。', 'green')
    switchMission()
    return
  }
  $.ajax({
    url: _l.protocol + "//" + _l.host + '/ananas/job?jobid=' + jobId + '&knowledgeid=' + knowledgeId + '&courseid=' + courseId + '&clazzid=' + clazzId + '&jtoken=' + jtoken + '&_dc=' + String(Math.round(new Date())),
    method: 'GET',
    success: function (res) {
      if (res.status) {
        logger('读书：' + name + res.msg + ',准备执行下一个任务。', 'green')
      } else {
        logger('读书：' + name + '处理异常,跳过。', 'red')
      }
      switchMission()
      return
    },
  })
}

function missonLive(dom, obj) {

}

function missonDoucument(dom, obj) {
  if (setting.task) {
    if (obj['jobid'] == undefined) {
      logger("当前只处理任务点任务,跳过", 'red')
      switchMission()
      return
    }
  }
  let jobId = obj['property']['jobid'],
    name = obj['property']['name'],
    jtoken = obj['jtoken'],
    knowledgeId = _defaults['knowledgeid'],
    courseId = _defaults['courseid'],
    clazzId = _defaults['clazzId'];
  if (obj['job'] == undefined) {
    logger('文档：' + name + '检测已完成，准备执行下一个任务。', 'green')
    try {
      // 尝试直接调用switchMission
      switchMission();
    } catch (e) {
      logger('文档任务切换失败: ' + e, 'red');
      // 如果直接调用失败，使用setTimeout
      setTimeout(() => {
        try {
          switchMission();
        } catch (e2) {
          // 最后尝试直接调用initializeTaskSystem
          logger('使用initializeTaskSystem作为备选', 'orange');
          setTimeout(initializeTaskSystem, 2000);
        }
      }, 3000);
    }
    return
  }
  $.ajax({
    url: _l.protocol + "//" + _l.host + '/ananas/job/document?jobid=' + jobId + '&knowledgeid=' + knowledgeId + '&courseid=' + courseId + '&clazzid=' + clazzId + '&jtoken=' + jtoken + '&_dc=' + String(Math.round(new Date())),
    method: 'GET',
    success: function (res) {
      if (res.status) {
        logger('文档：' + name + res.msg + ',准备执行下一个任务。', 'green')
      } else {
        logger('文档：' + name + '处理异常,跳过。', 'red')
      }
      try {
        // 尝试直接调用switchMission
        switchMission();
      } catch (e) {
        logger('文档任务切换失败: ' + e, 'red');
        // 如果直接调用失败，使用setTimeout
        setTimeout(() => {
          try {
            switchMission();
          } catch (e2) {
            // 最后尝试直接调用initializeTaskSystem
            logger('使用initializeTaskSystem作为备选', 'orange');
            setTimeout(initializeTaskSystem, 2000);
          }
        }, 3000);
      }
      return
    },
  })
}

function missonRead(dom, obj) {
  if (setting.task) {
    if (obj['jobid'] == undefined) {
      logger("当前只处理任务点任务,跳过", 'red')
      try {
        // 尝试直接调用switchMission
        switchMission();
      } catch (e) {
        logger('阅读任务切换失败: ' + e, 'red');
        // 如果直接调用失败，使用setTimeout
        setTimeout(() => {
          try {
            switchMission();
          } catch (e2) {
            // 最后尝试直接调用initializeTaskSystem
            logger('使用initializeTaskSystem作为备选', 'orange');
            setTimeout(initializeTaskSystem, 2000);
          }
        }, 3000);
      }
      return
    }
  }
  let jobId = obj['property']['jobid'],
    name = obj['property']['title'],
    jtoken = obj['jtoken'],
    knowledgeId = _defaults['knowledgeid'],
    courseId = _defaults['courseid'],
    clazzId = _defaults['clazzId'];
  if (obj['job'] == undefined) {
    logger('阅读：' + name + ',检测已完成，准备执行下一个任务。', 'green')
    try {
      // 尝试直接调用switchMission
      switchMission();
    } catch (e) {
      logger('阅读任务切换失败: ' + e, 'red');
      // 如果直接调用失败，使用setTimeout
      setTimeout(() => {
        try {
          switchMission();
        } catch (e2) {
          // 最后尝试直接调用initializeTaskSystem
          logger('使用initializeTaskSystem作为备选', 'orange');
          setTimeout(initializeTaskSystem, 2000);
        }
      }, 3000);
    }
    return
  }
  $.ajax({
    url: _l.protocol + '//' + _l.host + '/ananas/job/readv2?jobid=' + jobId + '&knowledgeid=' + knowledgeId + '&courseid=' + courseId + '&clazzid=' + clazzId + '&jtoken=' + jtoken + '&_dc=' + String(Math.round(new Date())),
    method: 'GET',
    success: function (res) {
      if (res.status) {
        logger('阅读：' + name + res.msg + ',准备执行下一个任务。', 'green')
      } else {
        logger('阅读：' + name + '处理异常,跳过。', 'red')
      }
      try {
        // 尝试直接调用switchMission
        switchMission();
      } catch (e) {
        logger('阅读任务切换失败: ' + e, 'red');
        // 如果直接调用失败，使用setTimeout
        setTimeout(() => {
          try {
            switchMission();
          } catch (e2) {
            // 最后尝试直接调用initializeTaskSystem
            logger('使用initializeTaskSystem作为备选', 'orange');
            setTimeout(initializeTaskSystem, 2000);
          }
        }, 3000);
      }
      return
    }
  })
}

function missonWork(dom, obj) {
  if (!setting.work) {
    logger('用户设置不自动处理测验，准备处理下一个任务', 'green')
    try {
      // 尝试直接调用switchMission
      switchMission();
    } catch (e) {
      logger('测验任务切换失败: ' + e, 'red');
      // 如果直接调用失败，使用setTimeout
      setTimeout(() => {
        try {
          switchMission();
        } catch (e2) {
          // 最后尝试直接调用initializeTaskSystem
          logger('使用initializeTaskSystem作为备选', 'orange');
          setTimeout(initializeTaskSystem, 2000);
        }
      }, 3000);
    }
    return
  }
  let isDo;
  if (setting.task) {
    logger("当前只处理任务点任务", 'red')
    if (obj['jobid'] == undefined ? false : true) {
      isDo = true
    } else {
      isDo = false
      // 添加安全的任务切换
      try {
        logger('非任务点测验，跳过', 'orange');
        switchMission();
      } catch (e) {
        logger('任务切换失败: ' + e, 'red');
        setTimeout(initializeTaskSystem, 3000);
      }
      return;
    }
  } else {
    logger("当前默认处理所有任务（包括非任务点任务）", 'red')
    isDo = true
  }
  if (isDo) {
    if (obj['jobid'] !== undefined) {
      var phoneWeb = _l.protocol + '//' + _l.host + '/work/phone/work?workId=' + obj['jobid'].replace('work-', '') + '&courseId=' + _defaults['courseid'] + '&clazzId=' + _defaults['clazzId'] + '&knowledgeId=' + _defaults['knowledgeid'] + '&jobId=' + obj['jobid'] + '&enc=' + obj['enc']
      // setTimeout(() => { startDoCyWork(0, dom) }, 3000)
      setTimeout(() => { startDoPhoneCyWork(0, dom, phoneWeb) }, 3000)
    } else {
      setTimeout(() => { startDoCyWork(0, dom) }, 3000)
    }
    // } else if (!GM_getValue('cando')) {
    //     logger('存在未完成任务点，脚本已暂停执行，请手动处理后刷新网页。', 'red')
    //     return
  } else {
    logger('用户设置只处理属于任务点的任务，准备处理下一个任务', 'green')
    switchMission()
    return
  }
}

function doPhoneWork($dom) {
  let $cy = $dom.find('.Wrappadding form')
  $subBtn = $cy.find('.zquestions .zsubmit .btn-ok-bottom')
  $okBtn = $dom.find('#okBtn')
  $saveBtn = $cy.find('.zquestions .zsubmit .btn-save')
  let TimuList = $cy.find('.zquestions .Py-mian1')
  startDoPhoneTimu(0, TimuList)
}

function startDoPhoneTimu(index, TimuList) {
  if (index == TimuList.length) {
    if (localStorage.getItem('GPTJsSetting.sub') === 'true') {
      logger('测验处理完成，准备自动提交。', 'green')
      setTimeout(() => {
        $subBtn.click()
        setTimeout(() => {
          logger('提交成功，准备切换下一个任务。', 'green')
          _mlist.splice(0, 1)
          _domList.splice(0, 1)
          setTimeout(() => { switchMission() }, 3000)
        }, 3000)
      }, 5000)
    } else if (localStorage.getItem('GPTJsSetting.force') === 'true') {
      logger('测验处理完成，存在无答案题目,由于用户设置了强制提交，准备自动提交。', 'red')
      setTimeout(() => {
        $subBtn.click()
        setTimeout(() => {
          $okBtn.click()
          logger('提交成功，准备切换下一个任务。', 'green')
          _mlist.splice(0, 1)
          _domList.splice(0, 1)
          setTimeout(() => { switchMission() }, 3000)
        }, 3000)
      }, 5000)
    } else {
      logger('测验处理完成，存在无答案题目或用户设置不自动提交，自动保存！', 'green')
      setTimeout(() => {
        logger('保存成功，准备切换下一个任务。', 'green')
        $saveBtn.click()
        setTimeout(() => {
          logger('保存成功，准备切换下一个任务。', 'green')
          _mlist.splice(0, 1)
          _domList.splice(0, 1)

          // 修复切换任务的问题：直接调用initializeTaskSystem
          try {
            logger('正在执行任务切换...', 'blue');
            // 直接调用函数，不使用setTimeout
            initializeTaskSystem();
          } catch (e) {
            logger('任务切换出错: ' + e, 'red');
            // 如果initializeTaskSystem出错，尝试使用switchMission
            setTimeout(() => {
              try {
                switchMission();
              } catch (e2) {
                logger('通过switchMission切换任务也失败: ' + e2, 'red');
                logger('将在5秒后重试，如仍失败请刷新页面', 'orange');
                // 最后尝试直接跳转下一页
                setTimeout(() => {
                  try {
                    toNext();
                  } catch (e3) {
                    logger('无法自动切换，请手动切换到下一任务', 'red');
                  }
                }, 5000);
              }
            }, 3000);
          }
        }, 3000)
      }, 5000)
    }
    return
  }
  let questionFull = $(TimuList[index]).find('.Py-m1-title').html()
  let _question = formatQuestionText(questionFull).replace(/.*?\[.*?题\]\s*\n\s*/, '').trim()
  let _type = ({ 单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4, 选择题: 5 })[questionFull.match(/.*?\[(.*?)]|$/)[1]]
  let _a = []
  let _answerTmpArr
  var check_answer_flag = 0;
  switch (_type) {
    case 0:
      //遍历选项列表
      _answerTmpArr = $(TimuList[index]).find('.answerList.singleChoice li')
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");

      // 不在发送到API的题目中添加题型前缀，只在日志中显示
      logger("单选题: " + _question + '\n' + mergedAnswers, 'blue');
      // 保存选项信息，将选项信息添加到题目中传递给AI
      let phoneQuestionWithOptions = _question + '\n' + mergedAnswers;

      //判断题目是否已作答
      for (var i = 0; i < _answerTmpArr.length; i++) {
        if ($(_answerTmpArr[i]).attr('aria-label')) {
          logger(index + 1 + '此题已作答，准备切换下一题', 'green')
          check_answer_flag = 1;
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, 300)
          break
        }
      }
      if (check_answer_flag == 0) {
        getAnswer(_type, phoneQuestionWithOptions).then((agrs) => {
          _answerTmpArr = $(TimuList[index]).find('.answerList.singleChoice li')
          $.each(_answerTmpArr, (i, t) => {
            _a.push(cleanTextContent($(t).html()).replace(/^[A-Z]\s*\n\s*/, '').trim())
          })

          // 改进的答案匹配逻辑
          let _i = -1;

          // 如果答案为空（来自题库查询失败且没有开启AI或随机答题功能），则不尝试匹配
          if (!agrs || agrs.trim() === '') {
            logger('未获取到有效答案，跳过此题', 'red');
            localStorage.setItem('GPTJsSetting.sub', false);
            setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time);
            return;
          }

          // 检查AI答案是否为选项字母（A、B、C、D）
          if (/^[A-D]$/i.test(agrs.trim())) {
            // 如果是选项字母，直接使用字母对应的索引
            let letterIndex = agrs.trim().toUpperCase().charCodeAt(0) - 65; // A=0, B=1, 等
            if (letterIndex >= 0 && letterIndex < _answerTmpArr.length) {
              _i = letterIndex;
              // logger('识别到选项字母: ' + agrs.trim() + '，对应索引: ' + _i, 'green');
            }
          }
          // 检查答案是否包含"答案：X"格式
          else if (/答案：?[A-D]/i.test(agrs)) {
            let match = agrs.match(/答案：?([A-D])/i);
            if (match && match[1]) {
              let letterIndex = match[1].toUpperCase().charCodeAt(0) - 65;
              if (letterIndex >= 0 && letterIndex < _answerTmpArr.length) {
                _i = letterIndex;
                logger('从答案文本中提取选项: ' + match[1] + '，对应索引: ' + _i, 'green');
              }
            }
          }
          // 如果上面的方法都没找到匹配项，尝试内容匹配
          else {
            // 尝试精确匹配
            _i = _a.findIndex((item) => item == agrs);

            // 如果精确匹配失败，仅在明确得到答案时尝试模糊匹配
            if (_i == -1 && !agrs.includes('未找到答案') && agrs !== '暂无答案') {
              for (let j = 0; j < _a.length; j++) {
                if (agrs.includes(_a[j]) || _a[j].includes(agrs)) {
                  _i = j;
                  logger('使用内容模糊匹配找到选项，索引: ' + _i, 'green');
                  break;
                }
              }
            }
          }

          if (_i == -1) {
            logger('无法匹配正确答案,请手动选择，跳过此题', 'red')
            // setting.sub = 0
            localStorage.setItem('GPTJsSetting.sub', false)
            setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
          } else {
            $(_answerTmpArr[_i]).click()
            logger('自动答题成功，准备切换下一题', 'green')
            setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
          }
        }).catch((agrs) => {
          logger('答案获取失败，跳过此题', 'red');
          if (agrs['c'] == 0) {
            setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
          }
        })
      }
      break
    case 1:
      //遍历选项列表
      _answerTmpArr = $(TimuList[index]).find('.answerList.multiChoice li')
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");

      // 不在发送到API的题目中添加题型前缀，只在日志中显示
      logger("多选题: " + _question + '\n' + mergedAnswers, 'blue');
      // 保存选项信息，将选项信息添加到题目中传递给AI
      let phoneMultiQuestionWithOptions = _question + '\n' + mergedAnswers;

      //判断题目是否已作答
      for (var i = 0; i < _answerTmpArr.length; i++) {
        if ($(_answerTmpArr[i]).attr('aria-label')) {
          logger(index + 1 + '此题已作答，准备切换下一题', 'green')
          check_answer_flag = 1;
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, 300)
          break
        }
      }
      if (check_answer_flag == 0) {
        getAnswer(_type, phoneMultiQuestionWithOptions).then((agrs) => {
          // 检查答案是否为空或无效
          if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案') {
            logger('未获取到有效答案，跳过此题', 'red')
            // setting.sub = 0
            localStorage.setItem('GPTJsSetting.sub', false)
            setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
            return;
          } else {
            _answerTmpArr = $(TimuList[index]).find('.answerList.multiChoice li')

            // 检查AI答案是否为选项字母组合（ABC、BCD等）
            if (/^[A-D]+$/i.test(agrs.trim())) {
              // 如果是选项字母组合，分别点击对应的选项
              let letters = agrs.trim().toUpperCase().split('');
              logger('识别到多选题选项字母: ' + letters.join(','), 'green');

              letters.forEach(letter => {
                let index = letter.charCodeAt(0) - 65; // A=0, B=1, 等
                if (index >= 0 && index < _answerTmpArr.length) {
                  setTimeout(() => { $(_answerTmpArr[index]).click() }, 300);
                }
              });
            }
            // 检查答案是否包含"答案：XYZ"格式
            else if (/答案：?[A-D]+/i.test(agrs)) {
              let match = agrs.match(/答案：?([A-D]+)/i);
              if (match && match[1]) {
                let letters = match[1].toUpperCase().split('');
                logger('从答案文本中提取选项: ' + letters.join(','), 'green');

                letters.forEach(letter => {
                  let index = letter.charCodeAt(0) - 65;
                  if (index >= 0 && index < _answerTmpArr.length) {
                    setTimeout(() => { $(_answerTmpArr[index]).click() }, 300);
                  }
                });
              }
            }
            // 内容匹配（改进的匹配逻辑）
            else {
              // 将AI答案按###分割
              let aiAnswers = agrs.split('###').map(ans => ans.trim());
              logger(`使用AI返回的完整选项内容: ${agrs}`, 'blue');
              logger(`最终处理后的AI答案: ${agrs}`, 'blue');

              $.each(_answerTmpArr, (i, t) => {
                let _tt = cleanTextContent($(t).html()).replace(/^[A-Z]\s*\n\s*/, '').trim();

                // 尝试多种匹配方式
                let matched = false;

                // 1. 精确匹配
                for (let aiAnswer of aiAnswers) {
                  if (_tt === aiAnswer) {
                    matched = true;
                    logger(`精确匹配选项 ${i + 1}: "${_tt}" = "${aiAnswer}"`, 'green');
                    break;
                  }
                }

                // 2. 包含匹配（AI答案包含选项内容）
                if (!matched) {
                  for (let aiAnswer of aiAnswers) {
                    if (aiAnswer.indexOf(_tt) !== -1) {
                      matched = true;
                      logger(`包含匹配选项 ${i + 1}: "${_tt}" 包含在 "${aiAnswer}"`, 'green');
                      break;
                    }
                  }
                }

                // 3. 反向包含匹配（选项内容包含AI答案）
                if (!matched) {
                  for (let aiAnswer of aiAnswers) {
                    if (_tt.indexOf(aiAnswer) !== -1) {
                      matched = true;
                      logger(`反向包含匹配选项 ${i + 1}: "${aiAnswer}" 包含在 "${_tt}"`, 'green');
                      break;
                    }
                  }
                }

                // 4. 模糊匹配（去除标点符号后比较）
                if (!matched) {
                  let cleanTt = _tt.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
                  for (let aiAnswer of aiAnswers) {
                    let cleanAiAnswer = aiAnswer.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
                    if (cleanTt === cleanAiAnswer) {
                      matched = true;
                      logger(`模糊匹配选项 ${i + 1}: "${cleanTt}" = "${cleanAiAnswer}"`, 'green');
                      break;
                    }
                  }
                }

                if (matched) {
                  setTimeout(() => { $(_answerTmpArr[i]).click() }, 300 + i * 100);
                }
              });
            }

            let check = 0
            setTimeout(() => {
              $.each(_answerTmpArr, (i, t) => {
                if ($(t).attr('class').indexOf('cur') != -1) {
                  check = 1
                }
              })
              if (check) {
                logger('自动答题成功，准备切换下一题', 'green')
              } else {
                logger('未能正确选择答案，请手动选择，跳过此题', 'red')
                // setting.sub = 0
                localStorage.setItem('GPTJsSetting.sub', false)
              }
              setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
            }, 1000)
          }
        }).catch((agrs) => {
          if (agrs['c'] == 0) {
            setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
          }
        })
      }
      break
    case 2:
      // 检测是否为新测验框架
      let isNewFramework = $(TimuList[index]).find('.blankList2').length > 0;
      let tkList;

      if (isNewFramework) {
        // 新测验框架：查找富文本编辑器
        tkList = $(TimuList[index]).find('.blankList2 .Briefanswer');
        logger('检测到新测验框架，使用富文本编辑器处理填空题', 'blue');
      } else {
        // 原始框架：查找input元素
        tkList = $(TimuList[index]).find('.blankList2 input');
        logger('检测到原始测验框架，使用input元素处理填空题', 'blue');
      }

      // 检查是否已作答
      let isAnswered = false;
      if (isNewFramework) {
        // 新框架：检查富文本编辑器是否有内容
        tkList.each(function () {
          let editorId = $(this).find('iframe').attr('id');
          if (editorId) {
            try {
              let editorInstance = window.UE && window.UE.getEditor && window.UE.getEditor(editorId.replace('ueditor_', 'ueditorInstant'));
              if (editorInstance && editorInstance.getContent && editorInstance.getContent().trim() !== '') {
                isAnswered = true;
                return false; // 跳出each循环
              }
            } catch (e) {
              // 如果无法获取编辑器实例，检查是否有可见内容
              let content = $(this).find('iframe').contents().find('body').text().trim();
              if (content !== '') {
                isAnswered = true;
                return false;
              }
            }
          }
        });
      } else {
        // 原始框架：检查input值
        if (tkList.length > 0 && $(tkList[0]).val() !== null && $(tkList[0]).val().trim() !== '') {
          isAnswered = true;
        }
      }

      if (isAnswered) {
        logger("此题已作答,跳过", "green");
        setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, 300);
        break;
      }

      getAnswer(_type, _question).then((agrs) => {
        if (agrs == '暂无答案' || agrs === '') {
          logger('AI无法完美匹配正确答案,请手动选择，跳过此题', 'red')
          localStorage.setItem('GPTJsSetting.sub', false)
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
          return
        }

        // 处理AI可能返回的答案格式
        let processedAnswer = agrs;

        // 如果答案包含"答案："前缀，提取其后内容
        if (processedAnswer.includes('答案：')) {
          let parts = processedAnswer.split('答案：');
          if (parts.length > 1) {
            processedAnswer = parts[1].trim();
            // 如果后面有解释，只取第一行
            let lines = processedAnswer.split(/[\n\r]+/);
            processedAnswer = lines[0].trim();
            logger('从AI回答中提取填空答案: ' + processedAnswer, 'green');
          }
        }

        // 检查是否有多个填空，按照不同分隔符尝试分割
        let answers;

        // 先尝试原有的#分隔符
        if (processedAnswer.includes('#')) {
          answers = processedAnswer.split('#');
          logger('使用#号分隔填空答案', 'green');
        }
        // 尝试逗号分隔
        else if (processedAnswer.includes('，') || processedAnswer.includes(',')) {
          answers = processedAnswer.split(/[,，]/);
          logger('使用逗号分隔填空答案', 'green');
        }
        // 尝试空格或制表符分隔
        else if (processedAnswer.includes(' ') || processedAnswer.includes('\t')) {
          answers = processedAnswer.split(/[\s\t]+/);
          logger('使用空格分隔填空答案', 'green');
        }
        // 尝试分号分隔
        else if (processedAnswer.includes('；') || processedAnswer.includes(';')) {
          answers = processedAnswer.split(/[;；]/);
          logger('使用分号分隔填空答案', 'green');
        }
        // 如果没有明显的分隔符，整体作为一个答案
        else {
          answers = [processedAnswer];
          logger('填空答案无分隔符，作为单个答案处理', 'green');
        }

        if (isNewFramework) {
          // 新测验框架：填充富文本编辑器
          logger('开始填充新框架填空题答案', 'blue');
          tkList.each(function (i) {
            if (i < answers.length) {
              let answer = answers[i].trim();
              // 移除可能的序号前缀，如"1."、"（1）"等
              answer = answer.replace(/^\s*[\(（]?\d+[\)）\.]?\s*/, '');

              let editorDiv = $(this);
              let editorId = editorDiv.find('iframe').attr('id');

              if (editorId) {
                setTimeout(() => {
                  try {
                    // 尝试通过UEditor API设置内容
                    let editorInstanceId = editorId.replace('ueditor_', 'ueditorInstant');
                    let editorInstance = window.UE && window.UE.getEditor && window.UE.getEditor(editorInstanceId);

                    if (editorInstance && editorInstance.setContent) {
                      // 使用UEditor API设置内容
                      editorInstance.setContent(answer);
                      // 触发编辑器内容变化事件，确保placeholder消失
                      if (editorInstance.fireEvent) {
                        editorInstance.fireEvent('contentchange');
                      }
                      logger(`成功通过UEditor API填充第${i + 1}空: ${answer}`, 'green');
                    } else {
                      // 备用方案：直接操作iframe内容
                      let iframe = editorDiv.find('iframe')[0];
                      if (iframe && iframe.contentDocument) {
                        let body = iframe.contentDocument.body;
                        if (body) {
                          // 清除placeholder属性，避免文本重叠
                          if (body.hasAttribute('placeholder-attr')) {
                            body.removeAttribute('placeholder-attr');
                          }
                          // 移除empty类，表示编辑器有内容
                          if (body.classList.contains('empty')) {
                            body.classList.remove('empty');
                          }
                          // 设置答案内容
                          body.innerHTML = `<p>${answer}</p>`;
                          // 触发input事件，通知编辑器内容已改变
                          let inputEvent = new Event('input', { bubbles: true });
                          body.dispatchEvent(inputEvent);
                          logger(`成功通过iframe直接填充第${i + 1}空: ${answer}`, 'green');
                        }
                      }
                    }
                  } catch (e) {
                    logger(`填充第${i + 1}空时出错: ${e.message}`, 'red');
                  }
                }, 200 * (i + 1));
              }
            }
          });
        } else {
          // 原始框架：填充input元素
          logger('开始填充原始框架填空题答案', 'blue');
          $.each(tkList, (i, t) => {
            if (i < answers.length) {
              let answer = answers[i].trim();
              // 移除可能的序号前缀，如"1."、"（1）"等
              answer = answer.replace(/^\s*[\(（]?\d+[\)）\.]?\s*/, '');
              setTimeout(() => { $(t).val(answer) }, 200)
            }
          });
        }

        logger('填空题自动答题成功，准备切换下一题', 'green')
        setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
      }).catch((agrs) => {
        if (agrs['c'] == 0) {
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
        }
      })
      break
    case 3:
      // 不在发送到API的题目中添加题型前缀，只在日志中显示
      logger("判断题(只回答正确或错误): " + _question, 'blue');
      getAnswer(_type, _question).then((agrs) => {
        // 检查答案是否为空或无效
        if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案') {
          logger('未获取到有效答案，跳过此题', 'red')
          // setting.sub = 0
          localStorage.setItem('GPTJsSetting.sub', false)
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
          return
        }
        let _true = '正确|是|对|√|T|ri|true|yes'
        let _false = '错误|否|错|×|F|wr|false|no'
        _answerTmpArr = $(TimuList[index]).find('.answerList.panduan li')

        // 处理AI可能返回的带有"答案："前缀的情况
        let cleanAnswer = agrs;
        if (agrs.includes('答案：')) {
          cleanAnswer = agrs.split('答案：')[1].trim().split(/[\n\r]/)[0].trim();
          logger('从AI回答中提取判断结果: ' + cleanAnswer, 'green');
        }

        // 检查是否明确包含正确或错误关键词
        let isTrue = false;
        let isFalse = false;

        // 检查是否包含正确关键词
        _true.split('|').forEach(keyword => {
          if (cleanAnswer.toLowerCase().includes(keyword.toLowerCase())) {
            isTrue = true;
          }
        });

        // 检查是否包含错误关键词
        _false.split('|').forEach(keyword => {
          if (cleanAnswer.toLowerCase().includes(keyword.toLowerCase())) {
            isFalse = true;
          }
        });

        if (isTrue && !isFalse) {
          logger('判断为"正确"', 'green');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'true') {
              $(t).click()
            }
          })
        } else if (isFalse && !isTrue) {
          logger('判断为"错误"', 'green');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'false') {
              $(t).click()
            }
          })
        } else if (_true.indexOf(cleanAnswer) != -1) {
          // 原有的匹配逻辑作为备用
          logger('使用原有逻辑判断为"正确"', 'green');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'true') {
              $(t).click()
            }
          })
        } else {
          // 默认为错误
          logger('默认判断为"错误"', 'orange');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'false') {
              $(t).click()
            }
          })
        }

        logger('自动答题成功，准备切换下一题', 'green')
        setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
      }).catch((agrs) => {
        if (agrs['c'] == 0) {
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
        }
      })
      break
    case 4:
      // 不在发送到API的题目中添加题型前缀，只在日志中显示
      logger("判断题(只回答正确或错误): " + _question, 'blue');
      getAnswer(_type, _question).then((agrs) => {
        // 检查答案是否为空或无效
        if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案') {
          logger('未获取到有效答案，跳过此题', 'red')
          // setting.sub = 0
          localStorage.setItem('GPTJsSetting.sub', false)
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
          return
        }
        let _true = '正确|是|对|√|T|ri|true|yes'
        let _false = '错误|否|错|×|F|wr|false|no'
        _answerTmpArr = $(TimuList[index]).find('.answerList.panduan li')

        // 处理AI可能返回的带有"答案："前缀的情况
        let cleanAnswer = agrs;
        if (agrs.includes('答案：')) {
          cleanAnswer = agrs.split('答案：')[1].trim().split(/[\n\r]/)[0].trim();
          logger('从AI回答中提取判断结果: ' + cleanAnswer, 'green');
        }

        // 检查是否明确包含正确或错误关键词
        let isTrue = false;
        let isFalse = false;

        // 检查是否包含正确关键词
        _true.split('|').forEach(keyword => {
          if (cleanAnswer.toLowerCase().includes(keyword.toLowerCase())) {
            isTrue = true;
          }
        });

        // 检查是否包含错误关键词
        _false.split('|').forEach(keyword => {
          if (cleanAnswer.toLowerCase().includes(keyword.toLowerCase())) {
            isFalse = true;
          }
        });

        if (isTrue && !isFalse) {
          logger('判断为"正确"', 'green');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'true') {
              $(t).click()
            }
          })
        } else if (isFalse && !isTrue) {
          logger('判断为"错误"', 'green');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'false') {
              $(t).click()
            }
          })
        } else if (_true.indexOf(cleanAnswer) != -1) {
          // 原有的匹配逻辑作为备用
          logger('使用原有逻辑判断为"正确"', 'green');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'true') {
              $(t).click()
            }
          })
        } else {
          // 默认为错误
          logger('默认判断为"错误"', 'orange');
          $.each(_answerTmpArr, (i, t) => {
            if ($(t).attr('val-param') == 'false') {
              $(t).click()
            }
          })
        }

        logger('自动答题成功，准备切换下一题', 'green')
        setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
      }).catch((agrs) => {
        if (agrs['c'] == 0) {
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
        }
      })
      break
    case 5:
      getAnswer(_type, _question).then((agrs) => {
        // setting.sub = 0
        localStorage.setItem('GPTJsSetting.sub', false)
        logger('此类型题目无法区分单/多选，请手动选择答案', 'red')
        setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
      }).catch((agrs) => {
        if (agrs['c'] == 0) {
          setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
        }
      })
      break
    default:
      logger('暂不支持处理此类型题目：' + questionFull.match(/.*?\[(.*?)]|$/)[1] + ',跳过！请手动作答。', 'red')
      // setting.sub = 0
      localStorage.setItem('GPTJsSetting.sub', false)
      setTimeout(() => { startDoPhoneTimu(index + 1, TimuList) }, setting.time)
      break
  }
}

function startDoPhoneCyWork(index, doms, phoneWeb) {
  if (index == doms.length) {
    logger('此页面全部测验已处理完毕！准备进行下一项任务')
    setTimeout(missonStart, 5000)
    return
  }
  logger('等待测验框架加载...', 'purple')
  getElement($(doms[index]).contents()[0], 'iframe').then(element => {
    let workIframe = element
    if (workIframe.length == 0) {
      setTimeout(() => { startDoPhoneCyWork(index, doms) }, 5000)
    }
    // let workStatus = $(workIframe).contents().find('.CeYan .ZyTop h3 span:nth-child(1)').text().trim()
    let workStatus = $(workIframe).contents().find('.newTestCon .newTestTitle .testTit_status').text().trim()
    // console.log(workStatus)
    if (!workStatus) {
      _domList.splice(0, 1)
      setTimeout(missonStart, 2000)
      return
    }
    if (setting.share && workStatus.indexOf("已完成") != -1) {
      logger('测验：' + (index + 1) + ',检测到此测验已完成,准备收录答案。', 'green')
      setTimeout(() => { upLoadWork(index, doms, workIframe) }, 2000)
    } else if (workStatus.indexOf("待做") != -1 || workStatus.indexOf("待完成") != -1 || workStatus.indexOf("未达到及格线") != -1) {
      logger('测验：' + (index + 1) + ',准备处理此测验...', 'purple')
      $(workIframe).attr('src', phoneWeb)
      getElement($(doms[index]).contents()[0], 'iframe[src="' + phoneWeb + '"]').then((element) => {
        setTimeout(() => { doPhoneWork($(element).contents()) }, 3000)
      })
    } else if (workStatus.indexOf('待批阅') != -1) {
      _mlist.splice(0, 1)
      _domList.splice(0, 1)
      logger('测验：' + (index + 1) + ',测验待批阅,跳过', 'red')
      setTimeout(() => { startDoPhoneCyWork(index + 1, doms, phoneWeb) }, 5000)
    } else {
      _mlist.splice(0, 1)
      _domList.splice(0, 1)
      logger('测验：' + (index + 1) + ',未知状态或用户选择不收录答案,跳过', 'red')
      setTimeout(() => { startDoPhoneCyWork(index + 1, doms, phoneWeb) }, 5000)
    }
  })
}

function startDoCyWork(index, doms) {
  if (index == doms.length) {
    logger('此页面全部测验已处理完毕！准备进行下一项任务')
    setTimeout(missonStart, 5000)
    return
  }
  logger('等待测验框架加载...', 'purple')
  getElement($(doms[index]).contents()[0], 'iframe').then(element => {
    let workIframe = element
    if (workIframe.length == 0) {
      setTimeout(() => { startDoCyWork(index, doms) }, 5000)
    }
    let workStatus = $(workIframe).contents().find(".newTestCon .newTestTitle .testTit_status").text().trim()
    if (!workStatus) {
      _domList.splice(0, 1)
      setTimeout(missonStart, 2000)
      return
    }
    if (setting.share && workStatus.indexOf("已完成") != -1) {
      logger('测验：' + (index + 1) + ',检测到此测验已完成,准备收录答案。', 'green')
      setTimeout(() => { upLoadWork(index, doms, workIframe) }, 2000)
    } else if (workStatus.indexOf("待做") != -1 || workStatus.indexOf("待完成") != -1) {
      logger('测验：' + (index + 1) + ',准备处理此测验...', 'purple')
      setTimeout(() => { doWork(index, doms, workIframe) }, 5000)
    } else if (workStatus.indexOf('待批阅') != -1) {
      _mlist.splice(0, 1)
      _domList.splice(0, 1)
      logger('测验：' + (index + 1) + ',测验待批阅,跳过', 'red')
      setTimeout(() => { startDoCyWork(index + 1, doms) }, 5000)
    } else {
      _mlist.splice(0, 1)
      _domList.splice(0, 1)
      logger('测验：' + (index + 1) + ',未知状态或用户选择不收录答案,跳过', 'red')
      setTimeout(() => { startDoCyWork(index + 1, doms) }, 5000)
    }
  })
}



function getElement(parent, selector, timeout = 0) {
  /**
   * Author   cxxjackie
   * From     https://bbs.tampermonkey.net.cn
   */
  return new Promise(resolve => {
    let result = parent.querySelector(selector);
    if (result) return resolve(result);
    let timer;
    const mutationObserver = window.MutationObserver || window.WebkitMutationObserver || window.MozMutationObserver;
    if (mutationObserver) {
      const observer = new mutationObserver(mutations => {
        for (let mutation of mutations) {
          for (let addedNode of mutation.addedNodes) {
            if (addedNode instanceof Element) {
              result = addedNode.matches(selector) ? addedNode : addedNode.querySelector(selector);
              if (result) {
                observer.disconnect();
                timer && clearTimeout(timer);
                return resolve(result);
              }
            }
          }
        }
      });
      observer.observe(parent, {
        childList: true,
        subtree: true
      });
      if (timeout > 0) {
        timer = setTimeout(() => {
          observer.disconnect();
          return resolve(null);
        }, timeout);
      }
    } else {
      const listener = e => {
        if (e.target instanceof Element) {
          result = e.target.matches(selector) ? e.target : e.target.querySelector(selector);
          if (result) {
            parent.removeEventListener('DOMNodeInserted', listener, true);
            timer && clearTimeout(timer);
            return resolve(result);
          }
        }
      };
      parent.addEventListener('DOMNodeInserted', listener, true);
      if (timeout > 0) {
        timer = setTimeout(() => {
          parent.removeEventListener('DOMNodeInserted', listener, true);
          return resolve(null);
        }, timeout);
      }
    }
  });
}

function missonHomeWork() {
  logger('开始处理作业', 'green')
  let $_homeworktable = $('.mark_table').find('form')
  let TimuList = $_homeworktable.find('.questionLi')

  // 确保全局变量可用
  window._homeworkTimuList = TimuList
  window._homeworkTable = $_homeworktable

  // 保存原始saveWork函数的引用，以便在需要时调用
  if (typeof saveWork === 'function' && !window._originalSaveWork) {
    window._originalSaveWork = saveWork
  }

  doHomeWork(0, TimuList)
}

function doHomeWork(index, TiMuList) {
  if (index == TiMuList.length) {
    logger('作业题目已全部完成', 'green')

    // 检查是否开启了自动保存
    if (localStorage.getItem('GPTJsSetting.autoSave') !== 'false') {
      // 添加自动保存功能
      try {
        logger('正在自动保存作业答案...', 'blue')

        // 使用安全执行函数调用页面的saveWork函数
        if (safeExecutePageFunction('saveWork')) {
          logger('答案保存成功！', 'green')
        } else {
          // 如果无法通过函数名调用，尝试找到保存按钮并点击
          const saveBtn = $('.btnGray_1:visible') || $('button:contains("保存"):visible') || $('.saveBtn:visible');
          if (saveBtn && saveBtn.length > 0) {
            saveBtn.click();
            logger('已点击保存按钮', 'green')
          } else {
            // 最后尝试通过表单提交
            const form = $('.mark_table').find('form');
            if (form && form.length > 0) {
              logger('尝试通过表单提交保存答案...', 'blue');
              form.submit();
              logger('表单已提交，答案应已保存', 'green');
            } else {
              logger('未找到保存按钮或表单，请手动保存', 'red')
            }
          }
        }

        // 检查是否开启了自动提交作业
        if (localStorage.getItem('GPTJsSetting.autoSubmit') === 'true') {
          logger('准备自动提交作业答案...', 'green');
          setTimeout(() => {
            try {
              // 尝试多种方式找到提交按钮 - 增强选择器匹配能力
              let submitBtn = null;

              // 常见的提交按钮选择器列表
              const submitSelectors = [
                // 优先检查特定ID的按钮
                '#submitBtn:visible',                 // ID为submitBtn的按钮
                '#btnSubmit:visible',                 // ID为btnSubmit的按钮
                '#submit:visible',                    // ID为submit的按钮

                // 特定类的按钮
                '.jb_btn:visible',                    // jb_btn类按钮
                '.jb_btn_92:visible',                 // jb_btn_92类按钮
                '.fr.fs14:visible',                   // 右浮动文本按钮
                '.fs14:contains("提交"):visible',     // 包含"提交"文本的fs14类元素
                '.fr:contains("提交"):visible',       // 包含"提交"文本的fr类元素

                // 超星常见按钮
                '.Btn_blue_1:visible',                // 超星常见蓝色提交按钮
                'button.bluebtn:visible',             // 另一种蓝色提交按钮
                '.btnBlue:visible',                   // 蓝色按钮类
                '.ZY_sub .btnSubmit:visible',         // 章节测验提交按钮
                '.submiting:visible',                 // 提交中类
                'button.submitBtn:visible',           // 提交按钮类
                'a.bluebtn:contains("提交"):visible', // 链接样式的提交按钮

                // 通用按钮
                'button:contains("提交"):visible',    // 包含"提交"文本的按钮
                'input[type="submit"]:visible',       // 提交类型的input
                'button.btn-submit:visible',          // btn-submit类按钮
                'input[value="提交"]:visible',        // 值为"提交"的输入框
                '[onclick*="submit"]:visible',        // 带有提交onclick的元素
                '.fs14:contains("确定"):visible'      // 包含"确定"文本的fs14类元素
              ];

              // 遍历所有可能的选择器
              for (const selector of submitSelectors) {
                const btn = $(selector);
                if (btn && btn.length > 0) {
                  submitBtn = btn;
                  logger(`找到提交按钮(${selector})`, 'green');

                  // 如果找到的是jb_btn类按钮，优先使用它
                  if (btn.hasClass('jb_btn')) {
                    logger('优先使用jb_btn类提交按钮', 'blue');
                    break;
                  }

                  // 如果找到的是带有提交文本的按钮，优先使用它
                  if (btn.text().includes('提交')) {
                    logger('优先使用包含"提交"文本的按钮', 'blue');
                    break;
                  }

                  // 如果不是特殊按钮，继续查找更优先的按钮
                }
              }

              // 如果找到了提交按钮
              if (submitBtn && submitBtn.length > 0) {
                logger('找到提交按钮，点击提交...', 'green');

                // 检查是否是特殊按钮，需要特殊处理
                if (submitBtn.hasClass('jb_btn') || submitBtn.hasClass('fr') || submitBtn.hasClass('fs14')) {
                  logger('检测到特殊提交按钮，使用多种方式点击', 'blue');
                  try {
                    // 尝试直接触发点击事件
                    submitBtn[0].click();
                    // 尝试使用jQuery点击
                    submitBtn.trigger('click');
                    // 尝试模拟点击事件
                    const event = document.createEvent('MouseEvents');
                    event.initEvent('click', true, true);
                    submitBtn[0].dispatchEvent(event);
                  } catch (e) {
                    logger(`特殊处理点击失败: ${e.message}，尝试常规点击`, 'orange');
                    submitBtn.click();
                  }
                } else {
                  // 常规点击
                  submitBtn.click();
                }

                // 尝试处理确认对话框
                setTimeout(() => {
                  // 常见的确认按钮选择器
                  const confirmSelectors = [
                    // 优先检查特定ID的按钮
                    '#popok:visible',                  // 作业确认提交按钮
                    '#btnConfirm:visible',             // 确认按钮ID
                    '#confirmBtn:visible',             // 确认按钮ID
                    '#confirm:visible',                // 确认按钮ID

                    // 特定类的按钮
                    '.jb_btn:contains("提交"):visible', // 包含"提交"文本的jb_btn类按钮
                    '.jb_btn:contains("确定"):visible', // 包含"确定"文本的jb_btn类按钮
                    '.jb_btn_92:visible',              // jb_btn_92类按钮

                    // 超星常见按钮
                    '.bluebtn:visible',                // 蓝色确认按钮
                    '#confirmSubWin .bluebtn:visible', // 确认窗口中的蓝色按钮
                    '.btnBlue:visible',                // 蓝色按钮
                    '.layui-layer-btn0:visible',       // layui弹窗确认按钮
                    '.layui-layer-btn a:eq(0):visible', // layui第一个按钮

                    // 通用按钮
                    'button:contains("确定"):visible', // 包含"确定"文本的按钮
                    'a:contains("确定"):visible',      // 包含"确定"文本的链接
                    'input[value="确定"]:visible',     // 值为"确定"的输入框
                    'a:contains("提交"):visible',      // 包含"提交"文本的链接
                    'button:contains("提交"):visible'  // 包含"提交"文本的按钮
                  ];

                  // 查找确认按钮
                  let confirmBtn = null;
                  for (const selector of confirmSelectors) {
                    const btn = $(selector);
                    if (btn && btn.length > 0) {
                      confirmBtn = btn;
                      logger(`找到确认按钮(${selector})`, 'green');

                      // 如果找到的是#popok，立即使用它
                      if (btn.attr('id') === 'popok') {
                        logger('优先使用#popok确认按钮', 'blue');
                        break;
                      }

                      // 如果找到的是jb_btn类按钮，优先使用它
                      if (btn.hasClass('jb_btn')) {
                        logger('优先使用jb_btn类确认按钮', 'blue');
                        break;
                      }

                      // 如果不是特殊按钮，继续查找更优先的按钮
                    }
                  }

                  if (confirmBtn && confirmBtn.length > 0) {
                    logger('点击确认提交...', 'green');

                    // 检查是否是#popok元素（特殊处理）
                    if (confirmBtn.attr('id') === 'popok') {
                      logger('检测到popok确认按钮，使用特殊处理', 'blue');
                      try {
                        // 尝试直接触发点击事件
                        confirmBtn[0].click();
                        // 尝试使用jQuery点击
                        confirmBtn.trigger('click');
                        // 尝试模拟点击事件
                        const event = document.createEvent('MouseEvents');
                        event.initEvent('click', true, true);
                        confirmBtn[0].dispatchEvent(event);
                      } catch (e) {
                        logger(`特殊处理点击失败: ${e.message}，尝试常规点击`, 'orange');
                        confirmBtn.click();
                      }
                    } else {
                      // 常规点击
                      confirmBtn.click();
                    }

                    logger('作业已自动提交！', 'green');
                  } else {
                    // 如果没有找到确认按钮，可能是直接提交成功了
                    logger('未检测到确认对话框，提交可能已完成', 'blue');
                  }
                }, 1000);
              } else {
                logger('未找到提交按钮，请手动提交', 'red');

                // 记录页面上所有可能的按钮，帮助调试
                const allButtons = $('button, input[type="submit"], .btn, a.btn, .Btn_blue_1, .bluebtn').filter(':visible');
                if (allButtons && allButtons.length > 0) {
                  logger(`页面上有 ${allButtons.length} 个可见按钮，但未找到匹配的提交按钮`, 'orange');
                }
              }
            } catch (e) {
              logger(`自动提交失败: ${e.message}，请手动提交`, 'red');
            }
          }, 2000); // 延迟2秒，确保保存操作完成
        }
      } catch (e) {
        logger('自动保存失败: ' + e.message + '，请手动保存', 'red')
      }
    } else {
      logger('自动保存已禁用，请手动保存答案', 'blue')

      // 即使自动保存被禁用，如果开启了自动提交，仍然尝试提交
      if (localStorage.getItem('GPTJsSetting.autoSubmit') === 'true') {
        logger('准备自动提交作业答案...', 'green');
        setTimeout(() => {
          try {
            // 尝试多种方式找到提交按钮 - 增强选择器匹配能力
            let submitBtn = null;

            // 常见的提交按钮选择器列表
            const submitSelectors = [
              '.Btn_blue_1:visible',                // 超星常见蓝色提交按钮
              'button.bluebtn:visible',             // 另一种蓝色提交按钮
              'button:contains("提交"):visible',    // 包含"提交"文本的按钮
              'input[type="submit"]:visible',       // 提交类型的input
              'button.btn-submit:visible',          // btn-submit类按钮
              '.btnBlue:visible',                   // 蓝色按钮类
              '.ZY_sub .btnSubmit:visible',         // 章节测验提交按钮
              '.submiting:visible',                 // 提交中类
              'button.submitBtn:visible',           // 提交按钮类
              'a.bluebtn:contains("提交"):visible', // 链接样式的提交按钮
              'input[value="提交"]:visible',        // 值为"提交"的输入框
              '[onclick*="submit"]:visible',        // 带有提交onclick的元素
              '.jb_btn:visible',                    // jb_btn类按钮
              '.jb_btn_92:visible',                 // jb_btn_92类按钮
              '.fr.fs14:visible',                   // 右浮动文本按钮
              '.fs14:contains("提交"):visible',     // 包含"提交"文本的fs14类元素
              '.fs14:contains("确定"):visible',     // 包含"确定"文本的fs14类元素
              '.fr:contains("提交"):visible'        // 包含"提交"文本的fr类元素
            ];

            // 遍历所有可能的选择器
            for (const selector of submitSelectors) {
              const btn = $(selector);
              if (btn && btn.length > 0) {
                submitBtn = btn;
                logger(`找到提交按钮(${selector})`, 'green');

                // 如果找到的是jb_btn类按钮，优先使用它
                if (btn.hasClass('jb_btn')) {
                  logger('优先使用jb_btn类提交按钮', 'blue');
                  break;
                }

                // 如果找到的是带有提交文本的按钮，优先使用它
                if (btn.text().includes('提交')) {
                  logger('优先使用包含"提交"文本的按钮', 'blue');
                  break;
                }

                // 如果不是特殊按钮，继续查找更优先的按钮
              }
            }

            // 如果找到了提交按钮
            if (submitBtn && submitBtn.length > 0) {
              logger('找到提交按钮，点击提交...', 'green');

              // 检查是否是特殊按钮，需要特殊处理
              if (submitBtn.hasClass('jb_btn') || submitBtn.hasClass('fr') || submitBtn.hasClass('fs14')) {
                logger('检测到特殊提交按钮，使用多种方式点击', 'blue');
                try {
                  // 尝试直接触发点击事件
                  submitBtn[0].click();
                  // 尝试使用jQuery点击
                  submitBtn.trigger('click');
                  // 尝试模拟点击事件
                  const event = document.createEvent('MouseEvents');
                  event.initEvent('click', true, true);
                  submitBtn[0].dispatchEvent(event);
                } catch (e) {
                  logger(`特殊处理点击失败: ${e.message}，尝试常规点击`, 'orange');
                  submitBtn.click();
                }
              } else {
                // 常规点击
                submitBtn.click();
              }

              // 尝试处理确认对话框
              setTimeout(() => {
                // 常见的确认按钮选择器
                const confirmSelectors = [
                  '.bluebtn:visible',                // 蓝色确认按钮
                  '.layui-layer-btn0:visible',       // layui弹窗确认按钮
                  'button:contains("确定"):visible', // 包含"确定"文本的按钮
                  'a:contains("确定"):visible',      // 包含"确定"文本的链接
                  '#confirmSubWin .bluebtn:visible', // 确认窗口中的蓝色按钮
                  '.btnBlue:visible',                // 蓝色按钮
                  'input[value="确定"]:visible',     // 值为"确定"的输入框
                  '.layui-layer-btn a:eq(0):visible', // layui第一个按钮
                  '#popok:visible',                  // 作业确认提交按钮
                  '.jb_btn:contains("提交"):visible', // 包含"提交"文本的jb_btn类按钮
                  '.jb_btn:contains("确定"):visible'  // 包含"确定"文本的jb_btn类按钮
                ];

                // 查找确认按钮
                let confirmBtn = null;
                for (const selector of confirmSelectors) {
                  const btn = $(selector);
                  if (btn && btn.length > 0) {
                    confirmBtn = btn;
                    logger(`找到确认按钮(${selector})`, 'green');

                    // 如果找到的是#popok，立即使用它
                    if (btn.attr('id') === 'popok') {
                      logger('优先使用#popok确认按钮', 'blue');
                      break;
                    }

                    // 如果找到的是jb_btn类按钮，优先使用它
                    if (btn.hasClass('jb_btn')) {
                      logger('优先使用jb_btn类确认按钮', 'blue');
                      break;
                    }

                    // 如果不是特殊按钮，继续查找更优先的按钮
                  }
                }

                if (confirmBtn && confirmBtn.length > 0) {
                  logger('点击确认提交...', 'green');

                  // 检查是否是#popok元素（特殊处理）
                  if (confirmBtn.attr('id') === 'popok') {
                    logger('检测到popok确认按钮，使用特殊处理', 'blue');
                    try {
                      // 尝试直接触发点击事件
                      confirmBtn[0].click();
                      // 尝试使用jQuery点击
                      confirmBtn.trigger('click');
                      // 尝试模拟点击事件
                      const event = document.createEvent('MouseEvents');
                      event.initEvent('click', true, true);
                      confirmBtn[0].dispatchEvent(event);
                    } catch (e) {
                      logger(`特殊处理点击失败: ${e.message}，尝试常规点击`, 'orange');
                      confirmBtn.click();
                    }
                  } else {
                    // 常规点击
                    confirmBtn.click();
                  }

                  logger('作业已自动提交！', 'green');
                } else {
                  // 如果没有找到确认按钮，可能是直接提交成功了
                  logger('未检测到确认对话框，提交可能已完成', 'blue');
                }
              }, 1000);
            } else {
              logger('未找到提交按钮，请手动提交', 'red');

              // 记录页面上所有可能的按钮，帮助调试
              const allButtons = $('button, input[type="submit"], .btn, a.btn, .Btn_blue_1, .bluebtn').filter(':visible');
              if (allButtons && allButtons.length > 0) {
                logger(`页面上有 ${allButtons.length} 个可见按钮，但未找到匹配的提交按钮`, 'orange');
              }
            }
          } catch (e) {
            logger(`自动提交失败: ${e.message}，请手动提交`, 'red');
          }
        }, 2000);
      }
    }
    return
  }
  let _type = ({ 单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4, 写作题: 5, 翻译题: 6 })[$(TiMuList[index]).attr('typename')]
  let _questionFull = $(TiMuList[index]).find('.mark_name').html()

  // 增强题目处理，确保移除题目类型信息
  let _question = formatQuestionText(_questionFull)

  // 移除题目类型信息 (多选题, 2分) 等格式
  _question = _question.replace(/^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：,，]*[\d\.]*分?[\)）\]\】]?\s*/i, '')

  // 移除开头的序号和其他格式
  _question = _question.replace(/^\s*\d+[\.\、\:：]\s*/, '')

  // 清理多余的空格
  _question = _question.trim()
  let _a = []
  let _answerTmpArr, _textareaList
  var check_answer_flag = 0;
  switch (_type) {
    case 0:
      _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.answer_p')

      //遍历选项列表
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");
      _question = "单选题:" + _question + '\n' + mergedAnswers
      //判断题目是否已作答
      for (var i = 0; i < _answerTmpArr.length; i++) {
        if ($(_answerTmpArr[i]).parent().find('span').attr('class').indexOf('check_answer') == -1) {
          //没有被选择
        } else {
          logger(index + 1 + '此题已作答，准备切换下一题', 'green')
          check_answer_flag = 1;
          setTimeout(() => { doHomeWork(index + 1, TiMuList) }, 300)
          break
        }
      }
      if (check_answer_flag == 0) {
        getAnswer(_type, _question).then((agrs) => {
          $.each(_answerTmpArr, (i, t) => {
            _a.push(cleanTextContent($(t).html()))
          })
          let _i = _a.findIndex((item) => item == agrs)

          if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
            //修改题目将答案插入
            let timuele = $(TiMuList[index]).find('.mark_name')
            // logger("timuele题目标签:"+timuele.html())
            timuele.html(timuele.html() + "<p></p>" + agrs)
          }
          if (_i == -1) {
            logger('AI无法完美匹配正确答案,请手动选择，跳过此题', 'red')
            setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
          } else {
            setTimeout(() => {
              let check = $(_answerTmpArr[_i]).parent().find('span').attr('class')
              if (check.indexOf('check_answer') == -1) {
                $(_answerTmpArr[_i]).parent().click()
              }
              logger('自动答题成功，准备切换下一题', 'green')
              setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
            }, 300)
          }
        }).catch((agrs) => {
          if (agrs['c'] == 0) {
            setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
          }
        })
      }
      break

    case 1:
      _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.answer_p')
      //遍历选项列表
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");
      _question = "多选题:" + _question + '\n' + mergedAnswers
      //判断题目是否已作答
      for (var i = 0; i < _answerTmpArr.length; i++) {
        if ($(_answerTmpArr[i]).parent().find('span').attr('class').indexOf('check_answer') == -1) {
          //没有被选择
        } else {
          logger(index + 1 + '此题已作答，准备切换下一题', 'green')
          check_answer_flag = 1;
          setTimeout(() => { doHomeWork(index + 1, TiMuList) }, 300)
          break
        }
      }
      if (check_answer_flag == 0) {
        getAnswer(_type, _question).then((agrs) => {
          if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
            //修改题目将答案插入
            let timuele = $(TiMuList[index]).find('.mark_name')
            // logger("timuele题目标签:"+timuele.html())
            timuele.html(timuele.html() + "<p></p>" + agrs)
          }
          $.each(_answerTmpArr, (i, t) => {
            if (agrs.indexOf(cleanTextContent($(t).html())) != -1) {
              setTimeout(() => {
                let check = $(_answerTmpArr[i]).parent().find('span').attr('class')
                if (check.indexOf('check_answer_dx') == -1) {
                  $(_answerTmpArr[i]).parent().click()
                }
              }, 300)
            }
          })
          logger('自动答题成功，准备切换下一题', 'green')
          setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
        }).catch((agrs) => {
          if (agrs['c'] == 0) {
            setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
          }
        })
      }
      break
    case 2:
      _question = "填空题,用\"|\"分割多个答案:" + _question;
      _textareaList = $(TiMuList[index]).find('.stem_answer').find('.Answer .divText .textDIV textarea');
      // 判断题目是否已作答
      let _id = $(_textareaList).attr('id');
      if (UE.getEditor(_id).getContent() !== '') {
        logger(index + 1 + '此题已作答，准备切换下一题', 'green');
        setTimeout(() => { doHomeWork(index + 1, TiMuList) }, 300);
      } else {
        getAnswer(_type, _question).then((agrs) => {
          $.each(_textareaList, (i, t) => {
            let _currentId = $(t).attr('id'); // 使用不同的变量名保存当前文本框的ID
            if (UE.getEditor(_currentId).getContent() === '') {
              let _answerTmpArr = agrs.split('|');
              setTimeout(() => { UE.getEditor(_currentId).setContent(_answerTmpArr[i]) }, 300);
            }
          });
          setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time);
          logger('自动答题成功，准备切换下一题', 'green');
        });
      }
      break;

    case 3:
      let _true = '正确|是|对|√|T|ri'
      let _false = '错误|否|错|×|F|wr'
      let _i = 0
      _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.answer_p')
      _question = "判断题(只回答正确或错误):" + _question + '\n' + _answerTmpArr.text()
      $.each(_answerTmpArr, (i, t) => {
        _a.push($(t).text().trim())
      })
      //判断题目是否已作答
      for (var i = 0; i < _answerTmpArr.length; i++) {
        if ($(_answerTmpArr[i]).parent().find('span').attr('class').indexOf('check_answer') == -1) {
          //没有被选择
        } else {
          logger(index + 1 + '此题已作答，准备切换下一题', 'green')
          check_answer_flag = 1;
          setTimeout(() => { doHomeWork(index + 1, TiMuList) }, 300)
          break
        }
      }
      if (check_answer_flag == 0) {
        getAnswer(_type, _question).then((agrs) => {
          if (_true.indexOf(agrs) != -1) {
            _i = _a.findIndex((item) => _true.indexOf(item) != -1)
          } else if (_false.indexOf(agrs) != -1) {
            _i = _a.findIndex((item) => _false.indexOf(item) != -1)
          } else {
            logger('答案匹配出错，准备切换下一题', 'green')
            setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
            return
          }
          setTimeout(() => {
            let check = $(_answerTmpArr[_i]).parent().find('span').attr('class')
            if (check.indexOf('check_answer') == -1) {
              $(_answerTmpArr[_i]).parent().click()
            }
          }, 300)
          logger('自动答题成功，准备切换下一题', 'green')
          setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
        }).catch((agrs) => {
          if (agrs['c'] == 0) {
            setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
          }
        })
      }
      break
    case 4:
      // 不添加用50字简要回答前缀
      _textareaList = $(TiMuList[index]).find('.stem_answer').find('.eidtDiv textarea');
      // 不再判断题目是否已作答，直接获取答案并填写
      $.each(_textareaList, (i, t) => {
        let _id = $(t).attr('id'); // 获取当前文本框的ID
        // 先清空现有内容
        const editor = UE.getEditor(_id);
        if (editor) {
          // 检查是否有内容
          const currentContent = editor.getContent();
          if (currentContent && currentContent !== '') {
            logger(index + 1 + '题目已有内容，将清空并重新填写', 'orange');
            editor.setContent(''); // 清空内容
          }
        }

        // 获取答案并填写
        getAnswer(_type, _question).then((agrs) => {
          setTimeout(() => { UE.getEditor(_id).setContent(agrs) }, 300);
          logger('自动答题成功，准备切换下一题', 'green');
          setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time);
        }).catch((agrs) => {
          if (agrs['c'] == 0) {
            setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time);
          }
        });
      });
      break;
    case 5:
      _answerEle = $_ansdom.find('.subEditor textarea')
      jdt = "用英文根据题目进行写作:" + _question
      $.each(_answerEle, (i, t) => {
        getAnswer(_qType, jdt).then((agrs) => {
          let _id = $(t).attr('name')
          setTimeout(() => { UE.getEditor(_id).setContent(agrs) }, 300);
        });
      });
      break
    case 6:
      _answerEle = $_ansdom.find('.subEditor textarea')
      jdt = "中文英文翻译题:" + _question
      $.each(_answerEle, (i, t) => {
        getAnswer(_qType, jdt).then((agrs) => {
          let _id = $(t).attr('name')
          setTimeout(() => { UE.getEditor(_id).setContent(agrs) }, 300);
        });
      });
      break
    default:
      _answerEle = $_ansdom.find('.subEditor textarea')
      if (_answerEle !== null) {
        jdt = $(TiMuList[index]).attr('typename') + ':' + _question
        $.each(_answerEle, (i, t) => {
          getAnswer(_qType, jdt).then((agrs) => {
            let _id = $(t).attr('name')
            setTimeout(() => { UE.getEditor(_id).setContent(agrs) }, 300);
          });
        });
      } else {
        logger('暂不支持处理此题型：' + $(TiMuList[index]).attr('typename') + ',跳过。', 'red')
        setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
      }
  }
}

function missonExam() {
  logger('开始处理考试', 'green')
  logger('等待测验框架加载...', 'blue')

  // 检查页面是否已加载完成
  if ($('.mark_table').length === 0 || $('.mark_table').find('.whiteDiv').length === 0) {
    // 如果页面元素还没加载完成，设置一个延时后重试
    logger('测验页面元素尚未加载完成，等待重试...', 'orange')
    setTimeout(missonExam, 1000)
    return
  }

  let $_examtable = $('.mark_table').find('.whiteDiv')
  let _questionFull = cleanTextContent($_examtable.find('h3.mark_name').html().trim())

  // 检查题目是否已作答
  let $_ansdom = $_examtable.find('#submitTest').find('.stem_answer')
  let isAnswered = false

  // 检查单选题/多选题的已选中状态
  // 根据浏览器快照，选中的选项可能使用radio[checked]或checkbox[checked]
  let $selectedRadios = $_ansdom.find('input[type="radio"]:checked, input[type="checkbox"]:checked')
  let $selectedSpans = $_ansdom.find('.clearfix.answerBg span.check_answer_dx, .clearfix.answerBg span.check_answer, .clearfix.answerBg span[class*="check"], .clearfix.answerBg .chosen, .clearfix.answerBg .selected')

  if ($selectedRadios.length > 0 || $selectedSpans.length > 0) {
    isAnswered = true
    logger('检测到此题已作答，准备切换下一题', 'green')
  }

  // 检查填空题/简答题的已填写状态
  if (!isAnswered) {
    let $textInputs = $_ansdom.find('input[type="text"], textarea')
    $textInputs.each(function () {
      if ($(this).val() && $(this).val().trim() !== '') {
        isAnswered = true
        return false // 跳出each循环
      }
    })
    if (isAnswered) {
      logger('检测到此题已填写答案，准备切换下一题', 'green')
    }
  }

  // 如果题目已作答且开启了自动跳转，直接跳转
  if (isAnswered && localStorage.getItem('GPTJsSetting.examTurn') === 'true') {
    toNextExam()
    return
  }

  // 增强题型识别逻辑，支持考试页面格式
  let _qType = undefined
  let typeText = ''

  // 尝试多种题型提取方式
  // 方式1: 从span标签中提取 (多选题, 2.0 分)
  let spanMatch = _questionFull.match(/[(（](.*?)[,，].*?分[)）]/);
  if (spanMatch && spanMatch[1]) {
    typeText = spanMatch[1].trim()
    logger(`从span标签提取到题型: ${typeText}`, 'blue')
  }

  // 方式2: 从隐藏的input元素中提取题型
  if (!typeText) {
    let typeNameInput = $_examtable.find('input[name*="typeName"]')
    if (typeNameInput.length > 0) {
      typeText = typeNameInput.val()
      logger(`从隐藏input提取到题型: ${typeText}`, 'blue')
    }
  }

  // 方式3: 原有的正则表达式匹配
  if (!typeText) {
    let oldMatch = _questionFull.match(/[(](.*?),.*?分[)]|$/)
    if (oldMatch && oldMatch[1]) {
      typeText = oldMatch[1].trim()
      logger(`使用原有正则提取到题型: ${typeText}`, 'blue')
    }
  }

  // 题型映射
  const typeMapping = {
    单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4,
    论述题: 4, 写作题: 5, 翻译题: 6
  }

  _qType = typeMapping[typeText]

  // 如果仍然无法识别题型，记录详细信息并设置默认值
  if (_qType === undefined) {
    logger(`无法识别题型，原始HTML: ${_questionFull}`, 'red')
    logger(`提取的题型文本: ${typeText}`, 'red')
    logger(`传递给AI的题型: 未知题型`, 'red')
    _qType = 0 // 默认设为单选题
  } else {
    logger(`传递给AI的题型: ${typeText}`, 'green')
  }

  let _question = formatQuestionText(_questionFull.replace(/[(（].*?分[)）]/, '').replace(/^\s*/, ''))
  // $_ansdom 已在上面声明，这里不需要重新声明
  let _answerTmpArr;
  let _a = []
  switch (_qType) {
    case 0:
      _answerTmpArr = $_ansdom.find('.clearfix.answerBg .fl.answer_p')
      //遍历选项列表
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");
      _question = "单选题:" + _question + '\n' + mergedAnswers
      _question = formatQuestionText(_question.replace(/[(].*?分[)]/, '').replace(/^\s*/, ''))
      logger(`传递给AI的题型: 单选题`, 'green')
      // logger(_question)
      getAnswer(_qType, _question).then((agrs) => {
        $.each(_answerTmpArr, (i, t) => {
          _a.push(cleanTextContent($(t).html()))
        })
        if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
          //修改题目将答案插入
          let timuele = $_examtable.find('h3.mark_name')
          // logger(timuele.html())
          timuele.html(timuele.html() + agrs)
        }

        let _i = _a.findIndex((item) => item == agrs)
        if (_i == -1) {
          logger('AI无法完美匹配正确答案,请手动选择，跳过此题', 'red')
          setTimeout(toNextExam, 5000)
        } else {
          setTimeout(() => {
            // 检查该选项是否已被选中
            let $optionParent = $(_answerTmpArr[_i]).parent()
            let $optionSpan = $optionParent.find('span')
            let $optionRadio = $optionParent.find('input[type="radio"]')
            let isOptionSelected = $optionSpan.hasClass('check_answer') ||
              $optionSpan.hasClass('check_answer_dx') ||
              $optionSpan.attr('class').indexOf('check') !== -1 ||
              $optionRadio.is(':checked')

            if (!isOptionSelected) {
              //好学生模式,ABCD加粗
              if (localStorage.getItem('GPTJsSetting.goodStudent') === 'true') {
                $optionSpan.css('font-weight', 'bold');
              } else {
                setTimeout(() => { $(_answerTmpArr[_i]).parent().click() }, 300)
              }
              logger('自动答题成功，准备切换下一题', 'green')
              toNextExam()
            } else {
              logger('此题已作答，准备切换下一题', 'green')
              toNextExam()
            }
          }, 300)
        }
      }).catch((agrs) => {
        if (agrs['c'] == 0) {
          toNextExam()
        }
      })
      break
    case 1:
      _answerTmpArr = $_ansdom.find('.clearfix.answerBg .fl.answer_p')
      //遍历选项列表
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");
      _question = "多选题:" + _question + '\n' + mergedAnswers
      logger(`传递给AI的题型: 多选题`, 'green')
      getAnswer(_qType, _question).then((agrs) => {
        if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
          //修改题目将答案插入
          let timuele = $_examtable.find('h3.mark_name')
          // logger(timuele.html())
          timuele.html(timuele.html() + agrs)
        }

        // 检查多选题是否已有选项被选中
        let $selectedMultiRadios = $_ansdom.find('input[type="radio"]:checked, input[type="checkbox"]:checked')
        let $selectedMultiSpans = $_ansdom.find('.clearfix.answerBg span.check_answer_dx, .clearfix.answerBg span.check_answer, .clearfix.answerBg span[class*="check"]')
        if ($selectedMultiRadios.length > 0 || $selectedMultiSpans.length > 0) {
          logger('此题已作答，准备切换下一题', 'green')
          toNextExam()
        } else {
          $.each(_answerTmpArr, (i, t) => {
            if (agrs.indexOf(cleanTextContent($(t).html())) != -1) {
              //好学生模式,ABCD加粗
              if (localStorage.getItem('GPTJsSetting.goodStudent') === 'true') {
                $(_answerTmpArr[_i]).parent().find('span').css('font-weight', 'bold');
              } else {
                setTimeout(() => { $(_answerTmpArr[i]).parent().click() }, 300)
              }
            }
          });
          logger('自动答题成功，准备切换下一题', 'green')
          toNextExam()
        }
      }).catch((agrs) => {
        if (agrs['c'] == 0) {
          toNextExam()
        }
      })
      break
    case 2:
      _question = "填空题,用\"|\"分割多个答案:" + _question;
      logger(`传递给AI的题型: 填空题`, 'green')
      let _textareaList = $_ansdom.find('.Answer .divText .subEditor textarea')
      // logger(_textareaList)
      getAnswer(_qType, _question).then((agrs) => {
        let _answerTmpArr = agrs.split('|')
        $.each(_textareaList, (i, t) => {
          // logger(t)
          let _id = $(t).attr('id')
          setTimeout(() => { UE.getEditor(_id).setContent(_answerTmpArr[i]) }, 300)
        })
        logger('自动答题成功，准备切换下一题', 'green')
        toNextExam()
      })
      break
    case 3:
      let _true = '正确|是|对|√|T|ri'
      let _false = '错误|否|错|×|F|wr'
      let _i = 0
      _question = "判断题(只回答正确或错误):" + _question;
      logger(`传递给AI的题型: 判断题`, 'green')
      _answerTmpArr = $_ansdom.find('.clearfix.answerBg .fl.answer_p')
      $.each(_answerTmpArr, (i, t) => {
        _a.push($(t).text().trim())
      })
      getAnswer(_qType, _question).then((agrs) => {
        if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
          //修改题目将答案插入
          let timuele = $_examtable.find('h3.mark_name')
          // logger(timuele.html())
          timuele.html(timuele.html() + agrs)
        }

        if (_true.indexOf(agrs) != -1) {
          _i = _a.findIndex((item) => _true.indexOf(item) != -1)
        } else if (_false.indexOf(agrs) != -1) {
          _i = _a.findIndex((item) => _false.indexOf(item) != -1)
        } else {
          logger('答案匹配出错，准备切换下一题', 'green')
          toNextExam()
          return
        }
        if ($(_answerTmpArr[_i]).parent().find('span').attr('class').indexOf('check_answer') == -1) {
          //好学生模式,ABCD加粗
          if (localStorage.getItem('GPTJsSetting.goodStudent') === 'true') {
            setTimeout(() => { $(_answerTmpArr[_i]).parent().find('span').css('font-weight', 'bold'); }, 300)
          } else {
            $(_answerTmpArr[_i]).parent().click()
          }
          logger('自动答题成功，准备切换下一题', 'green')
          toNextExam()
        } else {
          logger(index + 1 + '此题已作答，准备切换下一题', 'green')
          toNextExam()
        }
      }).catch((agrs) => {
        if (agrs['c'] == 0) {
          toNextExam()
        }
      })
      break
    case 4:
      _answerEle = $_ansdom.find('.subEditor textarea')
      jdt = _question // 不添加用50字简要回答前缀
      logger(`传递给AI的题型: 简答题`, 'green')
      $.each(_answerEle, (i, t) => {
        getAnswer(_qType, jdt).then((agrs) => {
          let _id = $(t).attr('name')
          setTimeout(() => { UE.getEditor(_id).setContent(agrs) }, 300);
          toNextExam()
        });
      });
      break
    case 5:
      _answerEle = $_ansdom.find('.subEditor textarea')
      jdt = "用英文根据题目进行写作:" + _question
      logger(`传递给AI的题型: 写作题`, 'green')
      $.each(_answerEle, (i, t) => {
        getAnswer(_qType, jdt).then((agrs) => {
          let _id = $(t).attr('name')
          setTimeout(() => { UE.getEditor(_id).setContent(agrs) }, 300);
          toNextExam()
        });
      });
      break
    case 6:
      _answerEle = $_ansdom.find('.subEditor textarea')
      jdt = "中文英文翻译题:" + _question
      logger(`传递给AI的题型: 翻译题`, 'green')
      $.each(_answerEle, (i, t) => {
        getAnswer(_qType, jdt).then((agrs) => {
          let _id = $(t).attr('name')
          setTimeout(() => { UE.getEditor(_id).setContent(agrs) }, 300);
          toNextExam()
        });
      });
      break
    default:
      _answerEle = $_ansdom.find('.Answer .divText .subEditor textarea')
      if (typeof _answerEle !== 'undefined') {
        jdt = _questionFull.match(/[(](.*?),.*?分[)]|$/)[1] + ":" + "填空题,用\"|\"分割多个答案:" + _question
        getAnswer(_qType, _question).then((agrs) => {
          let _answerTmpArr = agrs.split('|')
          $.each(_answerEle, (i, t) => {
            let _id = $(t).attr('id')
            setTimeout(() => { UE.getEditor(_id).setContent(_answerTmpArr[i]) }, 300)
          })
          logger('自动答题成功，准备切换下一题', 'green')
          toNextExam()
        })
      }
      else {
        logger('暂不支持处理此题型：' + $(TiMuList[index]).attr('typename') + ',跳过。', 'red')
        setTimeout(() => { doHomeWork(index + 1, TiMuList) }, setting.time)
      }
  }
}

function toNextExam() {
  if (localStorage.getItem('GPTJsSetting.examTurn') === 'true') {
    let $_examtable = $('.mark_table').find('.whiteDiv')
    let $nextbtn = $_examtable.find('.nextDiv a.jb_btn')
    setTimeout(() => {
      $nextbtn.click()
    }, setting.examTurnTime ? 2000 + (Math.floor(Math.random() * 5 + 1) * 1000) : 2000)
  } else {
    logger('用户设置不自动跳转下一题，请手动点击', 'blue')
  }
}

function uploadExam() {
  logger('考试答案收录功能处于bate阶段，遇到bug请及时反馈!!', 'red')
  logger('考试答案收录功能处于bate阶段，遇到bug请及时反馈!!', 'red')
  logger('开始收录考试答案', 'green')
  let TimuList = $('.mark_table .mark_item .questionLi')
  let data = []
  $.each(TimuList, (i, t) => {
    let _a = {}
    let _answer
    let _answerTmpArr, _answerList = []
    let TiMuFull = formatQuestionText($(t).find('h3').html())
    let _type = ({ 单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4 })[TiMuFull.match(/[(](.*?)[)]|$/)[1].replace(/,.*?分/, '')]
    let _question = TiMuFull.replace(/^[(].*?[)]|$/, '').trim()
    let _rightAns = $(t).find('.mark_answer').find('.colorGreen').text().replace(/正确答案[:：]/, '').trim()
    switch (_type) {
      case 0:
        if (_rightAns.length <= 0) {
          _isTrue = $(t).find('.mark_answer').find('.mark_score span').attr('class')
          _isZero = $(t).find('.mark_answer').find('.mark_score .totalScore.fr i').text()
          if (_isTrue == 'marking_dui' || _isZero != '0') {
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
          } else {
            break
          }
        }
        _answerTmpArr = $(t).find('.mark_letter li')
        $.each(_answerTmpArr, (a, b) => {
          _answerList.push(cleanTextContent($(b).html()).replace(/[A-Z].\s*/, ''))
        })
        let _i = ({ A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 })[_rightAns]
        _answer = _answerList[_i]
        _a['question'] = _question
        _a['type'] = _type
        _a['answer'] = _answer
        data.push(_a)
        break
      case 1:
        _answer = []
        if (_rightAns.length <= 0) {
          _isTrue = $(t).find('.mark_answer').find('.mark_score span').attr('class')
          _isZero = $(t).find('.mark_answer').find('.mark_score .totalScore.fr i').text()
          if (_isTrue == 'marking_dui' || _isTrue == 'marking_bandui' || _isZero != '0') {
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
          } else {
            break
          }
        }
        _answerTmpArr = $(t).find('.mark_letter li')
        $.each(_answerTmpArr, (a, b) => {
          _answerList.push(cleanTextContent($(b).html()).replace(/[A-Z].\s*/, ''))
        })
        $.each(_rightAns.split(''), (c, d) => {
          let _i = ({ A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 })[d]
          _answer.push(_answerList[_i])
        })
        _a['question'] = _question
        _a['type'] = _type
        _a['answer'] = _answer.join("#")
        data.push(_a)
        break
      case 2:
        _answerTmpArr = []
        let answers = $(t).find('.mark_answer').find('.colorDeep').find('dd')
        if (_rightAns.length <= 0) {
          $.each(answers, (i, t) => {
            _isTrue = $(t).find('span:eq(1)').attr('class')
            if (_isTrue == 'marking_dui') {
              _rightAns = $(t).find('span:eq(0)').html()
              _answerTmpArr.push(_rightAns.replace(/[(][0-9].*?[)]/, '').replace(/第.*?空:/, '').trim())
            } else {
              return
            }
          })
          _answer = _answerTmpArr.join('#')
        } else {
          _answer = _rightAns.replace(/\s/g, '').replace(/[(][0-9].*?[)]/g, '#').replace(/第.*?空:/g, '#').replace(/^#*/, '')
        }
        if (_answer.length != 0) {
          _a['question'] = _question
          _a['type'] = _type
          _a['answer'] = _answer
          data.push(_a)
        }
        break
      case 3:
        if (_rightAns.length <= 0) {
          _isTrue = $(t).find('.mark_answer').find('.mark_score span').attr('class')
          _isZero = $(t).find('.mark_answer').find('.mark_score .totalScore.fr i').text()
          if (_isTrue == 'marking_dui' || _isZero != '0') {
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
          } else {
            let _true = '正确|是|对|√|T|ri'
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
            if (_true.indexOf(_rightAns) != -1) {
              _rightAns = '错'
            } else {
              _rightAns = '对'
            }
          }
        }
        _a['question'] = _question
        _a['type'] = _type
        _a['answer'] = _rightAns
        data.push(_a)
        break
      case 4:
        if (_rightAns.length <= 0) {
          break
        }
        _a['question'] = _question
        _a['type'] = _type
        _a['answer'] = _rightAns
        data.push(_a)
        break
      default:
        break
    }
  })
  setTimeout(() => { uploadAnswer(data) }, 1500)

}

function refreshCourseList() {
  let _p = parseUrlParams()
  return new Promise((resolve, reject) => {
    $.ajax({
      url: _l.protocol + '//' + _l.host + '/mycourse/studentstudycourselist?courseId=' + _p['courseid'] + '&chapterId=' + _p['knowledgeid'] + '&clazzid=' + _p['clazzid'] + '&mooc2=1',
      type: 'GET',
      dateType: 'html',
      success: function (res) {
        resolve(res)
      }
    })
  })

}

function updateAudio(reportUrl, dtoken, classId, playingTime, duration, clipTime, objectId, otherInfo, jobId, userId, isdrag, _rt) {
  return new Promise((resolve, reject) => {
    getEnc(classId, userId, jobId, objectId, playingTime, duration, clipTime).then((enc) => {
      if (reportUrlChange) {
        reportUrl = http2https(reportUrl)
      }
      $.ajax({
        url: reportUrl + '/' + dtoken + '?clazzId=' + classId + '&playingTime=' + playingTime + '&duration=' + duration + '&clipTime=' + clipTime + '&objectId=' + objectId + '&otherInfo=' + otherInfo + '&jobid=' + jobId + '&userid=' + userId + '&isdrag=' + isdrag + '&view=pc&enc=' + enc + '&rt=' + Number(_rt) + '&dtype=Audio&_t=' + String(Math.round(new Date())),
        type: 'GET',
        success: function (res) {
          try {
            if (res['isPassed']) {
              if (setting.review && playingTime != duration) {
                resolve(1)
              } else {
                resolve(2)
              }
            } else {
              if (setting.rate == 0 && playingTime == duration) {
                resolve(2)
              } else {
                resolve(1)
              }
            }
          } catch (e) {
            logger('发生错误：' + e, 'red')
            resolve(0)
          }
        },
        error: function (xhr) {
          if (xhr.status == 403) {
            logger('超星返回错误信息，尝试更换参数，40s后将重试，请等待...', 'red')
            resolve(3)
          } else {
            reportUrlChange = 1;
            logger('超星返回错误信息，如果持续出现，请联系作者', 'red')
          }
        }
      })
    })
  })
}

function updateVideo(reportUrl, dtoken, classId, playingTime, duration, clipTime, objectId, otherInfo, jobId, userId, isdrag, _rt) {
  return new Promise((resolve, reject) => {
    getEnc(classId, userId, jobId, objectId, playingTime, duration, clipTime).then((enc) => {
      if (reportUrlChange) {
        reportUrl = http2https(reportUrl)
      }
      $.ajax({
        url: reportUrl + '/' + dtoken + '?clazzId=' + classId + '&playingTime=' + playingTime + '&duration=' + duration + '&clipTime=' + clipTime + '&objectId=' + objectId + '&otherInfo=' + otherInfo + '&jobid=' + jobId + '&userid=' + userId + '&isdrag=' + isdrag + '&view=pc&enc=' + enc + '&rt=' + Number(_rt) + '&dtype=Video&_t=' + String(Math.round(new Date())),
        type: 'GET',
        success: function (res) {
          try {
            if (res['isPassed']) {
              if (setting.review && playingTime != duration) {
                resolve(1)
              } else {
                resolve(2)
              }
            } else {
              if (setting.rate == 0 && playingTime == duration) {
                resolve(2)
              } else {
                resolve(1)
              }
            }
          } catch (e) {
            logger('发生错误：' + e, 'red')
            resolve(0)
          }
        },
        error: function (xhr) {
          if (xhr.status == 403) {
            logger('超星返回错误信息，尝试更换参数，40s后将重试，请等待...', 'red')
            resolve(3)
          } else {
            reportUrlChange = 1;
            logger('超星返回错误信息，如果持续出现，请联系作者', 'red')
          }
        }
      })
    })
  })
}

function upLoadWork(index, doms, dom) {
  let $CyHtml = $(dom).contents().find('.CeYan')
  let TiMuList = $CyHtml.find('.TiMu')
  let data = []
  for (let i = 0; i < TiMuList.length; i++) {
    let _a = {}
    let questionFull = $(TiMuList[i]).find('.Zy_TItle.clearfix > div.clearfix').html().trim()
    let _question = formatQuestionText(questionFull)
    let _TimuType = ({ 单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4 })[questionFull.match(/^【(.*?)】|$/)[1]]
    _a['question'] = _question
    _a['type'] = _TimuType
    let _selfAnswerCheck = $(TiMuList[i]).find('.Py_answer.clearfix > i').attr('class')
    switch (_TimuType) {
      case 0:
        if (_selfAnswerCheck == "fr dui") {
          let _selfAnswer = ({ A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 })[$(TiMuList[i]).find('.Py_answer.clearfix > span').html().trim().replace(/正确答案[:：]/, '').replace(/我的答案[:：]/, '').trim()]
          let _answerForm = $(TiMuList[i]).find('.Zy_ulTop li')
          let _answer = $(_answerForm[_selfAnswer]).find('a.fl').html()
          _a['answer'] = cleanTextContent(_answer)
        }
        break
      case 1:
        let _answerArr = $(TiMuList[i]).find('.Py_answer.clearfix > span').html().trim().replace(/正确答案[:：]/, '').replace(/我的答案[:：]/, '').trim()
        let _answerForm = $(TiMuList[i]).find('.Zy_ulTop li')
        let _answer = []
        if (_selfAnswerCheck == "fr dui" || _selfAnswerCheck == "fr bandui") {
          for (let i = 0; i < _answerArr.length; i++) {
            let _answerIndex = ({ A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 })[_answerArr[i]]
            _answer.push($(_answerForm[_answerIndex]).find('a.fl').html())
          }
        } else {
          break
        }
        _a['answer'] = cleanTextContent(_answer.join('#'))
        break
      case 2:
        let _TAnswerArr = $(TiMuList[i]).find('.Py_answer.clearfix .clearfix')
        let _TAnswer = []
        for (let i = 0; i < _TAnswerArr.length; i++) {
          let item = _TAnswerArr[i];
          if ($(item).find('i').attr('class') == 'fr dui') {
            _TAnswer.push($(item).find('p').html().replace(/[(][0-9].*?[)]/, '').replace(/第.*?空:/, '').trim())
          }
        }
        if (_TAnswer.length <= 0) { break }
        _a['answer'] = cleanTextContent(_TAnswer.join('#'))
        break
      case 3:
        if (_selfAnswerCheck == "fr dui") {
          let _answer = $(TiMuList[i]).find('.Py_answer.clearfix > span > i').html().replace(/正确答案[:：]/, '').replace(/我的答案[:：]/, '').trim()
          _a['answer'] = cleanTextContent(_answer)
        } else {
          if ($(TiMuList[i]).find('.Py_answer.clearfix > span > i').html()) {
            let _answer = $(TiMuList[i]).find('.Py_answer.clearfix > span > i').html().replace(/正确答案[:：]/, '').replace(/我的答案[:：]/, '').trim()
            _a['answer'] = (cleanTextContent(_answer) == '√' ? 'x' : '√')
          } else {
            break
          }
        }
        break
      case 4:
        break
    }
    if (_a['answer'] != undefined) {
      data.push(_a)
    } else {
      continue
    }
  }
  uploadAnswer(data).then(() => {
    _mlist.splice(0, 1)
    _domList.splice(0, 1)
    setTimeout(() => { startDoCyWork(index + 1, doms) }, 3000)
  })
}


function uploadHomeWork() {
  logger('开始收录答案', 'green')

  // 增加对不同DOM结构的支持
  let $_homeworktable = $('.mark_table')
  if ($_homeworktable.length === 0) {
    // 尝试其他可能的选择器
    $_homeworktable = $('.mark_cont')
    if ($_homeworktable.length === 0) {
      logger('未找到作业表格，可能是页面结构不匹配', 'red')
      return
    }
  }

  // 增强选择器，适配不同的DOM结构
  let TiMuList = $_homeworktable.find('.mark_item').find('.questionLi')
  if (TiMuList.length === 0) {
    // 尝试直接查找questionLi
    TiMuList = $_homeworktable.find('.questionLi')
    if (TiMuList.length === 0) {
      logger('未找到题目列表，可能是页面结构不匹配', 'red')
      return
    }
  }

  logger(`找到 ${TiMuList.length} 道题目`, 'green')
  let data = []
  $.each(TiMuList, (i, t) => {
    let _a = {}
    let _answer
    let _answerTmpArr, _answerList = []

    // 增强题目选择器
    let titleElem = $(t).find('h3.mark_name')
    if (titleElem.length === 0) {
      titleElem = $(t).find('.mark_name')
      if (titleElem.length === 0) {
        logger(`第${i + 1}题未找到题目标题，跳过`, 'orange')
        return
      }
    }

    let TiMuFull = formatQuestionText(titleElem.html())
    // 更健壮的题型提取逻辑
    let typeMatch = TiMuFull.match(/[(](.*?)[)]|$/)
    let typeText = typeMatch && typeMatch[1] ? typeMatch[1].replace(/, .*?分/, '') : ''
    let TiMuType = ({ 单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4 })[typeText] || 4  // 默认作为简答题处理

    let TiMu = TiMuFull.replace(/^[(].*?[)]|$/, '').trim()

    // 增强答案选择器
    let rightAnsElem = $(t).find('.mark_answer').find('.colorGreen')
    let _rightAns = rightAnsElem.length > 0 ? rightAnsElem.text().replace(/正确答案[:：]/, '').trim() : ''

    switch (TiMuType) {
      case 0:
        if (_rightAns.length <= 0) {
          let _isTrue = $(t).find('.mark_answer').find('.mark_score span').attr('class') || ''
          let _isZero = $(t).find('.mark_answer').find('.mark_score .totalScore.fr i').text() || '0'
          if (_isTrue == 'marking_dui' || _isZero != '0') {
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
          } else {
            return
          }
        }
        _answerTmpArr = $(t).find('.mark_letter li')
        $.each(_answerTmpArr, (a, b) => {
          _answerList.push(cleanTextContent($(b).html()).replace(/[A-Z].\s*/, ''))
        })
        let _i = ({ A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 })[_rightAns]
        if (_i !== undefined && _i < _answerList.length) {
          _answer = _answerList[_i]
          _a['question'] = TiMu
          _a['type'] = TiMuType
          _a['answer'] = _answer
          data.push(_a)
        } else {
          logger(`第${i + 1}题单选题答案提取失败: ${_rightAns}`, 'orange')
        }
        break
      case 1:
        _answer = []
        if (_rightAns.length <= 0) {
          let _isTrue = $(t).find('.mark_answer').find('.mark_score span').attr('class') || ''
          let _isZero = $(t).find('.mark_answer').find('.mark_score .totalScore.fr i').text() || '0'
          if (_isTrue == 'marking_dui' || _isTrue == 'marking_bandui' || _isZero != '0') {
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
          } else {
            break
          }
        }
        _answerTmpArr = $(t).find('.mark_letter li')
        $.each(_answerTmpArr, (a, b) => {
          _answerList.push(cleanTextContent($(b).html()).replace(/[A-Z].\s*/, ''))
        })
        let validLetters = true
        $.each(_rightAns.split(''), (c, d) => {
          let _i = ({ A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 })[d]
          if (_i !== undefined && _i < _answerList.length) {
            _answer.push(_answerList[_i])
          } else {
            validLetters = false
          }
        })
        if (validLetters && _answer.length > 0) {
          _a['question'] = TiMu
          _a['type'] = TiMuType
          _a['answer'] = _answer.join("#")
          data.push(_a)
        } else {
          logger(`第${i + 1}题多选题答案提取失败: ${_rightAns}`, 'orange')
        }
        break
      case 2:
        _answerTmpArr = []
        let answers = $(t).find('.mark_answer').find('.colorDeep').find('dd')
        if (_rightAns.length <= 0) {
          $.each(answers, (i, t) => {
            let _isTrue = $(t).find('span:eq(1)').attr('class') || ''
            if (_isTrue == 'marking_dui') {
              _rightAns = $(t).find('span:eq(0)').html() || ''
              _answerTmpArr.push(_rightAns.replace(/[(][0-9].*?[)]/, '').replace(/第.*?空:/, '').trim())
            } else {
              return
            }
          })
          _answer = _answerTmpArr.join('#')
        } else {
          _answer = _rightAns.replace(/\s/g, '').replace(/[(][0-9].*?[)]/g, '#').replace(/第.*?空:/g, '#').replace(/^#*/, '')
        }
        if (_answer && _answer.length != 0) {
          _a['question'] = TiMu
          _a['type'] = TiMuType
          _a['answer'] = _answer
          data.push(_a)
        } else {
          logger(`第${i + 1}题填空题答案提取失败`, 'orange')
        }
        break
      case 3:
        if (_rightAns.length <= 0) {
          let _isTrue = $(t).find('.mark_answer').find('.mark_score span').attr('class') || ''
          let _isZero = $(t).find('.mark_answer').find('.mark_score .totalScore.fr i').text() || '0'
          if (_isTrue == 'marking_dui' || _isZero != '0') {
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
          } else {
            let _true = '正确|是|对|√|T|ri'
            _rightAns = $(t).find('.mark_answer').find('.colorDeep').text().replace(/我的答案[:：]/, '').trim()
            if (_true.indexOf(_rightAns) != -1) {
              _rightAns = '错'
            } else {
              _rightAns = '对'
            }
          }
        }
        if (_rightAns && _rightAns.length > 0) {
          _a['question'] = TiMu
          _a['type'] = TiMuType
          _a['answer'] = _rightAns
          data.push(_a)
        } else {
          logger(`第${i + 1}题判断题答案提取失败`, 'orange')
        }
        break
      case 4:
        if (_rightAns.length <= 0) {
          // 尝试查找其他可能包含答案的元素
          let altAnsElem = $(t).find('.mark_answer').find('.colorDeep')
          if (altAnsElem.length > 0) {
            _rightAns = altAnsElem.text().replace(/我的答案[:：]/, '').trim()
          }

          if (_rightAns.length <= 0) {
            logger(`第${i + 1}题简答题未找到答案，跳过`, 'orange')
            break
          }
        }
        _a['question'] = TiMu
        _a['type'] = TiMuType
        _a['answer'] = _rightAns
        data.push(_a)
        break
    }
  })

  if (data.length > 0) {
    logger(`成功提取 ${data.length} 道题目答案，准备上传`, 'green')
    setTimeout(() => { uploadAnswer(data) }, 1500)
  } else {
    logger('未能提取到任何题目答案，请检查页面结构或手动收录', 'red')
  }
}

function getEnc(a, b, c, d, e, f, g) {
  // return new Promise((resolve, reject) => {
  //     try {
  //         GM_xmlhttpRequest({
  //             url: "127.0.0.1" + "/api/v1/enc?a=" + a + '&b=' + b + '&c=' + c + '&d=' + d + '&e=' + e + '&f=' + f + '&g=' + g + '&v=' + GM_info['script']['version'],
  //             method: 'GET',
  //             timeout: 3000,
  //             headers: {
  //                 'Authorization': localStorage.getItem('netok')
  //             },
  //             onload: function (xhr) {
  //                 let res = $.parseJSON(xhr.responseText)
  //                 if (res['code'] == 200) {
  //                     enc = res['data']['ne21enc']
  //                     if (enc.length != 32) {
  //                         logger('获取enc出错！' + enc, 'red')
  //                         reject()
  //                     } else {
  //                         resolve(enc)
  //                     }
  //                 } else {
  //                     logger(res['msg'], 'red')
  //                     reject()
  //                 }
  //             }
  //         })
  //     } catch (e) {
  //         logger('获取enc出错！' + e, 'red')
  //         reject()
  //     }
  // })
}


function getAnswer(_t, _q) {
  // 进一步清理题目文本，移除题目类型前缀和分数信息
  let cleanedQuestion = _q;

  // 移除题目类型前缀和分数信息
  cleanedQuestion = cleanedQuestion.replace(/^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：,，]*[\d\.]*分?[\)）\]\】]?\s*/i, '');
  cleanedQuestion = cleanedQuestion.replace(/\(\s*\d+\.\d+\s*分\s*\)/g, '');
  cleanedQuestion = cleanedQuestion.replace(/（\s*\d+\.\d+\s*分\s*）/g, '');

  logger('题目:' + cleanedQuestion, 'pink');

  // 移除正在查询答案的通知
  // showDesktopNotification('正在查询答案', `正在查询题目: ${cleanedQuestion.substring(0, 30)}${cleanedQuestion.length > 30 ? '...' : ''}`);

  return new Promise((resolve, reject) => {
    // 处理特殊格式 "单选题:下面哪一位奖虢季子白盘转赠给国家?() 端方|刘铭传|刘鹗|刘肃曾"
    let processedQuestion = cleanedQuestion;
    let extractedType = _t;

    // 提取题型前缀
    if (_q.startsWith('单选题:')) {
      extractedType = '0';
      processedQuestion = _q.substring(4); // 移除 "单选题:"
      logger('从题目中提取题型: 单选题', 'blue');
    } else if (_q.startsWith('多选题:')) {
      extractedType = '1';
      processedQuestion = _q.substring(4); // 移除 "多选题:"
      logger('从题目中提取题型: 多选题', 'blue');
    } else if (_q.startsWith('判断题:')) {
      extractedType = '3';
      processedQuestion = _q.substring(4); // 移除 "判断题:"
      logger('从题目中提取题型: 判断题', 'blue');
    }

    // 获取用户配置的 key（如果有）
    let userKey = localStorage.getItem('GPTJsSetting.key') || localStorage.getItem('tiku_key') || '';

    // 检查key是否为空
    if (!userKey) {
      logger('未配置Key，请在设置中配置您的Key', 'red');
      reject('请在设置中配置您的Key');
      return;
    }

    // 不再显示使用的key信息
    // logger('使用 Key: ' + userKey.substring(0, 3) + '***' + userKey.substring(userKey.length - 3), 'blue');

    // 检查是否开启题库答题功能
    if (localStorage.getItem('GPTJsSetting.useTiku') !== 'true') {
      logger('题库答题功能已关闭，跳过题库查询', 'orange');

      // 打印当前AI和随机答题设置状态
      logger('题库答题功能状态：' + (localStorage.getItem('GPTJsSetting.useTiku') === 'true' ? '已开启' : '未开启'), '#1890ff');
      logger('AI答题功能状态：' + (localStorage.getItem('GPTJsSetting.useAI') === 'true' ? '已开启' : '未开启'), '#1890ff');
      logger('随机答题功能状态：' + (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true' ? '已开启' : '未开启'), '#1890ff');
      logger('使用模型：' + (localStorage.getItem('GPTJsSetting.model') || 'gpt-3.5-turbo-16k'), '#1890ff');

      // 将题型数字转换为名称
      let typeNames = {
        '0': '单选题',
        '1': '多选题',
        '2': '填空题',
        '3': '判断题',
        '4': '简答题',
        '5': '选择题'
      };
      let typeName = typeNames[extractedType] || '未知题型';
      logger('题目类型: ' + typeName, '#1890ff');

      // 先检查是否开启AI答题功能
      if (localStorage.getItem('GPTJsSetting.useAI') === 'true') {
        // 使用AI尝试获取答案
        logger('已开启AI答题功能，准备获取AI答案...', '#1890ff');
        logger('传递给AI的题型: ' + typeName, '#1890ff');

        // 设置额外的超时保护
        let aiAnswerTimeout = setTimeout(() => {
          logger('AI答题系统响应超时，切换到随机答题...', 'red');
          if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
            // 使用随机答题功能作为备选
            logger('尝试使用随机答题功能作为备选...', '#1890ff');
            const randomAnswer = getRandomAnswer(typeName);
            logger('成功生成随机答案: ' + randomAnswer, 'green');
            resolve(randomAnswer);
          } else {
            // 设置不自动提交
            localStorage.setItem('GPTJsSetting.sub', false);
            // 返回空答案，继续下一题
            resolve('');
          }
        }, 30000); // 30秒后如果AI答题系统仍未返回结果，就切换到随机答题

        getAIAnswer(processedQuestion, typeName)
          .then(aiAnswer => {
            // 清除超时保护
            clearTimeout(aiAnswerTimeout);

            // AI成功获取答案
            logger('AI成功回答，继续处理...', 'green');
            resolve(aiAnswer);
          })
          .catch(error => {
            // 清除超时保护
            clearTimeout(aiAnswerTimeout);

            // AI回答出错，尝试随机答题
            logger('AI回答失败: ' + error, 'red');

            // 检查是否开启随机答题功能
            if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
              // 使用随机答题功能作为备选
              logger('尝试使用随机答题功能作为备选...', '#1890ff');
              const randomAnswer = getRandomAnswer(typeName);
              logger('成功生成随机答案: ' + randomAnswer, 'green');
              resolve(randomAnswer);
            } else {
              // 设置不自动提交
              localStorage.setItem('GPTJsSetting.sub', false);
              // 返回空答案，继续下一题
              resolve('');
            }
          });
        return;
      }

      // 如果AI答题未开启，检查是否开启随机答题功能
      if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
        // 使用随机答题功能
        logger('已开启随机答题功能，准备生成随机答案...', '#1890ff');
        const randomAnswer = getRandomAnswer(typeName);
        logger('成功生成随机答案: ' + randomAnswer, 'green');
        resolve(randomAnswer);
        return;
      }

      // 既没有开启AI答题，也没有开启随机答题
      logger('未开启AI答题和随机答题功能，留空并继续下一题...', 'red');

      // 设置不自动提交
      localStorage.setItem('GPTJsSetting.sub', false);

      // 返回空答案，继续下一题
      resolve('');
      return;
    }

    // 提取选项（如果有）
    let options = '';
    try {
      // 尝试从页面中获取选项
      // 定义可能包含选项的选择器
      const optionSelectors = [
        '.option-content', // 通用选项内容
        '.el-radio__label', // Element UI 单选
        '.el-checkbox__label', // Element UI 多选
        '.ant-radio-wrapper', // Ant Design 单选
        '.ant-checkbox-wrapper', // Ant Design 多选
        'label.option', // 通用选项标签
        '.option-item', // 通用选项项
        '.answer-item', // 答案项
        '.subject-item', // 题目项
        'li.option', // 列表选项
        'div[class*="option"]', // 包含 option 的 div
        'span[class*="option"]', // 包含 option 的 span
        'input[type="radio"] + label', // 单选按钮后的标签
        'input[type="checkbox"] + label' // 复选框后的标签
      ];

      // 合并所有选择器并查询
      const optionsElements = document.querySelectorAll(optionSelectors.join(', '));

      if (optionsElements && optionsElements.length > 0) {
        const optionsArray = [];
        optionsElements.forEach(el => {
          // 清理文本，移除选项标记（如 A.、B.、C. 等）
          let text = el.textContent.trim();
          text = text.replace(/^[A-Z][\.\、\s]+/i, '').trim();

          if (text && !optionsArray.includes(text)) {
            optionsArray.push(text);
          }
        });

        // 如果没有找到选项，尝试从题目中提取
        if (optionsArray.length === 0) {
          // 尝试从题目文本中提取选项
          const questionMatch = _q.match(/\(([^)]+)\)/);
          if (questionMatch && questionMatch[1]) {
            // 提取括号中的选项
            optionsArray.push(...questionMatch[1].split(/[|,，、]/));
          } else {
            // 尝试从题目末尾提取选项
            const endOptionsMatch = _q.match(/[A-D][\.、][\s\S]+?[A-D][\.、][\s\S]+/);
            if (endOptionsMatch) {
              optionsArray.push(...endOptionsMatch[0].split(/[A-D][\.、]/));
            }

            // 尝试查找题目中的选项分隔符
            const splitOptions = _q.split(/[|,，、]/);
            if (splitOptions.length > 1) {
              // 如果分割后有多个部分，可能是选项
              const lastPart = splitOptions[splitOptions.length - 1];
              // 检查最后一部分是否包含选项标记
              if (lastPart.match(/[A-D]/)) {
                optionsArray.push(...splitOptions);
              }
            }
          }
        }

        options = optionsArray.join('|');
        logger('提取到选项: ' + options, 'blue');
      }
    } catch (e) {
      logger('提取选项失败: ' + e, 'red');
    }

    // 处理题目中可能包含的选项
    let extractedOptions = '';

    // 检查题目是否包含选项格式 "题目内容 端方|刘铭传|刘鹗|刘肃曾"
    const optionsSeparatorIndex = processedQuestion.lastIndexOf(' ');
    if (optionsSeparatorIndex !== -1) {
      const possibleOptions = processedQuestion.substring(optionsSeparatorIndex + 1);
      // 检查是否包含选项分隔符
      if (possibleOptions.includes('|')) {
        processedQuestion = processedQuestion.substring(0, optionsSeparatorIndex);
        extractedOptions = possibleOptions;
        logger('从题目中提取选项: ' + extractedOptions, 'blue');
      }
    }

    // 处理特殊格式：题目末尾包含括号和选项
    if (!extractedOptions) {
      const bracketMatch = processedQuestion.match(/\(\)[\s]*([^()]+)$/);
      if (bracketMatch && bracketMatch[1]) {
        const afterBracket = bracketMatch[1].trim();
        if (afterBracket.includes('|')) {
          extractedOptions = afterBracket;
          // 移除括号和选项部分
          processedQuestion = processedQuestion.replace(/\(\)[\s]*[^()]+$/, '()');
          logger('从题目括号后提取选项: ' + extractedOptions, 'blue');
        }
      }
    }



    // 将题型数字转换为名称
    let typeNames = {
      '0': '单选题',
      '1': '多选题',
      '2': '填空题',
      '3': '判断题',
      '4': '简答题',
      '5': '选择题'
    };

    let typeName = typeNames[extractedType] || '未知题型';
    logger('题目类型: ' + typeName, 'green');

    if (extractedOptions) {
      logger('使用题目中提取的选项: ' + extractedOptions, 'green');
    } else if (options) {
      logger('使用页面元素提取的选项: ' + options, 'green');
    }

    // 构建请求数据
    let requestData = "key=" + encodeURIComponent(userKey) +
      "&question=" + encodeURIComponent(processedQuestion) +
      "&type=" + encodeURIComponent(extractedType);

    // 优先使用从题目中提取的选项，其次使用从页面元素提取的选项
    if (extractedOptions) {
      requestData += "&options=" + encodeURIComponent(extractedOptions);
    } else if (options) {
      requestData += "&options=" + encodeURIComponent(options);
    }

    // 不再显示发送请求信息
    // logger('发送请求: ' + requestData, 'gray');

    GM_xmlhttpRequest({
      method: "POST",
      url: API_BASE_URL + "?act=xxt",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": "Bearer " + userKey  // 添加授权头部
      },
      data: requestData,
      timeout: 120000, // 将超时时间从20秒增加到120秒
      onload: function (response) {
        if (response.status == 200) {
          try {
            // 移除服务器响应内容的日志输出
            // logger('服务器响应状态: ' + response.status, 'gray');

            // 检查响应是否为空
            if (!response.responseText) {
              logger('服务器响应内容为空', 'red');
              reject('服务器响应内容为空');
              return;
            }

            let res = JSON.parse(response.responseText);
            // 移除服务器响应内容的日志输出
            // logger('服务器响应码: ' + res.code, 'gray');

            // 首先检查是否有答案（任何状态码）
            if (res.msg && res.msg.includes('未找到答案')) {
              // 如果明确提示未找到答案，尝试使用AI
              logger('题库返回：' + res.msg + '，准备使用AI尝试回答...', 'orange');

              // 打印当前AI设置状态
              logger('题库答题功能状态：' + (localStorage.getItem('GPTJsSetting.useTiku') === 'true' ? '已开启' : '未开启'), '#1890ff');
              logger('AI答题功能状态：' + (localStorage.getItem('GPTJsSetting.useAI') === 'true' ? '已开启' : '未开启'), '#1890ff');
              logger('随机答题功能状态：' + (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true' ? '已开启' : '未开启'), '#1890ff');
              logger('使用模型：' + (localStorage.getItem('GPTJsSetting.model') || 'gpt-3.5-turbo-16k'), '#1890ff');

              // 显示桌面通知
              showDesktopNotification('题库无答案', `题型: ${typeName}\n题目: ${processedQuestion.substring(0, 30)}...`, '');

              // 切换到AI答题
              if (localStorage.getItem('GPTJsSetting.useAI') === 'true') {
                // 使用AI尝试获取答案
                logger('已开启AI答题功能，准备获取AI答案...', '#1890ff');
                getAIAnswer(processedQuestion, typeName)
                  .then(aiAnswer => {
                    // AI成功获取答案
                    logger('AI成功回答，继续处理...', 'green');
                    resolve(aiAnswer);
                  })
                  .catch(error => {
                    // AI回答出错，尝试随机答题
                    logger('AI回答失败: ' + error, 'red');

                    // 检查是否开启随机答题功能
                    if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
                      // 使用随机答题功能作为备选
                      logger('尝试使用随机答题功能作为备选...', '#1890ff');
                      const randomAnswer = getRandomAnswer(typeName);
                      logger('成功生成随机答案: ' + randomAnswer, 'green');
                      resolve(randomAnswer);
                    } else {
                      // 设置不自动提交
                      localStorage.setItem('GPTJsSetting.sub', false);
                      // 返回空答案，继续下一题
                      resolve('');
                    }
                  });
                return;
              } else if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
                // 使用随机答题功能
                logger('未开启AI答题但已开启随机答题，准备生成随机答案...', '#1890ff');
                const randomAnswer = getRandomAnswer(typeName);
                logger('成功生成随机答案: ' + randomAnswer, 'green');
                resolve(randomAnswer);
                return;
              } else {
                // 既没有开启AI答题，也没有开启随机答题
                logger('未开启AI答题和随机答题功能，留空并继续下一题...', 'red');
                // 设置不自动提交
                localStorage.setItem('GPTJsSetting.sub', false);
                // 返回空答案，继续下一题
                resolve('');
                return;
              }
            }

            // 检查是否有错误信息
            if (res.code === 0) {
              logger('错误: ' + res.msg, 'red');
              // 如果是Key验证失败，提示用户设置正确的Key
              if (res.msg.includes('Key验证失败') || res.msg.includes('请提供有效的Key')) {
                logger('请在设置中配置正确的Key', 'red');

                // 显示桌面通知
                showDesktopNotification('Key验证失败', '请检查您的Key是否正确，并在设置中重新配置', '');

                // 显示一个临时提示
                const notification = document.createElement('div');
                notification.textContent = 'Key验证失败，请检查您的Key是否正确';
                notification.style.cssText = `
                  position: fixed;
                  top: 20px;
                  left: 50%;
                  transform: translateX(-50%);
                  background: #F56C6C;
                  color: white;
                  padding: 10px 20px;
                  border-radius: 4px;
                  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
                  z-index: 10000;
                  transition: all 0.3s;
                `;
                document.body.appendChild(notification);
                setTimeout(() => {
                  notification.style.opacity = '0';
                  setTimeout(() => {
                    document.body.removeChild(notification);
                  }, 500);
                }, 3000);
                reject(res.msg);
                return;
              }

              // 未找到答案或题目找到但答案无效的情况，尝试使用AI答题
              if (res.msg.includes('未找到答案') || res.msg.includes('题目找到但答案无效') || res.msg.includes('未找到有效答案')) {
                logger('题库返回: ' + res.msg + '，尝试使用AI答题...', 'orange');

                // 打印当前AI设置状态
                logger('AI答题功能状态：' + (localStorage.getItem('GPTJsSetting.useAI') === 'true' ? '已开启' : '未开启'), '#1890ff');
                logger('随机答题功能状态：' + (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true' ? '已开启' : '未开启'), '#1890ff');

                // 先检查是否开启AI答题功能
                if (localStorage.getItem('GPTJsSetting.useAI') === 'true') {
                  // 使用AI尝试获取答案
                  logger('已开启AI答题功能，准备获取AI答案...', '#1890ff');
                  getAIAnswer(processedQuestion, typeName)
                    .then(aiAnswer => {
                      // AI成功获取答案
                      logger('AI成功回答，继续处理...', 'green');
                      resolve(aiAnswer);
                    })
                    .catch(error => {
                      // AI回答出错，尝试随机答题
                      logger('AI回答失败: ' + error, 'red');

                      // 检查是否开启随机答题功能
                      if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
                        // 使用随机答题功能作为备选
                        logger('尝试使用随机答题功能作为备选...', '#1890ff');
                        const randomAnswer = getRandomAnswer(typeName);
                        logger('成功生成随机答案: ' + randomAnswer, 'green');
                        resolve(randomAnswer);
                      } else {
                        // 设置不自动提交
                        localStorage.setItem('GPTJsSetting.sub', false);
                        // 返回空答案，继续下一题
                        resolve('');
                      }
                    });
                  return;
                } else if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
                  // 使用随机答题功能
                  logger('未开启AI答题但已开启随机答题，准备生成随机答案...', '#1890ff');
                  const randomAnswer = getRandomAnswer(typeName);
                  logger('成功生成随机答案: ' + randomAnswer, 'green');
                  resolve(randomAnswer);
                  return;
                } else {
                  // 既没有开启AI答题，也没有开启随机答题
                  logger('未开启AI答题和随机答题功能，留空并继续下一题...', 'red');
                  // 设置不自动提交
                  localStorage.setItem('GPTJsSetting.sub', false);
                  // 返回空答案，继续下一题
                  resolve('');
                  return;
                }
              }

              // 其他错误情况
              reject(res.msg);
              return;
            }

            // 兼容两种返回格式：code=200 和 code=1000
            if ((res.code == 200 || res.code == 1000) && res.data) {
              // 检查答案是否存在且不为空
              if (res.data.answer !== undefined) {
                // 即使答案是"0"，也视为有效答案
                if (res.data.answer === 0 || res.data.answer === "0" || res.data.answer) {
                  logger('答案:' + res.data.answer, 'pink');
                  resolve(String(res.data.answer)); // 确保转换为字符串
                  return;
                }
              }

              // 如果答案为空或未定义，尝试使用AI
              logger('题库返回答案为空或无效，准备使用AI尝试回答...', 'orange');

              // 打印当前AI设置状态
              logger('AI答题功能状态：' + (localStorage.getItem('GPTJsSetting.useAI') === 'true' ? '已开启' : '未开启'), '#1890ff');
              logger('随机答题功能状态：' + (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true' ? '已开启' : '未开启'), '#1890ff');
              logger('使用模型：' + (localStorage.getItem('GPTJsSetting.model') || 'gpt-3.5-turbo-16k'), '#1890ff');

              // 显示桌面通知
              showDesktopNotification('题库无答案', `题型: ${typeName}\n题目: ${processedQuestion.substring(0, 30)}...`, '');

              // 先检查是否开启AI答题功能
              if (localStorage.getItem('GPTJsSetting.useAI') === 'true') {
                // 使用AI尝试获取答案
                logger('已开启AI答题功能，准备获取AI答案...', '#1890ff');
                getAIAnswer(processedQuestion, typeName)
                  .then(aiAnswer => {
                    // AI成功获取答案
                    logger('AI成功回答，继续处理...', 'green');
                    resolve(aiAnswer);
                  })
                  .catch(error => {
                    // AI回答出错，尝试随机答题
                    logger('AI回答失败: ' + error, 'red');

                    // 检查是否开启随机答题功能
                    if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
                      // 使用随机答题功能作为备选
                      logger('尝试使用随机答题功能作为备选...', '#1890ff');
                      const randomAnswer = getRandomAnswer(typeName);
                      logger('成功生成随机答案: ' + randomAnswer, 'green');
                      resolve(randomAnswer);
                    } else {
                      // 设置不自动提交
                      localStorage.setItem('GPTJsSetting.sub', false);
                      // 返回空答案，继续下一题
                      resolve('');
                    }
                  });
                return;
              }

              // 如果AI答题未开启，检查是否开启随机答题功能
              if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
                // 使用随机答题功能
                logger('已开启随机答题功能，准备生成随机答案...', '#1890ff');
                const randomAnswer = getRandomAnswer(typeName);
                logger('成功生成随机答案: ' + randomAnswer, 'green');
                resolve(randomAnswer);
                return;
              }

              // 既没有开启AI答题，也没有开启随机答题
              logger('未开启AI答题和随机答题功能，留空并继续下一题...', 'red');

              // 设置不自动提交
              localStorage.setItem('GPTJsSetting.sub', false);

              // 返回空答案，继续下一题
              resolve('');
            }
          } catch (e) {
            logger('解析响应出错: ' + e, 'red');

            // 显示桌面通知
            showDesktopNotification('解析响应出错', `错误信息: ${e}`, '');

            reject(e);
          }
        } else {
          logger('请求失败，状态码: ' + response.status, 'red');

          // 显示桌面通知
          showDesktopNotification('请求失败', `状态码: ${response.status}`, '');

          reject('请求失败，状态码: ' + response.status);
        }
      },
      onerror: function (error) {
        logger('请求出错: ' + (error.statusText || '网络错误'), 'red');

        // 移除更详细的错误信息日志
        // logger('具体错误信息: ' + JSON.stringify(error), 'red');

        // 显示桌面通知
        showDesktopNotification('请求出错', `错误信息: ${error.statusText || '网络错误'}`, '');

        reject(error.statusText || '网络错误');
      },
      ontimeout: function () {
        logger('请求超时，服务器响应时间过长', 'red');
        logger('尝试重新连接到服务器...', 'orange');

        // 显示桌面通知
        showDesktopNotification('请求超时', '请求答案超时，正在尝试使用备选方案', '');

        // 尝试使用AI或随机答题作为备选
        if (localStorage.getItem('GPTJsSetting.useAI') === 'true') {
          logger('尝试使用AI回答...', 'orange');
          getAIAnswer(processedQuestion, typeName)
            .then(aiAnswer => {
              logger('AI回答成功，继续处理...', 'green');
              resolve(aiAnswer);
            })
            .catch(error => {
              // AI回答出错，尝试随机答题
              logger('AI回答失败: ' + error, 'red');

              if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
                const randomAnswer = getRandomAnswer(typeName);
                logger('使用随机答案: ' + randomAnswer, 'green');
                resolve(randomAnswer);
              } else {
                reject('请求超时，且备用方案失败');
              }
            });
        } else if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
          const randomAnswer = getRandomAnswer(typeName);
          logger('使用随机答案: ' + randomAnswer, 'green');
          resolve(randomAnswer);
        } else {
          reject('请求超时');
        }
      }
    });
  });
}


function doWork(index, doms, dom) {
  $frame_c = $(dom).contents();
  let $CyHtml = $frame_c.find('.CeYan')
  let TiMuList = $CyHtml.find('.TiMu')
  $subBtn = $frame_c.find(".ZY_sub").find(".btnSubmit");
  $saveBtn = $frame_c.find(".ZY_sub").find(".btnSave");
  startDoWork(index, doms, 0, TiMuList)
}

function startDoWork(index, doms, c, TiMuList) {
  if (c == TiMuList.length) {
    if (localStorage.getItem('GPTJsSetting.sub') === 'true') {
      logger('测验处理完成，准备自动提交。', 'green')
      setTimeout(() => {
        if ($subBtn && $subBtn.length > 0) {
          $subBtn.click()
          setTimeout(() => {
            const confirmBtn = $frame_c.find('#confirmSubWin > div > div > a.bluebtn');
            if (confirmBtn && confirmBtn.length > 0) {
              confirmBtn.click()
              logger('提交成功，准备切换下一个任务。', 'green')
            } else {
              logger('未找到确认按钮，可能已自动确认', 'orange')
            }
            _mlist.splice(0, 1)
            _domList.splice(0, 1)
            setTimeout(() => { startDoCyWork(index + 1, doms) }, 3000)
          }, 3000)
        } else {
          logger('未找到提交按钮，请手动提交', 'red')
          _mlist.splice(0, 1)
          _domList.splice(0, 1)
          setTimeout(() => { startDoCyWork(index + 1, doms) }, 3000)
        }
      }, 5000)
    } else if (localStorage.getItem('GPTJsSetting.force') === 'true') {
      logger('测验处理完成，存在无答案题目,由于用户设置了强制提交，准备自动提交。', 'red')
      setTimeout(() => {
        if ($subBtn && $subBtn.length > 0) {
          $subBtn.click()
          setTimeout(() => {
            const confirmBtn = $frame_c.find('#confirmSubWin > div > div > a.bluebtn');
            if (confirmBtn && confirmBtn.length > 0) {
              confirmBtn.click()
              logger('提交成功，准备切换下一个任务。', 'green')
            } else {
              logger('未找到确认按钮，可能已自动确认', 'orange')
            }
            _mlist.splice(0, 1)
            _domList.splice(0, 1)
            setTimeout(() => { startDoCyWork(index + 1, doms) }, 3000)
          }, 3000)
        } else {
          logger('未找到提交按钮，请手动提交', 'red')
          _mlist.splice(0, 1)
          _domList.splice(0, 1)
          setTimeout(() => { startDoCyWork(index + 1, doms) }, 3000)
        }
      }, 5000)
    } else {
      logger('测验处理完成，存在无答案题目或者用户设置不提交，自动保存！', 'green')
      setTimeout(() => {
        $saveBtn.click()
        setTimeout(() => {
          logger('保存成功，准备切换下一个任务。', 'green')
          _mlist.splice(0, 1)
          _domList.splice(0, 1)
          setTimeout(() => { startDoCyWork(index + 1, doms) }, 3000)
        }, 3000)
      }, 5000)
    }
    return
  }
  let questionFull = $(TiMuList[c]).find('.Zy_TItle.clearfix > div').html()
  questionFull = formatQuestionText(questionFull).replace("/<span.*?>.*?</span>/", "");
  let _question = formatQuestionText(questionFull)
  let _TimuType = { 单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4 }[questionFull.match(/^【(.*?)】|$/)[1]]
  let _a = []
  let _answerTmpArr
  switch (_TimuType) {
    case 0:
      _answerTmpArr = $(TiMuList[c]).find('.Zy_ulTop li').find('a')
      //遍历选项列表
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");
      // 不在发送到API的题目中添加题型前缀，只在日志中显示
      logger("单选题: " + _question + '\n' + mergedAnswers, 'blue');
      // 保存选项信息，但不修改题目本身
      let questionWithOptions = _question + '\n' + mergedAnswers;
      $.each(_answerTmpArr, (i, t) => {
        _a.push(cleanTextContent($(t).html()))
      })
      getAnswer(_TimuType, questionWithOptions).then((agrs) => {
        if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
          //修改题目将答案插入
          let timuele = $(TiMuList[c]).find('.Zy_TItle.clearfix > div')
          timuele.html(timuele.html() + agrs)
        }

        // 检查答案是否为空或无效
        if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案') {
          logger('未获取到有效答案，跳过此题', 'red');
          localStorage.setItem('GPTJsSetting.sub', false);
          setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time);
          return;
        }

        $.each(_answerTmpArr, (i, t) => {
          if (agrs.indexOf(cleanTextContent($(t).html())) != -1) {
            $(_answerTmpArr[i]).parent().click();
            _a.push(['A', 'B', 'C', 'D', 'E', 'F', 'G'][i])
          }
        })
        let id = getStr($(TiMuList[c]).find('.Zy_ulTop li:nth-child(1)').attr('onclick'), 'addcheck(', ');').replace('(', '').replace(')', '')
        if (_a.length <= 0) {
          logger('无法匹配正确答案,请手动选择，跳过', 'red')
          // setting.sub = 0
          localStorage.setItem('GPTJsSetting.sub', false)
        } else {
          $(TiMuList[c]).find('.Zy_ulTop').parent().find('#answer' + id).val(_a.join(""))
        }
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      }).catch((agrs) => {
        logger('答案获取失败，跳过此题', 'red');
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      })
      break
    case 1:
      _answerTmpArr = $(TiMuList[c]).find('.Zy_ulTop li').find('a')
      //遍历选项列表
      var mergedAnswers = [];
      _answerTmpArr.each(function () {
        var answerText = $(this).text().replace(/[ABCD]/g, '').trim();
        mergedAnswers.push(answerText);
      });
      mergedAnswers = mergedAnswers.join("|");
      // 不在发送到API的题目中添加题型前缀，只在日志中显示
      logger("多选题,用\"#\"分割多个答案: " + _question + '\n' + mergedAnswers, 'blue');
      // 保存选项信息，但不修改题目本身
      let multiQuestionWithOptions = _question + '\n' + mergedAnswers;
      getAnswer(_TimuType, multiQuestionWithOptions).then((agrs) => {
        if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
          //修改题目将答案插入
          let timuele = $(TiMuList[c]).find('.Zy_TItle.clearfix > div')
          timuele.html(timuele.html() + agrs)
        }

        // 检查答案是否为空或无效
        if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案') {
          logger('未获取到有效答案，跳过此题', 'red');
          localStorage.setItem('GPTJsSetting.sub', false);
          setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time);
          return;
        }

        // 尝试多种匹配方式
        _a = []; // 清空答案数组

        // 方法1: 直接匹配选项内容
        $.each(_answerTmpArr, (i, t) => {
          const optionText = cleanTextContent($(t).html());
          if (agrs.indexOf(optionText) !== -1) {
            $(_answerTmpArr[i]).parent().click();
            _a.push(['A', 'B', 'C', 'D', 'E', 'F', 'G'][i]);
            logger(`直接匹配到选项 ${['A', 'B', 'C', 'D', 'E', 'F', 'G'][i]}: ${optionText}`, 'green');
          }
        });

        // 方法2: 如果没有匹配到，尝试关键词匹配
        if (_a.length === 0) {
          logger('直接匹配失败，尝试关键词匹配...', 'orange');

          // 将答案拆分为关键词
          const answerKeywords = agrs
            .replace(/[.,，。、；;:：!！?？()（）\[\]【】\s]/g, ' ')
            .replace(/\s+/g, ' ')
            .toLowerCase()
            .split(' ')
            .filter(k => k.length > 1);

          logger(`提取的关键词: ${answerKeywords.join(', ')}`, 'blue');

          // 为每个选项计算匹配分数
          const scores = [];

          $.each(_answerTmpArr, (i, t) => {
            const optionText = cleanTextContent($(t).html()).toLowerCase();
            let matchScore = 0;

            // 计算每个关键词的匹配情况
            for (const keyword of answerKeywords) {
              if (keyword.length < 2) continue;

              if (optionText.includes(keyword)) {
                matchScore += 1;
              } else {
                // 尝试模糊匹配
                for (const word of optionText.split(/\s+/)) {
                  if (word.length < 2) continue;

                  if (calculateSimilarity(word, keyword) > 0.7) {
                    matchScore += 0.5;
                    break;
                  }
                }
              }
            }

            scores.push({
              index: i,
              score: matchScore,
              label: ['A', 'B', 'C', 'D', 'E', 'F', 'G'][i],
              text: optionText
            });
          });

          // 按分数排序
          scores.sort((a, b) => b.score - a.score);

          // 记录匹配结果
          logger('关键词匹配结果:', 'blue');
          scores.forEach(item => {
            logger(`选项 ${item.label}: 得分 ${item.score.toFixed(2)} - ${item.text.substring(0, 20)}...`, 'blue');
          });

          // 选择得分最高的选项(如果有多个相同高分，全选)
          if (scores.length > 0 && scores[0].score > 0) {
            const highestScore = scores[0].score;

            scores.forEach(item => {
              if (item.score >= highestScore * 0.7) { // 选择得分至少为最高分70%的选项
                $(_answerTmpArr[item.index]).parent().click();
                _a.push(item.label);
                logger(`通过关键词匹配选择选项 ${item.label}，得分: ${item.score.toFixed(2)}`, 'green');
              }
            });
          }
        }

        // 方法3: 如果前两种方法都失败，尝试相似度匹配
        if (_a.length === 0) {
          logger('关键词匹配失败，尝试相似度匹配...', 'orange');

          const similarities = [];

          $.each(_answerTmpArr, (i, t) => {
            const optionText = cleanTextContent($(t).html());
            const similarity = calculateSimilarity(optionText, agrs);

            similarities.push({
              index: i,
              similarity: similarity,
              label: ['A', 'B', 'C', 'D', 'E', 'F', 'G'][i],
              text: optionText
            });
          });

          // 按相似度排序
          similarities.sort((a, b) => b.similarity - a.similarity);

          // 记录相似度结果
          logger('相似度匹配结果:', 'blue');
          similarities.forEach(item => {
            logger(`选项 ${item.label}: 相似度 ${item.similarity.toFixed(2)} - ${item.text.substring(0, 20)}...`, 'blue');
          });

          // 选择相似度最高的选项(如果有多个相似度高的，全选)
          if (similarities.length > 0 && similarities[0].similarity > 0.4) {
            const highestSimilarity = similarities[0].similarity;

            similarities.forEach(item => {
              if (item.similarity >= Math.max(0.4, highestSimilarity * 0.7)) { // 选择相似度至少为0.4或最高相似度70%的选项
                $(_answerTmpArr[item.index]).parent().click();
                _a.push(item.label);
                logger(`通过相似度匹配选择选项 ${item.label}，相似度: ${item.similarity.toFixed(2)}`, 'green');
              }
            });
          }
        }

        let id = getStr($(TiMuList[c]).find('.Zy_ulTop li:nth-child(1)').attr('onclick'), 'addcheck(', ');').replace('(', '').replace(')', '')
        if (_a.length <= 0) {
          logger('所有匹配策略均失败，无法确定正确选项，请手动选择，跳过此题', 'red')
          localStorage.setItem('GPTJsSetting.sub', false)
        } else {
          $(TiMuList[c]).find('.Zy_ulTop').parent().find('#answer' + id).val(_a.join(""))
        }
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      }).catch((agrs) => {
        logger('答案获取失败，跳过此题', 'red');
        localStorage.setItem('GPTJsSetting.sub', false);
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      })
      break
    case 2:
      let _textareaList = $(TiMuList[c]).find('.Zy_ulTk .XztiHover1')
      getAnswer(_TimuType, _question).then((agrs) => {
        if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
          //修改题目将答案插入
          let timuele = $(TiMuList[c]).find('.Zy_TItle.clearfix > div')
          timuele.html(timuele.html() + agrs)
        }
        let _answerList = agrs.split("#")
        $.each(_textareaList, (i, t) => {
          setTimeout(() => {
            $(t).find('#ueditor_' + i).contents().find('.view p').html(_answerList[i]);
            $(t).find('textarea').html('<p>' + _answerList[i] + '</p>')
          }, 300)
        })
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      }).catch((agrs) => {
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      })
      break
    case 3:
      _answerTmpArr = $(TiMuList[c]).find(".Zy_ulTop li").find("a");
      let _true = "正确|是|对|√|T|ri";
      $.each(_answerTmpArr, (i, t) => {
        _a.push(cleanTextContent($(t).html()));
      });
      // 不在发送到API的题目中添加题型前缀，只在日志中显示
      logger("判断题，只回答正确或错误: " + _question, 'blue');
      getAnswer(_TimuType, _question).then((agrs) => {
        if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
          //修改题目将答案插入
          let timuele = $(TiMuList[c]).find('.Zy_TItle.clearfix > div')
          timuele.html(timuele.html() + agrs)
        }

        // 检查答案是否为空或无效
        if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案') {
          logger('未获取到有效答案，跳过此题', 'red');
          localStorage.setItem('GPTJsSetting.sub', false);
          setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time);
          return;
        }

        agrs = _true.indexOf(agrs) != -1 ? "对" : "错";
        let _i = _a.findIndex((item) => item == agrs);
        if (_i == -1) {
          logger("未匹配到正确答案，跳过", "red");
          localStorage.setItem('GPTJsSetting.sub', false)
        } else {
          $(_answerTmpArr[_i]).parent().click();
        }
        setTimeout(() => {
          startDoWork(index, doms, c + 1, TiMuList);
        }, setting.time);
      }).catch((agrs) => {
        logger('答案获取失败，跳过此题', 'red');
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      })
      break;
    case 4:
      let _textareaLista = $(TiMuList[c]).find('.Zy_ulTk .XztiHover1')
      getAnswer(_TimuType, _question).then((agrs) => {
        if (agrs == '暂无答案') {
          // setting.sub = 0
          localStorage.setItem('GPTJsSetting.sub', false)
        }
        let _answerList = agrs.split("#")
        $.each(_textareaLista, (i, t) => {
          setTimeout(() => {
            $(t).find('#ueditor_' + i).contents().find('.view p').html(_answerList[i]);
            $(t).find('textarea').html('<p>' + _answerList[i] + '</p>')
          }, 300)
        })
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      }).catch((agrs) => {
        setTimeout(() => { startDoWork(index, doms, c + 1, TiMuList) }, setting.time)
      })
      break
  }
}

function uploadAnswer(a) {
  return new Promise((resolve, reject) => {
    GM_xmlhttpRequest({
      url: API_BASE_URL + '/api/v1/save?v=' + GM_info['script']['version'],
      data: 'data=' + encodeURIComponent(JSON.stringify(a)),
      method: 'POST',
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      onload: function (xhr) {
        try {
          let res = $.parseJSON(xhr.responseText)
          if (res['code'] == 200) {
            logger('答案收录成功！！此次收录' + res['data']['total'] + '道题目，准备处理下一个任务。', 'green')
          } else {
            logger('答案收录失败了，请向作者反馈，准备处理下一个任务。', 'red')
          }
          resolve()
        } catch {
          let res = xhr.responseText
          if (res.indexOf('防火墙') != -1) {
            logger('答案收录失败了，已被防火墙拦截，请联系作者手动收录。', 'red')
          } else {
            logger('答案收录失败了，未知错误，请向作者反馈。', 'red')
          }
          resolve()
        }
      }
    })

  })

}

function switchMission() {
  if (_mlist.length > 0) {
    let taskName = '';
    try {
      taskName = _mlist[0]['property']['name'] || '任务点';
    } catch (e) {
      taskName = '任务点';
    }
    // 添加桌面通知
    showDesktopNotification('完成一个任务点', `已完成: ${taskName}`, '');
  } else {
    // 添加桌面通知
    showDesktopNotification('没有任务点', '当前页面没有可处理的任务点', '');
  }

  _mlist.splice(0, 1)
  _domList.splice(0, 1)
  // 修改这一行，调用正确的函数
  setTimeout(initializeTaskSystem, 5000)
}

// 添加missonStart作为别名，兼容旧版调用
function missonStart() {
  // 直接调用正确的函数
  logger('通过missonStart别名调用initializeTaskSystem', 'blue');
  try {
    initializeTaskSystem();
  } catch (e) {
    logger('通过missonStart调用initializeTaskSystem失败: ' + e, 'red');
    // 恢复任务执行的基本逻辑
    showBox();
    if (_mlist.length <= 0) {
      // 添加桌面通知
      showDesktopNotification('任务点完成', '此页面所有任务点已处理完毕，准备跳转页面', '');
      logger('此页面任务处理完毕，准备跳转页面', 'green');
      return toNext();
    }
    // 处理剩余的任务...
    let _type = _mlist[0]['type'],
      _dom = _domList[0],
      _task = _mlist[0];
    if (_type == undefined) {
      _type = _mlist[0]['property']["module"];
    }
    // 简要逻辑，确保出错情况下也能继续执行
    logger('尝试继续执行任务: ' + _type, 'orange');
    setTimeout(() => { switchMission(); }, 5000);
  }
}

function cleanTextContent(textString) {
  if (!textString) return null;

  // 移除HTML标签（保留img标签）
  let cleaned = textString.replace(/<(?!img).*?>/g, "");

  // 移除其他特定格式
  cleaned = cleaned
    .replace(/^【.*?】\s*/, '') // 移除开头的【xxx】
    .replace(/\s*（\d+\.\d+分）$/, '') // 移除分数
    .replace(/\s*\(\d+\.\d+分\)$/, '') // 移除分数(另一种格式)
    .replace(/\s*[\(（].*?分[\)）]/, '') // 移除任何包含"分"的括号内容
    .replace(/&nbsp;/g, '') // 移除HTML空格
    .replace(new RegExp("&nbsp;", ("gm")), '')
    .replace(/\s*\([\d\.]+\)\s*$/, '') // 移除末尾的(5.0)等
    .replace(/\s*（[\d\.]+）\s*$/, '') // 移除末尾的（5.0）等
    .replace(/^\s*\d+[\.\、]\s*/, '') // 移除开头的题号
    .replace(/^\s*[\(（].*?[\)）]\s*/, '') // 移除开头的括号内容
    .trim() // 去除首尾空格
    .replace(/^\s+/, '')
    .replace(/\s+$/, '');

  return cleaned;
}

function formatQuestionText(questionText) {
  if (!questionText) return null;

  // 先进行基础清理
  let formatted = cleanTextContent(questionText);

  // 额外处理针对问题特有的格式
  formatted = formatted
    .replace(/^\d+[\.、]/, '') // 移除题号
    .replace(/^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：,，]*[\d\.]*分?[\)）\]\】]?\s*/i, '') // 移除题目类型信息
    .replace(/\(\s*\d+\.\d+\s*分\s*\)/g, '') // 移除分数信息 (5.0分)
    .replace(/（\s*\d+\.\d+\s*分\s*）/g, '') // 移除分数信息 （5.0分）
    .replace('javascript:void(0);', '') // 移除JS链接
    .trim(); // 确保去除首尾空格

  return formatted;
}

/**
 * 计算两个字符串的相似度
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @returns {number} - 相似度，范围0-1
 */
function calculateSimilarity(str1, str2) {
  // 如果任一字符串为空，返回0
  if (!str1 || !str2) {
    return 0;
  }

  // 预处理字符串：转换为小写，去除空格和标点符号
  const cleanStr = (s) => String(s)
    .toLowerCase()
    .replace(/[.,，。、；;:：!！?？()（）\[\]【】\s]/g, '')
    .replace(/[""'']/g, '');

  const s1 = cleanStr(str1);
  const s2 = cleanStr(str2);

  // 如果完全相同，返回1
  if (s1 === s2) {
    return 1;
  }

  // 如果一个字符串完全包含另一个，返回较高的相似度
  if (s1.includes(s2)) {
    return 0.9 * (s2.length / s1.length);
  }

  if (s2.includes(s1)) {
    return 0.9 * (s1.length / s2.length);
  }

  // 计算关键词匹配
  const keywords1 = s1.split(/\d+/).filter(k => k.length > 1);
  const keywords2 = s2.split(/\d+/).filter(k => k.length > 1);

  let keywordMatches = 0;
  let totalKeywords = keywords1.length;

  for (const k1 of keywords1) {
    if (k1.length < 2) continue; // 忽略太短的关键词

    for (const k2 of keywords2) {
      if (k2.length < 2) continue;

      if (k1.includes(k2) || k2.includes(k1) ||
        levenshteinDistance(k1, k2) / Math.max(k1.length, k2.length) < 0.3) {
        keywordMatches++;
        break;
      }
    }
  }

  const keywordScore = totalKeywords > 0 ? keywordMatches / totalKeywords : 0;

  // 计算公共字符
  const len1 = s1.length;
  const len2 = s2.length;

  // 如果长度差异太大，降低相似度
  if (Math.abs(len1 - len2) > Math.min(len1, len2)) {
    return keywordScore * 0.7;
  }

  // 计算公共字符数量
  let commonChars = 0;
  for (let i = 0; i < len1; i++) {
    if (s2.includes(s1[i])) {
      commonChars++;
    }
  }

  const charScore = commonChars / Math.max(len1, len2);

  // 计算连续匹配的子串
  let longestSubstring = findLongestCommonSubstring(s1, s2);
  const substringScore = longestSubstring.length > 1 ?
    longestSubstring.length / Math.min(len1, len2) : 0;

  // 综合评分 (加权平均)
  return Math.max(
    keywordScore * 0.5 + charScore * 0.3 + substringScore * 0.2,
    keywordScore * 0.7,
    substringScore * 0.8
  );
}

/**
 * 计算两个字符串的编辑距离
 */
function levenshteinDistance(str1, str2) {
  const m = str1.length;
  const n = str2.length;

  // 创建距离矩阵
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  // 初始化第一行和第一列
  for (let i = 0; i <= m; i++) dp[i][0] = i;
  for (let j = 0; j <= n; j++) dp[0][j] = j;

  // 填充矩阵
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1];
      } else {
        dp[i][j] = Math.min(
          dp[i - 1][j] + 1,   // 删除
          dp[i][j - 1] + 1,   // 插入
          dp[i - 1][j - 1] + 1  // 替换
        );
      }
    }
  }

  return dp[m][n];
}

/**
 * 查找两个字符串的最长公共子串
 */
function findLongestCommonSubstring(str1, str2) {
  const m = str1.length;
  const n = str2.length;
  let maxLength = 0;
  let endIndex = 0;

  // 创建表格
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  // 填充表格
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;
        if (dp[i][j] > maxLength) {
          maxLength = dp[i][j];
          endIndex = i - 1;
        }
      }
    }
  }

  return maxLength > 0 ? str1.substring(endIndex - maxLength + 1, endIndex + 1) : "";
}

function convertEncryptedFont() {
  /**
  * Author   wyn665817
  * From     https://greasyfork.org/zh-CN/scripts/445007
  */
  var $tip = $('style:contains(font-cxsecret)');
  if (!$tip.length) return;
  var font = $tip.text().match(/base64,([\w\W]+?)'/)[1];
  font = Typr.parse(convertBase64ToArray(font))[0];
  var table = JSON.parse(GM_getResourceText('Table'));
  var match = {};
  for (var i = 19968; i < 40870; i++) {
    $tip = Typr.U.codeToGlyph(font, i);
    if (!$tip) continue;
    $tip = Typr.U.glyphToPath(font, $tip);
    $tip = md5(JSON.stringify($tip)).slice(24);
    match[i] = table[$tip];
  }
  $('.font-cxsecret').html(function (index, html) {
    $.each(match, function (key, value) {
      key = String.fromCharCode(key);
      key = new RegExp(key, 'g');
      value = String.fromCharCode(value);
      html = html.replace(key, value);
    });
    return html;
  }).removeClass('font-cxsecret');
}

function convertBase64ToArray(base64) {
  var data = window.atob(base64);
  var buffer = new Uint8Array(data.length);
  for (var i = 0; i < data.length; ++i) {
    buffer[i] = data.charCodeAt(i);
  }
  return buffer;
}

// 添加拖拽功能
$(document).ready(function () {
  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;
  let xOffset = 0;
  let yOffset = 0;

  // 保存位置到localStorage
  function savePosition() {
    const box = document.getElementById('ne-21box');
    if (box) {
      const position = {
        left: parseInt(box.style.left) || 20,
        top: parseInt(box.style.top) || 5
      };
      localStorage.setItem('GPTJsSetting.boxPosition', JSON.stringify(position));
    }
  }

  // 从localStorage加载位置
  function loadPosition() {
    const box = document.getElementById('ne-21box');
    if (box) {
      // 尝试从localStorage读取位置
      let storedPosition = localStorage.getItem('GPTJsSetting.boxPosition');
      let position = { left: 20, top: 5 }; // 默认位置

      if (storedPosition) {
        try {
          position = JSON.parse(storedPosition);
        } catch (e) {
          console.error('无法解析存储的位置信息');
        }
      }

      // 直接设置位置而不是使用transform
      box.style.right = 'auto'; // 取消right的影响
      box.style.left = position.left + 'px';
      box.style.top = position.top + 'px';
    }
  }

  // 更新拖拽事件绑定
  $(document).on('mousedown', '.ne-header', function (e) {
    isDragging = true;
    const box = document.getElementById('ne-21box');

    // 记录初始点击位置与盒子位置的偏移
    initialX = e.clientX - (parseInt(box.style.left) || 0);
    initialY = e.clientY - (parseInt(box.style.top) || 0);

    if (e.target === box) {
      box.style.cursor = 'move';
    }
  });

  $(document).on('mousemove', function (e) {
    if (isDragging) {
      e.preventDefault();

      // 计算新位置
      const newLeft = e.clientX - initialX;
      const newTop = e.clientY - initialY;

      // 确保不超出屏幕边界
      const box = document.getElementById('ne-21box');
      const maxX = window.innerWidth - box.offsetWidth;
      const maxY = window.innerHeight - box.offsetHeight;

      // 更新位置，使用直接的top和left属性
      box.style.right = 'auto'; // 取消right属性的影响
      box.style.left = Math.max(0, Math.min(newLeft, maxX)) + 'px';
      box.style.top = Math.max(0, Math.min(newTop, maxY)) + 'px';
    }
  });

  $(document).on('mouseup', function (e) {
    if (isDragging) {
      isDragging = false;
      const box = document.getElementById('ne-21box');
      if (box) {
        box.style.cursor = ''; // 恢复默认鼠标样式
      }
      savePosition();
    }
  });

  // 页面加载时恢复位置
  setTimeout(loadPosition, 1000);
});

// 设置题库 API Key
function setTikuKey() {
  let currentKey = localStorage.getItem('GPTJsSetting.key') || '';
  let newKey = prompt('请输入您的题库 API Key:', currentKey);

  if (newKey !== null) {
    if (newKey.trim() === '') {
      alert('请输入Key');
    } else {
      // 发送API请求验证key
      GM_xmlhttpRequest({
        url: API_BASE_URL + "?act=verify_key",
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        },
        data: "key=" + encodeURIComponent(newKey.trim()),
        onload: function (response) {
          try {
            const result = JSON.parse(response.responseText);
            if (result.code === 1) {
              // 验证成功，保存key
              localStorage.setItem('GPTJsSetting.key', newKey.trim());
              localStorage.setItem('tiku_key', newKey.trim());

              // 更新输入框的值
              if (document.getElementById('GPTJsSetting.key')) {
                document.getElementById('GPTJsSetting.key').value = newKey.trim();
              }

              alert('API Key 保存成功！');
              showDesktopNotification('API Key 保存成功！', '您的API Key已成功保存', '');
            } else {
              alert(result.msg || 'Key验证失败');
              showDesktopNotification('Key验证失败', result.msg || 'Key验证失败', '');
            }
          } catch (e) {
            alert('验证请求失败，请稍后重试');
          }
        },
        onerror: function () {
          alert('验证请求失败，请检查网络连接');
        }
      });
    }
  }
}

// 在页面加载时添加设置按钮
function addTikuKeyButton() {
  // 防止重复添加按钮
  if (document.querySelector('.tiku-settings-btn')) {
    return;
  }

  // 获取当前配置的 key
  const currentKey = localStorage.getItem('tiku_key');
  const isDefaultKey = !localStorage.getItem('tiku_key');

  // 创建设置按钮容器
  const settingsContainer = document.createElement('div');
  // 检查界面是否应该隐藏，如果隐藏则显示状态指示器
  const shouldHideBox = localStorage.getItem('GPTJsSetting.showBox') === 'hide';
  const containerDisplay = shouldHideBox ? 'flex' : 'none';

  settingsContainer.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: ${containerDisplay};
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    z-index: 9999;
  `;

  // 创建状态指示器
  const statusIndicator = document.createElement('div');
  statusIndicator.textContent = isDefaultKey ? '未配置' : 'F9显示面板';
  statusIndicator.style.cssText = `
    background: ${isDefaultKey ? '#FC3D74' : '#FC3D74'};
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s;
  `;

  // 创建设置按钮
  const settingsBtn = document.createElement('div');
  settingsBtn.className = 'tiku-settings-btn'; // 添加类名
  settingsBtn.innerHTML = '🔎';
  settingsBtn.style.cssText = `
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #20e5fe;
    color: white;
    border-radius: 50%;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
  `;

  // 添加悬停效果
  settingsBtn.onmouseover = function () {
    this.style.transform = 'scale(1.1)';
    this.style.boxShadow = '0 4px 12px 0 rgba(0,0,0,0.2)';
    statusIndicator.style.opacity = '1';
    statusIndicator.style.transform = 'translateY(0)';
  };

  settingsBtn.onmouseout = function () {
    this.style.transform = 'scale(1)';
    this.style.boxShadow = '0 2px 12px 0 rgba(0,0,0,0.1)';
    statusIndicator.style.opacity = '0';
    statusIndicator.style.transform = 'translateY(10px)';
  };

  // 点击事件
  settingsBtn.onclick = function () {
    // 检查ne-21box是否存在
    let neBox = document.getElementById('ne-21box');
    if (!neBox) {
      // 如果不存在，先调用showBox创建
      showBox();
      neBox = document.getElementById('ne-21box');
    }

    // 切换ne-21box的显示状态
    if (neBox) {
      let show = neBox.style.display === 'none' || neBox.style.display === '';
      neBox.style.display = show ? 'block' : 'none';

      // 同步更新localStorage状态，确保页面跳转后保持隐藏状态
      localStorage.setItem('GPTJsSetting.showBox', show ? 'show' : 'hide');

      // 当显示ne-21box时，隐藏状态指示器
      settingsContainer.style.display = show ? 'none' : 'flex';
    }
  };

  // 组装并添加到页面
  settingsContainer.appendChild(statusIndicator);
  settingsContainer.appendChild(settingsBtn);
  document.body.appendChild(settingsContainer);

  // 添加F9快捷键功能
  document.addEventListener('keydown', function (e) {
    if (e.key === 'F9') {
      // 阻止默认行为
      e.preventDefault();

      // 检查ne-21box是否存在
      let neBox = document.getElementById('ne-21box');
      if (!neBox) {
        // 如果不存在，先调用showBox创建
        showBox();
        neBox = document.getElementById('ne-21box');
      }

      // 切换ne-21box的显示状态
      if (neBox) {
        let show = neBox.style.display === 'none' || neBox.style.display === '';
        neBox.style.display = show ? 'block' : 'none';

        // 同步更新localStorage状态，确保页面跳转后保持隐藏状态
        localStorage.setItem('GPTJsSetting.showBox', show ? 'show' : 'hide');

        // 当显示ne-21box时，隐藏状态指示器
        settingsContainer.style.display = show ? 'none' : 'flex';
      }
    }
  });

  // 不再显示Key信息
  // const keyValue = localStorage.getItem('GPTJsSetting.key') || '';
  // if (keyValue) {
  //   logger('题库已配置，Key: ' + keyValue.substring(0, 3) + '***' + keyValue.substring(keyValue.length - 3), 'green');
  // } else {
  //   logger('请在设置中配置您的Key', 'orange');
  // }
}

// 在页面加载时添加设置按钮
window.addEventListener('load', function () {
  setTimeout(addTikuKeyButton, 1000);
});

// 添加初始化函数：

// 在页面加载完成后添加设置按钮和初始化配置
window.addEventListener('load', function () {
  // 初始化题库配置
  initTikuConfig();
  // 添加设置按钮
  setTimeout(addTikuKeyButton, 1000);
});

// 初始化题库配置
function initTikuConfig() {
  // 防止重复初始化
  if (window.tikuInitialized) {
    return;
  }
  window.tikuInitialized = true;

  // 获取当前Key
  let currentKey = localStorage.getItem('GPTJsSetting.key') || '';

  // 如果没有设置过 key，则同步tiku_key为空
  if (!localStorage.getItem('tiku_key') && currentKey) {
    localStorage.setItem('tiku_key', currentKey);
  } else if (localStorage.getItem('tiku_key') && !currentKey) {
    // 如果有tiku_key但没有GPTJsSetting.key，则同步
    localStorage.setItem('GPTJsSetting.key', localStorage.getItem('tiku_key'));
    currentKey = localStorage.getItem('tiku_key');
  }

  // 添加题库信息到页面
  // 不再显示题库连接信息
  /*
  setTimeout(() => {
    const tikuInfo = document.createElement('div');
    tikuInfo.innerHTML = `
      <div style="position: fixed; top: 10px; right: 10px; background: rgba(255, 255, 255, 0.9); 
                  padding: 8px 12px; border-radius: 4px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); 
                  font-size: 12px; color: #666; z-index: 9998; display: flex; align-items: center;">
        <span style="color: #409EFF; margin-right: 5px;">✓</span> 
        题库已连接 (Key: ${currentKey.substring(0, 3)}***)
      </div>
    `;
 
    // 3秒后自动隐藏
    setTimeout(() => {
      if (document.body.contains(tikuInfo)) {
        tikuInfo.style.opacity = '0';
        tikuInfo.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          if (document.body.contains(tikuInfo)) {
            document.body.removeChild(tikuInfo);
          }
        }, 500);
      }
    }, 3000);
 
    document.body.appendChild(tikuInfo);
  }, 2000);
  */

  // 为现有的gpt-box添加通知开关按钮
  addNotificationToggleToGptBox();
}

// 添加显示桌面通知的函数
function showDesktopNotification(title, message, icon = '') {
  // 检查通知是否启用
  if (localStorage.getItem('GPTJsSetting.notification') === 'false') {
    return;
  }

  // 检查浏览器是否支持通知
  if (!("Notification" in window)) {
    logger('您的浏览器不支持桌面通知', 'red');
    return;
  }

  // 设置默认图标
  const defaultIcon = 'https://mx.mixuelo.cc/index/pengzi/images/思考2.gif';
  const notificationIcon = icon || defaultIcon;

  // 检查通知权限
  if (Notification.permission === "granted") {
    // 发送通知
    new Notification(title, {
      body: message,
      icon: notificationIcon
    });
  } else if (Notification.permission !== "denied") {
    // 请求通知权限
    Notification.requestPermission().then(function (permission) {
      if (permission === "granted") {
        // 发送通知
        new Notification(title, {
          body: message,
          icon: notificationIcon
        });
      } else {
        logger('您拒绝了通知权限', 'red');
      }
    });
  }
}

// 添加测试通知功能
function testNotification() {
  showDesktopNotification('通知测试', '如果您看到这条消息，说明通知功能正常工作！', '');
}

// 修改通知开关的事件监听
$('#GPTJsSetting\\.notification').change(function () {
  localStorage.setItem('GPTJsSetting.notification', this.checked);

  // 显示状态变更提示
  const saveKeyMsg = document.getElementById('saveKeyMsg');
  saveKeyMsg.innerText = this.checked ? '桌面通知已开启' : '桌面通知已关闭';
  saveKeyMsg.style.backgroundColor = this.checked ? '#4CAF50' : '#FF9800';
  saveKeyMsg.style.display = 'block';

  // 使用setTimeout创建动画效果
  setTimeout(function () {
    saveKeyMsg.style.opacity = '1';
    saveKeyMsg.style.transform = 'translateY(0)';
  }, 10);

  // 如果开启通知，发送测试通知
  if (this.checked) {
    testNotification();
  }

  // 3秒后隐藏提示
  setTimeout(function () {
    saveKeyMsg.style.opacity = '0';
    saveKeyMsg.style.transform = 'translateY(-10px)';
    setTimeout(function () {
      saveKeyMsg.style.display = 'none';
    }, 300);
  }, 3000);
});

// 为gpt-box添加通知开关按钮
function addNotificationToggleToGptBox() {
  // 获取所有gpt-box元素
  const gptBoxes = document.querySelectorAll('.gpt-box');

  // 为每个gpt-box添加通知开关按钮
  gptBoxes.forEach(box => {
    // 检查是否已经添加过按钮
    if (box.querySelector('.notification-toggle-btn')) {
      return;
    }

    // 创建通知开关按钮
    const toggleBtn = document.createElement('div');
    toggleBtn.className = 'notification-toggle-btn';

    // 获取当前通知状态
    const notificationEnabled = localStorage.getItem('GPTJsSetting.notification') !== 'false';

    // 设置按钮样式和内容
    toggleBtn.innerHTML = notificationEnabled ?
      '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path></svg>' :
      '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path><line x1="1" y1="1" x2="23" y2="23"></line></svg>';

    toggleBtn.style.cssText = `
      position: absolute;
      top: 5px;
      right: 5px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: ${notificationEnabled ? '#4CAF50' : '#FF9800'};
      color: white;
      border-radius: 50%;
      cursor: pointer;
      font-size: 12px;
      z-index: 1000;
      opacity: 0.7;
      transition: all 0.3s ease;
    `;

    // 添加鼠标悬停效果
    toggleBtn.addEventListener('mouseover', () => {
      toggleBtn.style.opacity = '1';
    });

    toggleBtn.addEventListener('mouseout', () => {
      toggleBtn.style.opacity = '0.7';
    });

    // 添加点击事件
    toggleBtn.addEventListener('click', () => {
      // 切换通知状态
      const currentStatus = localStorage.getItem('GPTJsSetting.notification') !== 'false';
      localStorage.setItem('GPTJsSetting.notification', !currentStatus);

      // 更新按钮样式和内容
      toggleBtn.innerHTML = !currentStatus ?
        '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path></svg>' :
        '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path><line x1="1" y1="1" x2="23" y2="23"></line></svg>';

      toggleBtn.style.backgroundColor = !currentStatus ? '#4CAF50' : '#FF9800';

      // 同步更新设置面板中的复选框
      const checkbox = document.getElementById('GPTJsSetting.notification');
      if (checkbox) {
        checkbox.checked = !currentStatus;
      }

      // 显示提示信息
      const message = !currentStatus ? '桌面通知已开启' : '桌面通知已关闭';

      // 创建临时提示元素
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${!currentStatus ? '#4CAF50' : '#FF9800'};
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        z-index: 10000;
        transition: all 0.3s;
      `;
      document.body.appendChild(notification);

      // 3秒后自动隐藏提示
      setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 500);
      }, 3000);
    });

    // 将按钮添加到gpt-box中
    box.style.position = 'relative';
    box.appendChild(toggleBtn);
  });
}

// 监听DOM变化，为新添加的gpt-box添加通知开关按钮
function setupNotificationToggleObserver() {
  // 创建一个MutationObserver实例
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes && mutation.addedNodes.length > 0) {
        // 检查新添加的节点中是否有gpt-box
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1 && node.classList && node.classList.contains('gpt-box')) {
            // 为新添加的gpt-box添加通知开关按钮
            addNotificationToggleToGptBox();
          }
        });
      }
    });
  });

  // 配置观察选项
  const config = { childList: true, subtree: true };

  // 开始观察document.body的变化
  observer.observe(document.body, config);

  // 初始化时也执行一次
  addNotificationToggleToGptBox();
}

// 在页面加载完成后设置观察者
window.addEventListener('load', setupNotificationToggleObserver);

// 在初始化函数中也调用一次
// function initTikuConfig() {
//   // ... existing code ...

//   // 为现有的gpt-box添加通知开关按钮
//   addNotificationToggleToGptBox();

//   // ... existing code ...
// }

// 为 AI 功能添加事件监听器
document.getElementById('ai-send-btn').addEventListener('click', getAiAnswer);
document.getElementById('ai-copy-btn').addEventListener('click', copyAiAnswer);

// 获取 AI 答案的函数
function getAiAnswer() {
  const questionText = document.getElementById('ai-question').value.trim();
  if (!questionText) {
    document.getElementById('ai-answer').innerText = '请输入问题内容';
    return;
  }

  document.getElementById('ai-answer').innerText = '正在思考中，请稍候...';

  // 获取选择的模型
  const model = document.getElementById('modelSelect').value;

  // 获取用户配置的 key（如果有）
  let userKey = localStorage.getItem('GPTJsSetting.key') || localStorage.getItem('tiku_key') || '';

  // 检查key是否为空
  if (!userKey) {
    document.getElementById('ai-answer').innerText = '请在设置中配置您的Key';
    logger('未配置Key，请在设置中配置您的Key', 'red');
    return;
  }

  logger('使用模型: ' + model, '#1890ff');

  // 构建请求数据
  const data = {
    messages: [
      {
        role: "system",
        search: "true",
        content: "你是一个专业的答题助手,请帮我解答以下问题。"
      },
      {
        role: "user",
        content: questionText
      }
    ],
    model: model
  };

  // 注释掉不需要显示的日志
  // logger('发送请求数据: ' + JSON.stringify(data), 'gray');

  GM_xmlhttpRequest({
    method: 'POST',
    url: API_BASE_URL + '?act=ai',
    headers: {
      'Accept': 'application/json',
      'Authorization': 'Bearer ' + userKey,
      'Content-Type': 'application/json'
    },
    data: JSON.stringify(data),
    timeout: 10000,
    onload: function (response) {
      try {
        // 注释掉不需要显示的日志
        // logger('收到响应: ' + response.responseText.substring(0, 100) + '...', 'gray');

        const result = JSON.parse(response.responseText);
        if (response.status === 200) {
          // 检查是否有错误信息
          if (result.code === 1001) {
            document.getElementById('ai-answer').innerText = '错误: ' + result.msg;
            logger('AI回答失败: ' + result.msg, 'red');
            return;
          }

          // 处理成功响应
          if ((result.code === 1000 || result.code === 200) && result.data && result.data.answer) {
            const answer = result.data.answer;
            document.getElementById('ai-answer').innerText = answer;
            logger('AI回答成功', '#10b981');
          } else {
            document.getElementById('ai-answer').innerText = '获取答案失败: API返回格式错误';
            logger('AI回答失败: API返回格式错误', 'red');
          }
        } else {
          document.getElementById('ai-answer').innerText = '获取答案失败: 服务器响应错误 ' + response.status;
          logger('AI回答失败: 服务器响应错误 ' + response.status, 'red');
        }
      } catch (error) {
        document.getElementById('ai-answer').innerText = '获取答案失败: ' + error.message;
        logger('AI回答失败: ' + error.message, 'red');
      }
    },
    onerror: function (error) {
      document.getElementById('ai-answer').innerText = '获取答案失败，请检查网络连接';
      logger('AI请求错误: ' + error.message, 'red');
    },
    ontimeout: function () {
      document.getElementById('ai-answer').innerText = '获取答案失败，请求超时';
      logger('AI请求超时', 'red');
    }
  });
}

// 复制 AI 答案的函数
function copyAiAnswer() {
  const answerText = document.getElementById('ai-answer').innerText;
  if (answerText && answerText !== '正在思考中，请稍候...' && answerText !== 'AI 助手已准备就绪，请输入您的问题...') {
    navigator.clipboard.writeText(answerText)
      .then(() => {
        logger('答案已复制到剪贴板', '#10b981');
        // 显示复制成功的反馈
        const copyBtn = document.getElementById('ai-copy-btn');
        const originalText = copyBtn.innerText;
        copyBtn.innerText = '复制成功';
        copyBtn.style.backgroundColor = '#52c41a';
        setTimeout(() => {
          copyBtn.innerText = originalText;
          copyBtn.style.backgroundColor = '#722ed1';
        }, 1500);
      })
      .catch(err => {
        logger('复制失败: ' + err, 'red');
      });
  }
}

// 控制日志窗口显示/隐藏的函数
function toggleLogWindow() {
  const logBox = document.querySelector('.gpt-box');
  const logToggleBtn = document.getElementById('ai-log-toggle');

  if (!logBox) return;

  // 切换显示状态
  if (logBox.style.display === 'none') {
    logBox.style.display = 'block';
    logToggleBtn.innerText = '隐藏日志';
    logToggleBtn.style.backgroundColor = '#1890ff';
    localStorage.setItem('GPTJsSetting.hideGptBox', 'false');
  } else {
    logBox.style.display = 'none';
    logToggleBtn.innerText = '显示日志';
    logToggleBtn.style.backgroundColor = '#8c8c8c';
    localStorage.setItem('GPTJsSetting.hideGptBox', 'true');
  }
}

// 初始化日志窗口控制按钮
function initLogToggleButton() {
  const logToggleBtn = document.getElementById('ai-log-toggle');
  if (!logToggleBtn) return;

  // 添加点击事件
  logToggleBtn.addEventListener('click', toggleLogWindow);

  // 根据当前状态设置按钮文本
  const isHidden = localStorage.getItem('GPTJsSetting.hideGptBox') === 'true';
  if (isHidden) {
    logToggleBtn.innerText = '显示日志';
    logToggleBtn.style.backgroundColor = '#8c8c8c';
  } else {
    logToggleBtn.innerText = '隐藏日志';
    logToggleBtn.style.backgroundColor = '#1890ff';
  }
}

// 为 AI 功能添加事件监听器
document.getElementById('ai-send-btn').addEventListener('click', getAiAnswer);
document.getElementById('ai-copy-btn').addEventListener('click', copyAiAnswer);

// 初始化日志窗口控制按钮
setTimeout(() => {
  const logToggleBtn = document.getElementById('ai-log-toggle');
  if (logToggleBtn) {
    initLogToggleButton();
  }
}, 1000); // 给予DOM元素加载的时间

// 为答题系统提供的AI答案获取函数
function getAIAnswer(question, typeName) {
  return new Promise((resolve, reject) => {
    if (!question || question.trim() === '') {
      reject('问题不能为空');
      return;
    }

    logger(`尝试使用AI回答问题: ${question.substring(0, 30)}...`, '#1890ff');
    logger(`题型: ${typeName || '未知题型'}`, '#1890ff');

    // 获取选择的模型
    const model = localStorage.getItem('GPTJsSetting.model') || 'gpt-3.5-turbo-16k';
    logger(`使用模型: ${model}`, '#1890ff');

    // 获取用户配置的 key
    let userKey = localStorage.getItem('GPTJsSetting.key') || localStorage.getItem('tiku_key') || '';

    // 检查key是否为空
    if (!userKey) {
      logger('未配置Key，无法使用AI回答', 'red');
      reject('未配置Key');
      return;
    }

    // 处理题目内容，确保包含选项信息
    let processedQuestion = question;

    // 检查题目是否已经包含选项信息（格式：题目内容\n选项1|选项2|选项3）
    if (question.includes('\n') && question.includes('|')) {
      // 题目已经包含选项信息，直接使用
      logger('题目已包含选项信息，直接传递给AI', 'blue');
    } else if (typeName && (typeName.includes("单选题") || typeName.includes("多选题"))) {
      // 如果是选择题但没有选项信息，尝试从当前页面获取选项
      logger('尝试从当前页面获取选项信息...', 'blue');

      try {
        // 获取当前题目的选项
        let options = [];

        // 尝试多种选择器来获取选项
        const optionSelectors = [
          '.Zy_ulTop li a',                   // 测验页面选择器（新增）
          '.answerList.multiChoice li',       // 多选题选择器（新增）
          '.answerList.singleChoice li',      // 单选题选择器（新增）
          '.clearfix.answerBg .fl.answer_p',  // 考试页面选择器
          '.stem_answer .answer_p',           // 作业页面选择器
          '.answerList li',                   // 手机版选择器
          '.mark_letter li',                  // 其他格式选择器
          '.option-content',                  // 通用选项内容
          'div[class*="answer"]'              // 包含answer的div
        ];

        for (const selector of optionSelectors) {
          const elements = $(selector);
          if (elements.length > 0) {
            elements.each(function () {
              let optionText = $(this).text().trim();

              // 更强的选项文本清理
              optionText = optionText.replace(/^[ABCDEFG][\.\s]*/, ''); // 移除选项字母
              optionText = optionText.replace(/^\s*[A-G]\s*[\.\)]\s*/, ''); // 移除 "A." 或 "A)" 格式
              optionText = optionText.replace(/^\s*[（\(][A-G][）\)]\s*/, ''); // 移除 "(A)" 格式
              optionText = optionText.trim();

              // 过滤掉空选项和重复选项
              if (optionText && optionText.length > 1 && !options.includes(optionText)) {
                options.push(optionText);
              }
            });

            if (options.length > 0) {
              logger(`使用选择器 "${selector}" 找到 ${options.length} 个选项: ${options.join(' | ')}`, 'green');
              break;
            }
          }
        }

        // 如果找到了选项，将其添加到题目中
        if (options.length > 0) {
          processedQuestion = question + '\n' + options.join('|');
          logger(`已将选项信息添加到题目中: ${options.join('|')}`, 'green');
        } else {
          logger('未能从页面获取到选项信息，使用原始题目', 'orange');
        }
      } catch (e) {
        logger(`获取选项信息时出错: ${e.message}`, 'red');
      }
    }

    // 根据题型构建提示词
    let systemPrompt = "你是一个专业的答题助手。";
    if (typeName) {
      systemPrompt += `这是一道${typeName}，请给出准确答案。`;

      // 根据题型调整提示词
      if (typeName.includes("单选题")) {
        systemPrompt += "请直接给出正确选项的完整内容，不要返回选项字母（如A/B/C/D）。如果题目包含选项，请从给出的选项中选择正确答案。";
      } else if (typeName.includes("多选题")) {
        systemPrompt += "请直接给出所有正确选项的完整内容，用###分隔，不要返回选项字母。如果题目包含选项，请从给出的选项中选择正确答案。";
      } else if (typeName.includes("判断题")) {
        systemPrompt += "请直接回答'正确'或'错误'。";
      } else if (typeName.includes("填空题")) {
        systemPrompt += "请直接给出填空内容，无需额外说明。";
      }
    }

    // 不再需要构建复杂的请求数据，直接在URL参数中传递

    // 设置更合理的超时时间
    let requestTimedOut = false;
    const timeoutId = setTimeout(() => {
      requestTimedOut = true;
      logger('AI请求超时，未收到响应', 'red');

      // 当超时时，尝试使用随机答题作为备选
      if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
        logger('转为使用随机答题...', 'orange');
        const randomAnswer = getRandomAnswer(typeName);
        resolve(randomAnswer);
      } else {
        reject('请求超时，未收到响应');
      }
    }, 130000); // 130秒超时，比xmlhttpRequest的超时长一些

    try {
      GM_xmlhttpRequest({
        method: 'POST',
        url: API_BASE_URL + '?act=aimodel',
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer ' + userKey,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: `key=${encodeURIComponent(userKey)}&model=${encodeURIComponent(model)}&question=${encodeURIComponent(processedQuestion)}`,
        timeout: 120000, // 增加到120秒
        onload: function (response) {
          clearTimeout(timeoutId); // 清除超时计时器
          if (requestTimedOut) return; // 如果已经超时，不再处理

          try {
            // logger('收到AI响应，状态码: ' + response.status, 'blue');

            if (!response.responseText) {
              // logger('AI响应内容为空，返回空字符串', 'orange');
              resolve("");
              return;
            }

            // logger('AI响应内容: ' + response.responseText.substring(0, 200) + '...', 'blue');
            const result = JSON.parse(response.responseText);
            // logger('解析AI响应: 代码=' + result.code + ', 消息=' + (result.msg || '无'), 'blue');

            if (response.status === 200) {
              // 检查是否有错误信息
              if (result.code === 1001) {
                logger('AI回答失败: ' + result.msg + '，继续处理', 'orange');
                // 不使用默认答案，让API自己处理空结果
                resolve("");
                return;
              }

              // 处理成功响应 - 匹配api.php的返回格式
              if (result.code === 1 && result.data) {
                const answer = result.data;
                // 移除AI原始回答的日志输出
                // logger('AI原始回答: ' + answer.substring(0, 30) + '...', 'green');

                // 检查答案是否为空或无意义
                if (!answer.trim() || answer.trim() === '无法回答' || answer.trim() === '我不知道') {
                  logger('AI回答内容为空或无意义，直接返回原始回答', 'orange');
                  resolve(answer.trim());
                  return;
                }

                // 对答案进行格式化处理
                let processedAnswer = answer;
                let answerExtracted = false; // 标记是否成功提取了答案

                // 处理AI返回的答案内容
                if (typeName && (typeName.includes("单选题") || typeName.includes("多选题"))) {
                  // 检查是否返回了单个字母（A/B/C/D）作为答案
                  if (/^[A-D]$/i.test(answer.trim())) {
                    // 记录警告，但不做特殊处理，让后续的选项匹配逻辑处理
                    logger('警告：AI返回了选项字母而不是选项内容: ' + answer, 'orange');
                  }

                  // 使用原始答案，但移除可能的解释部分
                  if (answer.length > 100) {
                    // 如果答案很长，可能包含解释，尝试提取简短的答案部分
                    const shortAnswer = answer.split(/[。.;；\n\r]/)[0].trim();
                    if (shortAnswer && shortAnswer.length < 100) {
                      processedAnswer = shortAnswer;
                      logger('提取简短答案: ' + processedAnswer, 'orange');
                      answerExtracted = true;
                    }
                  } else {
                    // 使用完整答案
                    processedAnswer = answer.trim();
                    answerExtracted = true;
                    logger('使用AI返回的完整选项内容: ' + processedAnswer, '#10b981');
                  }
                }
                // 判断题处理
                else if (typeName && typeName.includes("判断题")) {
                  if (answer.includes("正确") || /^(对|是|√|T|ri|true|yes)$/i.test(answer.trim())) {
                    processedAnswer = "正确";
                    // 移除提取判断的日志输出
                    // logger('从AI回答中提取判断: 正确', '#10b981');
                    answerExtracted = true;
                  } else if (answer.includes("错误") || /^(错|否|×|F|wr|false|no)$/i.test(answer.trim())) {
                    processedAnswer = "错误";
                    // 移除提取判断的日志输出
                    // logger('从AI回答中提取判断: 错误', '#10b981');
                    answerExtracted = true;
                  }
                }
                // 填空题处理
                else if (typeName && typeName.includes("填空题")) {
                  // 首先尝试提取"答案："后面的内容
                  if (answer.includes('答案：') || answer.includes('答案:')) {
                    const parts = answer.split(/答案[:：]/);
                    if (parts.length > 1) {
                      processedAnswer = parts[1].trim();
                      // 如果有多行，只取第一行
                      const lines = processedAnswer.split(/[\n\r]+/);
                      processedAnswer = lines[0].trim();
                      // 移除提取填空内容的日志输出
                      // logger('从AI回答中提取填空内容: ' + processedAnswer, '#10b981');
                      answerExtracted = true;
                    }
                  }

                  // 清理答案中的引号、括号等
                  processedAnswer = processedAnswer.replace(/^['"\[\(（【]|['"\]\)）】]$/g, '');
                  answerExtracted = true;
                }
                // 简答题处理 - 根据api.php预设要求简化处理
                else if (typeName && (typeName.includes("简答题") || typeName.includes("论述题") || typeName.includes("分析题"))) {
                  // 清理AI返回的复杂格式答案，保持简洁
                  processedAnswer = answer;

                  // 移除常见的格式化标记
                  processedAnswer = processedAnswer.replace(/\*\*([^*]+)\*\*/g, '$1'); // 移除**粗体**标记
                  processedAnswer = processedAnswer.replace(/^\d+\.\s*/gm, ''); // 移除行首数字序号
                  processedAnswer = processedAnswer.replace(/^[•·]\s*/gm, ''); // 移除行首项目符号
                  processedAnswer = processedAnswer.replace(/^[-*]\s*/gm, ''); // 移除行首破折号和星号
                  processedAnswer = processedAnswer.replace(/答案[:：]\s*/g, ''); // 移除"答案："前缀
                  processedAnswer = processedAnswer.replace(/^[A-D][\.\:：]\s*/gm, ''); // 移除选项字母前缀

                  // 将多行内容合并为一段，用句号分隔
                  const lines = processedAnswer.split(/[\n\r]+/).filter(line => line.trim());
                  if (lines.length > 1) {
                    processedAnswer = lines.map(line => line.trim()).join('。');
                    // 确保结尾有句号
                    if (!processedAnswer.endsWith('。') && !processedAnswer.endsWith('.')) {
                      processedAnswer += '。';
                    }
                  }

                  // 清理多余的空格和标点
                  processedAnswer = processedAnswer.replace(/\s+/g, ' ').trim();
                  processedAnswer = processedAnswer.replace(/。+/g, '。'); // 合并多个句号

                  // logger('简答题答案处理完成: ' + processedAnswer.substring(0, 50) + '...', 'green');
                  answerExtracted = true;
                }

                // 允许所有类型的答案
                answerExtracted = true;

                logger('最终处理后的AI答案: ' + processedAnswer, 'green');
                resolve(processedAnswer);
              } else {
                // 移除详细的错误响应日志，只保留简洁信息
                logger('AI响应格式不正确或答案为空，返回空字符串', 'orange');
                // 返回空字符串，不使用默认答案
                resolve("");
              }
            } else {
              logger('AI请求返回非200状态码: ' + response.status + '，返回空字符串', 'orange');
              // 返回空字符串，不使用默认答案
              resolve("");
            }
          } catch (e) {
            logger('处理AI响应时出错: ' + e.message + '，返回空字符串', 'orange');
            // 返回空字符串，不使用默认答案
            resolve("");
          }
        },
        onerror: function (error) {
          clearTimeout(timeoutId); // 清除超时计时器
          if (requestTimedOut) return; // 如果已经超时，不再处理

          logger('AI请求发送失败: ' + (error.statusText || error), 'red');

          // 错误时尝试随机答题
          if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
            logger('请求失败，转为使用随机答题...', 'orange');
            const randomAnswer = getRandomAnswer(typeName);
            resolve(randomAnswer);
          } else {
            // 返回空字符串，不使用默认答案
            logger(`请求发送失败，返回空字符串`, 'orange');
            resolve("");
          }
        },
        ontimeout: function () {
          clearTimeout(timeoutId); // 清除超时计时器
          if (requestTimedOut) return; // 如果已经超时，不再处理

          logger('AI请求超时', 'red');

          // 超时时尝试随机答题
          if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
            logger('请求超时，转为使用随机答题...', 'orange');
            const randomAnswer = getRandomAnswer(typeName);
            resolve(randomAnswer);
          } else {
            // 使用默认答案
            const defaultAnswer = '6666';
            logger(`请求超时，使用默认答案: ${defaultAnswer}`, 'orange');
            resolve(defaultAnswer);
          }
        }
      });
    } catch (e) {
      clearTimeout(timeoutId); // 清除超时计时器
      logger('发送AI请求时出错: ' + e.message, 'red');

      // 错误时尝试随机答题
      if (localStorage.getItem('GPTJsSetting.randomAnswer') === 'true') {
        logger('异常错误，转为使用随机答题...', 'orange');
        const randomAnswer = getRandomAnswer(typeName);
        resolve(randomAnswer);
      } else {
        // 使用默认答案
        const defaultAnswer = '6666';
        logger(`发送请求失败，使用默认答案: ${defaultAnswer}`, 'orange');
        resolve(defaultAnswer);
      }
    }
  });
}

/**
 * 生成随机答案
 * @param {string} typeName - 题目类型
 * @returns {string} - 生成的随机答案
 */
function getRandomAnswer(typeName) {
  logger('准备使用随机答题功能...', 'orange');

  // 确保typeName是有效的字符串
  if (!typeName) {
    typeName = '未知题型';
    logger('题型未知，默认使用单选题随机答案', 'orange');
  }

  let randomAnswer = '';

  // 根据题型生成随机答案
  if (typeName.includes('单选题')) {
    // 单选题，随机选择A-D中的一个
    const options = ['A', 'B', 'C', 'D'];
    randomAnswer = options[Math.floor(Math.random() * options.length)];
    logger('随机生成单选题答案: ' + randomAnswer, '#E6A23C');
  } else if (typeName.includes('多选题')) {
    // 多选题，随机选择1-3个选项
    const options = ['A', 'B', 'C', 'D'];
    const numChoices = Math.floor(Math.random() * 3) + 1; // 随机选择1-3个
    // 打乱选项数组
    const shuffled = [...options].sort(() => 0.5 - Math.random());
    // 取前面的numChoices个元素
    randomAnswer = shuffled.slice(0, numChoices).sort().join('');
    logger('随机生成多选题答案: ' + randomAnswer, '#E6A23C');
  } else if (typeName.includes('判断题')) {
    // 判断题，随机选择正确或错误
    randomAnswer = Math.random() > 0.5 ? '正确' : '错误';
    logger('随机生成判断题答案: ' + randomAnswer, '#E6A23C');
  } else if (typeName.includes('填空题')) {
    // 填空题，使用默认答案
    randomAnswer = '6666';
    logger('为填空题设置默认答案: ' + randomAnswer, '#E6A23C');
  } else if (typeName.includes('简答题') || typeName.includes('论述题') || typeName.includes('分析题') || typeName.includes('写作题')) {
    // 简答题、论述题等，使用默认答案
    randomAnswer = '6666';
    logger('为简答题设置默认答案: ' + randomAnswer, '#E6A23C');
  } else {
    // 其他类型题目或未知题型，使用默认答案
    randomAnswer = '6666';
    logger('为未知题型设置默认答案: ' + randomAnswer, '#E6A23C');
  }

  // 显示桌面通知
  showDesktopNotification('随机答题', `题型: ${typeName}\n随机答案: ${randomAnswer}`, '');

  return randomAnswer;
}

/**
 * 处理/mooc-ans/mooc2/work/view页面的答题功能
 * 适配作业网页的DOM结构
 */
function doWorkView() {
  logger('开始处理作业页面答题', 'green');

  // 添加框架加载提示，类似章节测验
  logger('等待作业框架加载...', 'blue');

  // 检查当前URL，判断是否为作业页面
  const currentUrl = window.location.href;
  logger(`当前页面URL: ${currentUrl}`, 'blue');

  // 检查是否是平台ID:5500的作业页面
  const isPlatform5500 = currentUrl.includes('5500') ||
    document.body.innerHTML.includes('5500') ||
    document.body.innerHTML.includes('platform=5500');

  if (isPlatform5500) {
    logger('检测到平台ID:5500的作业页面，使用特殊处理', 'orange');
  }

  // 检查是否需要初始化AI答题框架
  const neBox = document.getElementById('ne-21box');
  const shouldHideBox = localStorage.getItem('GPTJsSetting.showBox') === 'hide';

  if (!neBox || (neBox.style.display === 'none' && !shouldHideBox)) {
    // 如果AI答题框架不存在，或者未显示但用户没有设置隐藏，则尝试激活它
    logger('尝试激活AI答题框架...', 'orange');
    try {
      // 查找并点击AI答题按钮
      const aiButtons = document.querySelectorAll('button');
      let aiButton = null;
      for (const btn of aiButtons) {
        if (btn.innerText && (
          btn.innerText.includes('AI答题') ||
          btn.innerText.includes('智能答题') ||
          btn.innerText.includes('智能解析') ||
          btn.innerText.includes('辅助解答')
        )) {
          aiButton = btn;
          break;
        }
      }

      if (aiButton) {
        logger('找到AI答题按钮，点击激活框架...', 'green');
        aiButton.click();
        // 给框架一些加载时间
        setTimeout(doWorkView, 2000);
        return;
      } else {
        logger('未找到AI答题按钮，尝试其他方式激活框架', 'orange');

        // 尝试查找可能的AI答题入口
        const possibleAiElements = [
          ...document.querySelectorAll('[class*="ai"]'),
          ...document.querySelectorAll('[id*="ai"]'),
          ...document.querySelectorAll('[class*="assistant"]'),
          ...document.querySelectorAll('[id*="assistant"]'),
          ...document.querySelectorAll('[class*="help"]'),
          ...document.querySelectorAll('[id*="help"]')
        ];

        for (const element of possibleAiElements) {
          if (element.tagName === 'BUTTON' ||
            element.tagName === 'A' ||
            element.tagName === 'DIV' ||
            element.tagName === 'SPAN') {
            logger(`尝试点击可能的AI入口: ${element.tagName}`, 'orange');
            try {
              element.click();
              setTimeout(() => {
                const neBox = document.getElementById('ne-21box');
                const shouldHideBox = localStorage.getItem('GPTJsSetting.showBox') === 'hide';
                if (neBox && (neBox.style.display !== 'none' || shouldHideBox)) {
                  logger('成功激活AI答题框架', 'green');
                  setTimeout(doWorkView, 1000);
                  return;
                }
              }, 500);
            } catch (e) {
              // 忽略点击错误
            }
          }
        }
      }
    } catch (e) {
      logger(`尝试激活AI答题框架失败: ${e.message}`, 'red');
    }
  }

  // 检查页面是否已加载完成
  if ($('.mark_cont').length === 0 && $('.mark_table').length === 0) {
    // 如果页面元素还没加载完成，设置一个延时后重试
    logger('页面元素尚未加载完成，等待重试...', 'orange')
    setTimeout(doWorkView, 1000)
    return
  }

  // 查找作业表格和题目列表 - 增强查找能力
  let $_homeworktable = $('.mark_cont')
  if ($_homeworktable.length === 0) {
    $_homeworktable = $('.mark_table')

    // 尝试更多可能的作业表格选择器
    if ($_homeworktable.length === 0) {
      const possibleTableSelectors = [
        '.mark_cont', '.mark_table', '.work_content',
        '.homework-content', '.workContent',
        '.homework-container', '.work-container',
        '.work-content-main', '.homework-box',
        '.work_question_list', '#work-content'
      ];

      for (const selector of possibleTableSelectors) {
        $_homeworktable = $(selector);
        if ($_homeworktable.length > 0) {
          logger(`使用选择器 "${selector}" 找到作业表格`, 'green');
          break;
        }
      }

      // 如果还是没找到，尝试在iframe中查找
      if ($_homeworktable.length === 0) {
        try {
          const iframes = document.querySelectorAll('iframe');
          logger(`尝试在 ${iframes.length} 个iframe中查找作业表格`, 'blue');

          for (const iframe of iframes) {
            try {
              const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

              // 在iframe中尝试所有可能的选择器
              for (const selector of possibleTableSelectors) {
                const iframeTable = iframeDoc.querySelector(selector);
                if (iframeTable) {
                  $_homeworktable = $(iframeTable);
                  logger(`在iframe中使用选择器 "${selector}" 找到作业表格`, 'green');
                  break;
                }
              }

              if ($_homeworktable.length > 0) break;
            } catch (e) {
              // 跨域iframe无法访问
              continue;
            }
          }
        } catch (e) {
          logger('尝试在iframe中查找作业表格失败: ' + e.message, 'red');
        }
      }

      // 如果还是没找到，尝试使用更通用的选择器
      if ($_homeworktable.length === 0) {
        $_homeworktable = $('body'); // 使用body作为最后的备选
        logger('未找到专用作业表格，使用body作为容器继续查找题目', 'orange');
      }
    }
  }

  // 查找题目列表 - 增强查找能力
  let TiMuList = $_homeworktable.find('.questionLi')

  // 尝试更多可能的题目列表选择器
  if (TiMuList.length === 0) {
    const possibleQuestionSelectors = [
      '.questionLi', '.mark_item', '.mark_question',
      '.homework_question', '.work-question-item',
      '.work_question', '.question-item', '.question',
      '.mark_li', '.que-item', '.que-box',
      '.questionItem', '.question-box'
    ];

    for (const selector of possibleQuestionSelectors) {
      TiMuList = $_homeworktable.find(selector);
      if (TiMuList.length > 0) {
        logger(`使用选择器 "${selector}" 找到 ${TiMuList.length} 道题目`, 'green');
        break;
      }
    }

    // 如果仍然没找到，尝试一个更通用的选择器
    if (TiMuList.length === 0) {
      TiMuList = $_homeworktable.find('[class*="question"], [class*="mark_"], [class*="work_"]');
      if (TiMuList.length > 0) {
        logger(`使用通配选择器找到 ${TiMuList.length} 个可能的题目元素`, 'orange');
      }
    }
  }

  // 如果还是没找到题目
  if (TiMuList.length === 0) {
    logger('未找到题目列表，尝试重新加载框架...', 'red');
    setTimeout(doWorkView, 2000); // 延迟后重试
    return;
  }

  logger(`成功找到 ${TiMuList.length} 道题目，准备开始答题`, 'green');

  // 检查是否已经完成
  if ($_homeworktable.find('.Py-mian1').text().includes('已批阅') ||
    $_homeworktable.find('.Py-mian1').text().includes('待批阅')) {
    logger('本作业已提交，无需再次答题', 'green')
    return
  }

  logger(`找到 ${TiMuList.length} 道题目，开始自动答题`, 'green')

  // 添加提交按钮点击功能
  const submitBtn = $('.Btn_blue_1')
  if (submitBtn.length > 0) {
    logger('已找到提交按钮，将在答题完成后自动提交', 'green')
    // 保存提交按钮引用，以便在答题完成后使用
    window._submitWorkBtn = submitBtn
  }

  // 设置自动提交
  if (localStorage.getItem('GPTJsSetting.autoSubmit') === 'true') {
    logger('已启用自动提交功能', 'green')
  }

  doWorkViewQuestion(0, TiMuList)
}

/**
 * 递归处理作业页面中的每个题目
 * @param {number} index - 当前处理的题目索引
 * @param {Array} TiMuList - 题目列表
 */
function doWorkViewQuestion(index, TiMuList) {
  if (index == TiMuList.length) {
    logger('作业题目已全部完成', 'green')

    // 检查是否开启了自动保存
    if (localStorage.getItem('GPTJsSetting.autoSave') !== 'false') {
      // 首先尝试保存答案
      try {
        logger('正在自动保存作业答案...', 'blue')

        // 使用安全执行函数调用页面的saveWork函数
        if (safeExecutePageFunction('saveWork')) {
          logger('答案保存成功！', 'green')
        } else {
          // 如果无法通过函数名调用，尝试找到保存按钮并点击
          const saveBtn = $('.btnGray_1:contains("保存")') || $('button:contains("保存")') || $('.saveBtn');
          if (saveBtn && saveBtn.length > 0) {
            saveBtn.click();
            logger('已点击保存按钮', 'green')
          } else {
            // 最后尝试通过表单提交
            const form = $('form#submitForm');
            if (form && form.length > 0) {
              logger('尝试通过表单提交保存答案...', 'blue');

              // 构建保存URL
              let saveUrl = form.attr('action');
              if (saveUrl) {
                saveUrl += '&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1';

                // 通过AJAX提交表单
                $.ajax({
                  type: form.attr('method') || 'POST',
                  url: saveUrl,
                  data: form.serialize(),
                  dataType: 'json',
                  success: function (data) {
                    if (data && data.status) {
                      logger('答案保存成功！', 'green');
                    } else {
                      logger('答案保存失败: ' + (data ? data.msg : '未知错误'), 'red');
                    }
                  },
                  error: function () {
                    logger('答案保存请求失败，请手动保存', 'red');
                  }
                });
              } else {
                logger('无法获取表单提交URL，请手动保存', 'red');
              }
            } else {
              logger('未找到保存按钮或表单，请手动保存', 'red')
            }
          }
        }
      } catch (e) {
        logger('自动保存失败: ' + e.message + '，请手动保存', 'red')
      }

      // 如果设置了自动提交，则在保存后提交
      if (localStorage.getItem('GPTJsSetting.autoSubmit') === 'true') {
        logger('准备自动提交作业...', 'green')
        setTimeout(() => {
          try {
            // 尝试多种方式找到提交按钮
            let submitBtn = window._submitWorkBtn;
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('.Btn_blue_1');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('button:contains("提交")');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('input[type="submit"]');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('button.btn-submit');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('.btnGray_1');
            }

            if (submitBtn && submitBtn.length > 0) {
              logger('找到提交按钮，点击提交...', 'green')
              submitBtn.click();

              // 尝试处理确认对话框
              setTimeout(() => {
                const confirmBtn = $('.bluebtn') || $('.layui-layer-btn0') || $('button:contains("确定")');
                if (confirmBtn && confirmBtn.length > 0) {
                  logger('点击确认提交...', 'green');
                  confirmBtn.click();
                }
                logger('作业已自动提交！', 'green')
              }, 1000);
            } else {
              logger('未找到提交按钮，请手动提交', 'red');
            }
          } catch (e) {
            logger(`自动提交失败: ${e.message}，请手动提交`, 'red');
          }
        }, 2000) // 延长等待时间，确保保存操作完成
      } else {
        logger('已完成所有题目并尝试保存，如需提交请手动点击提交按钮', 'blue')
      }
      return
    } else {
      logger('自动保存已禁用，请手动保存答案', 'blue')

      // 如果设置了自动提交，仍然可以自动提交
      if (localStorage.getItem('GPTJsSetting.autoSubmit') === 'true') {
        logger('准备自动提交作业...', 'green')
        setTimeout(() => {
          try {
            // 尝试多种方式找到提交按钮
            let submitBtn = window._submitWorkBtn;
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('.Btn_blue_1');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('button:contains("提交")');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('input[type="submit"]');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('button.btn-submit');
            }
            if (!submitBtn || submitBtn.length === 0) {
              submitBtn = $('.btnGray_1');
            }

            if (submitBtn && submitBtn.length > 0) {
              logger('找到提交按钮，点击提交...', 'green')
              submitBtn.click();

              // 尝试处理确认对话框
              setTimeout(() => {
                const confirmBtn = $('.bluebtn') || $('.layui-layer-btn0') || $('button:contains("确定")');
                if (confirmBtn && confirmBtn.length > 0) {
                  logger('点击确认提交...', 'green');
                  confirmBtn.click();
                }
                logger('作业已自动提交！', 'green')
              }, 1000);
            } else {
              logger('未找到提交按钮，请手动提交', 'red');
            }
          } catch (e) {
            logger(`自动提交失败: ${e.message}，请手动提交`, 'red');
          }
        }, 2000)
      } else {
        logger('已完成所有题目，请手动保存和提交', 'blue')
      }
      return
    }
  }

  // 获取题目类型
  let typeNameElem = $(TiMuList[index]).attr('typename')
  if (!typeNameElem) {
    let typeMatch = $(TiMuList[index]).find('.mark_name').find('.colorShallow').text()
    if (typeMatch) {
      typeNameElem = typeMatch.replace(/[()（）]/g, '')
    }
  }

  let _type = ({ 单选题: 0, 多选题: 1, 填空题: 2, 判断题: 3, 简答题: 4, 写作题: 5, 翻译题: 6 })[typeNameElem] || 4

  // 获取题目内容
  let _questionFull = $(TiMuList[index]).find('.mark_name').html()

  // 增强题目处理，确保移除题目类型信息
  let _question = formatQuestionText(_questionFull)

  // 移除题目类型信息 (多选题, 2分) 等格式
  _question = _question.replace(/^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：,，]*[\d\.]*分?[\)）\]\】]?\s*/i, '')

  // 移除开头的序号和其他格式
  _question = _question.replace(/^\s*\d+[\.\、\:：]\s*/, '')

  // 清理多余的空格
  _question = _question.trim()

  logger(`正在处理第 ${index + 1} 题: ${_question.substring(0, 30)}...`, 'blue')

  // 根据题型处理答题
  switch (_type) {
    case 0: // 单选题
      handleSingleChoice(index, TiMuList, _question, _type)
      break

    case 1: // 多选题
      handleMultiChoice(index, TiMuList, _question, _type)
      break

    case 2: // 填空题
      handleFillBlank(index, TiMuList, _question, _type)
      break

    case 3: // 判断题
      handleJudgment(index, TiMuList, _question, _type)
      break

    case 4: // 简答题
    case 5: // 写作题
    case 6: // 翻译题
      handleEssay(index, TiMuList, _question, _type)
      break

    default:
      logger(`暂不支持处理此题型：${typeNameElem}，跳过`, 'red')
      setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time)
  }
}

/**
 * 处理单选题
 */
function handleSingleChoice(index, TiMuList, _question, _type) {
  // 获取选项列表 - 适配不同页面结构
  let _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.answer_p')

  // 记录是否是作业网页源代码2.html格式
  let isWorkPage2Format = false;

  // 作业页面特殊选择器 - 增加多种可能的选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.mark_letter').find('li')

    // 尝试其他常见的作业选项选择器
    if (_answerTmpArr.length === 0) {
      _answerTmpArr = $(TiMuList[index]).find('.mark_letter li')
    }

    if (_answerTmpArr.length === 0) {
      _answerTmpArr = $(TiMuList[index]).find('ul.mark_letter li')
    }

    if (_answerTmpArr.length === 0) {
      _answerTmpArr = $(TiMuList[index]).find('.mark_letter > li')
    }

    // 添加对作业网页源代码2.html中的选择器支持
    if (_answerTmpArr.length === 0) {
      const answerBgElements = $(TiMuList[index]).find('.clearfix.answerBg');
      if (answerBgElements.length > 0) {
        _answerTmpArr = answerBgElements;
        isWorkPage2Format = true;
        logger('检测到作业网页源代码2格式的题目', 'blue');
      }
    }
  }

  // 章节测验选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('li')
  }

  // 如果仍然没找到选项，尝试其他可能的选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.fl')

    if (_answerTmpArr.length === 0) {
      logger(`第 ${index + 1} 题无法找到选项列表，尝试其他方式`, 'orange')

      // 尝试更多可能的选择器
      const possibleSelectors = [
        'ul.ulTop li',
        'ul li',
        '.mark_option li',
        '.mark_option div',
        '.mark_option > div',
        '.mark_option .option',
        '.que-answer li',
        '.que-answer .option',
        '.que-answer-content li',
        '.que-answer-options li',
        '.mark_letter .mark_letter_item',
        '.mark_cont li',
        '.clearfix.answerBg', // 作业网页源代码2.html中的选择器
        '.stem_answer .clearfix', // 通用选择器
        '.stem_answer [class*="answer"]', // 包含answer的元素
        '[class*="option"]', // 包含option的元素
        '[class*="choice"]', // 包含choice的元素
        '[role="radio"]' // ARIA角色为radio的元素
      ];

      // 尝试每个可能的选择器
      for (const selector of possibleSelectors) {
        _answerTmpArr = $(TiMuList[index]).find(selector);
        if (_answerTmpArr.length > 0) {
          logger(`使用选择器 "${selector}" 找到了选项列表`, 'green');
          break;
        }
      }

      // 尝试在iframe中查找
      if (_answerTmpArr.length === 0) {
        try {
          const iframes = document.querySelectorAll('iframe');
          for (const iframe of iframes) {
            try {
              const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
              const iframeQuestion = $(iframeDoc).find(TiMuList[index].selector);
              if (iframeQuestion.length > 0) {
                for (const selector of possibleSelectors) {
                  _answerTmpArr = $(iframeQuestion).find(selector);
                  if (_answerTmpArr.length > 0) {
                    logger(`在iframe中使用选择器 "${selector}" 找到了选项列表`, 'green');
                    break;
                  }
                }
              }
              if (_answerTmpArr.length > 0) break;
            } catch (e) {
              // 跨域iframe无法访问
              continue;
            }
          }
        } catch (e) {
          logger('尝试在iframe中查找选项失败: ' + e.message, 'red');
        }
      }
    }
  }

  // 如果还是没找到选项
  if (_answerTmpArr.length === 0) {
    logger(`第 ${index + 1} 题无法找到选项列表，跳过此题`, 'red')
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time)
    return
  }

  // 记录找到的选项数量
  logger(`找到 ${_answerTmpArr.length} 个选项`, 'blue')

  // 检查题目是否已作答
  let isAnswered = false;
  for (var i = 0; i < _answerTmpArr.length; i++) {
    // 检查多种可能的已答标记
    const $option = $(_answerTmpArr[i]);
    const $parent = $option.parent();
    const $grandparent = $parent.parent();

    if (
      // 标准标记
      ($parent.find('span.check_answer').length > 0) ||
      ($parent.find('span.check_answer_dx').length > 0) ||
      ($option.find('span.check_answer').length > 0) ||
      ($option.find('span.check_answer_dx').length > 0) ||
      // 常见类名
      ($option.hasClass('chosen')) ||
      ($option.hasClass('selected')) ||
      ($option.hasClass('checked')) ||
      ($option.hasClass('active')) ||
      ($option.hasClass('on')) ||
      ($parent.hasClass('chosen')) ||
      ($parent.hasClass('selected')) ||
      ($parent.hasClass('checked')) ||
      ($parent.hasClass('active')) ||
      ($parent.hasClass('on')) ||
      // 属性标记
      ($option.attr('aria-selected') === 'true') ||
      ($option.attr('checked') === 'checked') ||
      ($option.attr('selected') === 'selected') ||
      ($option.attr('data-checked') === 'true') ||
      // 视觉标记
      ($option.css('background-color') !== 'transparent' && $option.css('background-color') !== 'rgba(0, 0, 0, 0)') ||
      ($option.css('color') === 'rgb(255, 255, 255)') ||
      // 特殊元素
      ($option.find('input[type="radio"]:checked').length > 0) ||
      ($parent.find('input[type="radio"]:checked').length > 0) ||
      ($grandparent.find('input[type="radio"]:checked').length > 0) ||
      // 图标标记
      ($option.find('i.icon-check').length > 0) ||
      ($option.find('[class*="check"]').length > 0) ||
      ($option.find('[class*="selected"]').length > 0) ||
      ($parent.find('i.icon-check').length > 0) ||
      ($parent.find('[class*="check"]').length > 0) ||
      ($parent.find('[class*="selected"]').length > 0)
    ) {
      logger(`第 ${index + 1} 题已作答，准备切换下一题`, 'green');
      isAnswered = true;
      break;
    }
  }

  if (isAnswered) {
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, 300);
    return;
  }

  // 构建题目内容，包含选项
  var mergedAnswers = []
  _answerTmpArr.each(function () {
    var answerText = $(this).text().replace(/[ABCD]/g, '').trim()
    mergedAnswers.push(answerText)
  })
  mergedAnswers = mergedAnswers.join("|")
  _question = "单选题:" + _question + '\n' + mergedAnswers

  // 获取答案
  getAnswer(_type, _question).then((agrs) => {
    let _a = []
    let _optionLabels = []

    // 检查是否为错误信息
    if (agrs.includes('未找到答案') || agrs === '暂无答案' || agrs.includes('错误:') || agrs.includes('题目找到但答案无效') || agrs.includes('未找到有效答案')) {
      // 记录错误但继续尝试处理
      logger('题库返回错误信息，尝试随机选择', 'orange');
      // 随机选择一个选项
      const randomIndex = Math.floor(Math.random() * _answerTmpArr.length);
      logger(`随机选择选项: ${randomIndex + 1}`, 'orange');

      // 直接点击随机选项
      setTimeout(() => {
        try {
          const $option = $(_answerTmpArr[randomIndex]);

          // 检查是否是作业网页源代码2.html格式
          if (isWorkPage2Format) {
            // 尝试使用addChoice函数
            if (typeof addChoice === 'function') {
              logger('尝试直接调用addChoice函数', 'green');
              try {
                addChoice($option[0]);
                logger('addChoice函数调用成功', 'green');

                // 获取题目ID
                const questionId = $option.attr('qid') || $(TiMuList[index]).attr('id') || $(TiMuList[index]).attr('data');
                if (questionId) {
                  // 设置答案值
                  const answerInput = $(`#answer${questionId}`);
                  if (answerInput.length > 0) {
                    // 获取选项值
                    let optionLabel = $option.find('.num_option').attr('data') ||
                      $option.find('.num_option').text().trim();

                    // 设置答案值
                    answerInput.val(optionLabel);
                    logger(`设置答案输入框值: ${optionLabel}`, 'green');

                    // 触发change事件，确保值被正确应用
                    answerInput.trigger('change');
                  }

                  // 添加选中样式到num_option元素
                  $option.find('.num_option').addClass('check_answer');

                  // 设置ARIA属性
                  $option.attr('aria-checked', 'true');
                  $option.attr('aria-pressed', 'true');
                }
              } catch (e) {
                logger(`addChoice函数调用失败: ${e.message}，尝试其他方法`, 'red');
                $option.click();
              }
            } else {
              $option.click();
            }
          } else {
            $option.click();
          }

          // 继续处理下一题
          setTimeout(() => {
            logger('准备处理下一题...', 'green');
            index += 1;
            if (index < TiMuList.length) {
              doWorkViewQuestion(index, TiMuList);
            } else {
              logger('所有题目处理完成', 'green');
              // 检查是否需要自动提交
              if (localStorage.getItem('GPTJsSetting.sub') === 'true') {
                uploadAnswer();
              }
            }
          }, 500);
        } catch (e) {
          logger(`点击随机选项失败: ${e.message}，继续处理下一题`, 'red');

          // 即使点击失败，也继续处理下一题
          setTimeout(() => {
            index += 1;
            if (index < TiMuList.length) {
              doWorkViewQuestion(index, TiMuList);
            } else {
              logger('所有题目处理完成', 'green');
              // 检查是否需要自动提交
              if (localStorage.getItem('GPTJsSetting.sub') === 'true') {
                uploadAnswer();
              }
            }
          }, 500);
        }
      }, 500);

      return; // 提前返回，不再执行后续代码
    }

    // 获取选项文本和选项标签
    $.each(_answerTmpArr, (i, t) => {
      const $option = $(t);
      let optionText = cleanTextContent($option.html());
      _a.push(optionText);

      // 尝试获取选项标签(A/B/C/D)
      let optionLabel = '';

      // 方法1：从span.num_option元素获取
      const $numOption = $option.find('.num_option');
      if ($numOption.length > 0) {
        optionLabel = $numOption.attr('data') || $numOption.text().trim();
      }

      // 方法2：从选项文本的第一个字符获取
      if (!optionLabel) {
        const firstChar = $option.text().trim().charAt(0);
        if (/[A-D]/i.test(firstChar)) {
          optionLabel = firstChar.toUpperCase();
        }
      }

      // 方法3：根据索引分配标签
      if (!optionLabel) {
        optionLabel = String.fromCharCode(65 + i); // A, B, C, D...
      }

      _optionLabels.push(optionLabel);
    });

    // 调试信息
    logger(`获取到答案: ${agrs}`, 'blue')
    logger(`最终处理后的AI答案: ${agrs}`, 'blue')
    logger(`选项列表: ${_a.join(' | ')}`, 'blue')
    logger(`选项标签: ${_optionLabels.join(', ')}`, 'blue')

    // 检查答案是否为空或无效
    if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案' || agrs.includes('错误:') || agrs.includes('题目找到但答案无效') || agrs.includes('未找到有效答案')) {
      logger('未获取到有效答案或题库返回错误，跳过此题', 'red');
      localStorage.setItem('GPTJsSetting.sub', false);
      setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
      return;
    }



    // 首先检查答案是否直接匹配选项标签(A/B/C/D)
    let _i = -1;
    const directLabelMatch = agrs.trim().toUpperCase();

    // 增强选项标签匹配 - 支持更多格式
    if (/^[A-D]$/i.test(directLabelMatch)) {
      // 将答案转为大写，确保匹配不区分大小写
      const upperLabel = directLabelMatch.toUpperCase();
      _i = _optionLabels.findIndex(label => label.toUpperCase() === upperLabel);
      if (_i !== -1) {
        logger(`答案直接匹配选项标签: ${upperLabel}`, 'green');
      }
    }

    // 如果没有直接匹配选项标签，尝试精确匹配选项内容
    if (_i === -1) {
      _i = _a.findIndex((item) => {
        // 使用更宽松的匹配 - 忽略空格和大小写
        const cleanOption = item.replace(/\s+/g, '').toLowerCase();
        const cleanAnswer = agrs.replace(/\s+/g, '').toLowerCase();
        return cleanOption === cleanAnswer || cleanOption.includes(cleanAnswer) || cleanAnswer.includes(cleanOption);
      });

      if (_i !== -1) {
        logger(`答案内容匹配选项: ${_optionLabels[_i]}`, 'green');
      }
    }

    // 第一层：直接匹配选项标签(A/B/C/D)
    if (_i === -1) {
      // 增强的选项标签匹配 - 支持单个字母和更多格式
      const labelMatch = agrs.match(/^([A-D])$/i) ||
        agrs.match(/^选项\s*([A-D])$/i) ||
        agrs.match(/^答案\s*[是为:：]\s*([A-D])$/i) ||
        agrs.match(/选择\s*([A-D])\s*[项选]?/i) ||
        // 增加更多匹配模式
        agrs.match(/正确答案[是为:：]?\s*([A-D])/i) ||
        agrs.match(/([A-D])[是为]正确的?/i) ||
        agrs.match(/([A-D])[选项]?最合适/i) ||
        agrs.match(/应[该当]?[是选]([A-D])/i) ||
        agrs.match(/我[认觉]为[是应该选]([A-D])/i) ||
        agrs.match(/我选([A-D])/i) ||
        agrs.match(/选择([A-D])[选项]?/i) ||
        agrs.match(/([A-D])(?:\.|\s|$)/i);  // 匹配A. B. C. D.或单独的A B C D

      if (labelMatch && labelMatch[1]) {
        const upperLabel = labelMatch[1].toUpperCase();
        _i = _optionLabels.findIndex(label => label.toUpperCase() === upperLabel);
        if (_i !== -1) {
          logger(`第一层：增强匹配到选项标签 ${upperLabel}`, 'green');
        }
      } else {
        // 尝试直接匹配单个字母（不需要正则）
        const singleLetter = agrs.trim().toUpperCase();
        if (/^[A-D]$/.test(singleLetter)) {
          _i = _optionLabels.findIndex(label => label.toUpperCase() === singleLetter);
          if (_i !== -1) {
            logger(`第一层：直接匹配到选项标签 ${singleLetter}`, 'green');
          }
        }
      }
    }

    // 第三层C：内容包含匹配 - 增强版
    if (_i === -1) {
      let bestMatchIndex = -1;
      let bestMatchScore = 0;
      let bestMatchReason = '';

      _a.forEach((option, idx) => {
        const cleanOption = option.replace(/\s+/g, '').toLowerCase();
        const cleanAnswer = agrs.replace(/\s+/g, '').toLowerCase();
        let matchScore = 0;
        let matchReason = '';

        // 检查1：完全包含关系
        if (cleanOption.includes(cleanAnswer)) {
          matchScore = cleanAnswer.length / cleanOption.length * 0.9; // 权重0.9
          matchReason = '答案完全包含在选项中';
        }
        else if (cleanAnswer.includes(cleanOption)) {
          matchScore = cleanOption.length / cleanAnswer.length * 0.8; // 权重0.8
          matchReason = '选项完全包含在答案中';
        }

        // 检查2：关键词匹配
        if (matchScore < 0.7) { // 如果完全包含匹配得分不高
          // 提取关键词
          const optionKeywords = cleanOption.replace(/[.,，。、；;:：!！?？()（）\[\]【】]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 1);

          const answerKeywords = cleanAnswer.replace(/[.,，。、；;:：!！?？()（）\[\]【】]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 1);

          // 计算关键词匹配数
          let keywordMatches = 0;
          let totalKeywords = Math.max(optionKeywords.length, 1);

          for (const keyword of optionKeywords) {
            for (const answerWord of answerKeywords) {
              if (keyword.includes(answerWord) || answerWord.includes(keyword) ||
                calculateSimilarity(keyword, answerWord) > 0.7) {
                keywordMatches++;
                break;
              }
            }
          }

          const keywordScore = keywordMatches / totalKeywords * 0.85; // 权重0.85
          if (keywordScore > matchScore) {
            matchScore = keywordScore;
            matchReason = `关键词匹配度: ${(keywordScore * 100).toFixed(1)}%`;
          }
        }

        // 检查3：数值匹配 - 增强版
        const optionNumbers = option.match(/\d+(\.\d+)?/g);
        const answerNumbers = agrs.match(/\d+(\.\d+)?/g);

        if (optionNumbers && answerNumbers) {
          for (const optNum of optionNumbers) {
            if (answerNumbers.includes(optNum)) {
              const numberScore = 0.95; // 数值匹配给予高权重
              if (numberScore > matchScore) {
                matchScore = numberScore;
                matchReason = `数值精确匹配: ${optNum}`;
              }
              break;
            }
          }
        }

        // 检查4：特殊字符匹配
        const specialChars = ['√', '×', '对', '错', '是', '否', 'T', 'F', '正确', '错误'];
        for (const char of specialChars) {
          if (cleanOption.includes(char.toLowerCase()) && cleanAnswer.includes(char.toLowerCase())) {
            const specialScore = 0.9; // 特殊字符匹配给予高权重
            if (specialScore > matchScore) {
              matchScore = specialScore;
              matchReason = `特殊字符匹配: ${char}`;
            }
            break;
          }
        }

        // 更新最佳匹配
        if (matchScore > bestMatchScore) {
          bestMatchScore = matchScore;
          bestMatchIndex = idx;
          bestMatchReason = matchReason;
        }
      });

      // 如果找到了足够好的匹配
      if (bestMatchIndex !== -1 && bestMatchScore > 0.5) {
        logger(`第三层C：内容匹配成功，选项 ${_optionLabels[bestMatchIndex]}，原因: ${bestMatchReason}，得分: ${bestMatchScore.toFixed(2)}`, 'green');
        _i = bestMatchIndex;
      }
    }

    // 修改题目显示答案（如果设置允许）
    if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
      let timuele = $(TiMuList[index]).find('.mark_name')
      timuele.html(timuele.html() + "<p></p>" + agrs)
    }

    // 如果精确匹配失败，尝试多层次匹配策略
    if (_i == -1) {
      logger('精确匹配失败，启动三层匹配策略...', 'orange')

      // 第一层：直接匹配选项标签(A/B/C/D)
      const directLabelMatch = agrs.trim().toUpperCase();
      if (/^[A-D]$/.test(directLabelMatch)) {
        _i = _optionLabels.findIndex(label => label.toUpperCase() === directLabelMatch);
        if (_i !== -1) {
          logger(`第一层：直接匹配到选项标签 ${directLabelMatch}`, 'green');
        }
      }

      // 第二层：从答案中提取可能的选项标签
      if (_i === -1) {
        const possibleLabel = agrs.match(/^[A-D][\.、\s:：]/i);
        if (possibleLabel) {
          const labelToFind = possibleLabel[0].charAt(0).toUpperCase();
          const labelIndex = _optionLabels.findIndex(label => label.toUpperCase() === labelToFind);

          if (labelIndex !== -1) {
            logger(`第二层：从答案中提取到选项标签 ${labelToFind}`, 'green');
            _i = labelIndex;
          }
        }
      }

      // 第三层A：高级相似度匹配
      if (_i === -1) {
        // 首先尝试匹配选项内容
        let bestMatchIndex = -1;
        let bestMatchScore = 0;

        _a.forEach((option, idx) => {
          // 计算相似度
          const similarity = calculateSimilarity(option, agrs);
          logger(`选项 ${_optionLabels[idx]} 相似度: ${similarity}`, 'blue');

          if (similarity > bestMatchScore && similarity > 0.5) { // 降低阈值为0.5提高匹配概率
            bestMatchScore = similarity;
            bestMatchIndex = idx;
          }
        });

        // 如果找到了相似度足够高的选项
        if (bestMatchIndex !== -1) {
          logger(`第三层A：找到最佳匹配选项: ${_optionLabels[bestMatchIndex]}，相似度: ${bestMatchScore}`, 'green');
          _i = bestMatchIndex;
        }
      }

      // 第三层B：关键词匹配
      if (_i === -1) {
        let bestMatchIndex = -1;
        let bestMatchScore = 0;

        _a.forEach((option, idx) => {
          // 预处理文本，去除标点符号和多余空格
          const cleanOption = option.replace(/[.,，。、；;:：!！?？()（）\[\]【】]/g, ' ').replace(/\s+/g, ' ').trim().toLowerCase();
          const cleanAnswer = agrs.replace(/[.,，。、；;:：!！?？()（）\[\]【】]/g, ' ').replace(/\s+/g, ' ').trim().toLowerCase();

          // 提取选项和答案的关键词
          const optionKeywords = cleanOption.split(/\s+/).filter(word => word.length > 1);
          const answerKeywords = cleanAnswer.split(/\s+/).filter(word => word.length > 1);

          // 计算关键词匹配数
          let matchCount = 0;
          let totalWeight = 0;

          for (const keyword of optionKeywords) {
            const keywordWeight = keyword.length > 3 ? 2 : 1; // 长关键词权重更高
            totalWeight += keywordWeight;

            for (const ak of answerKeywords) {
              if (ak.includes(keyword) || keyword.includes(ak) ||
                calculateSimilarity(ak, keyword) > 0.7) { // 使用相似度匹配关键词
                matchCount += keywordWeight;
                break;
              }
            }
          }

          // 计算匹配得分
          const matchScore = totalWeight > 0 ? matchCount / totalWeight : 0;
          logger(`选项 ${_optionLabels[idx]} 关键词匹配得分: ${matchScore.toFixed(2)}`, 'blue');

          // 如果匹配得分足够高
          if (matchScore > bestMatchScore && matchScore > 0.4) { // 设置合理的阈值
            bestMatchScore = matchScore;
            bestMatchIndex = idx;
          }
        });

        if (bestMatchIndex !== -1) {
          logger(`第三层B：通过关键词匹配找到选项: ${_optionLabels[bestMatchIndex]}，得分: ${bestMatchScore.toFixed(2)}`, 'green');
          _i = bestMatchIndex;
        }
      }

      // 第三层C：内容包含匹配 - 增强版
      if (_i === -1) {
        let bestMatchIndex = -1;
        let bestMatchScore = 0;
        let bestMatchReason = '';

        _a.forEach((option, idx) => {
          const cleanOption = option.replace(/\s+/g, '').toLowerCase();
          const cleanAnswer = agrs.replace(/\s+/g, '').toLowerCase();
          let matchScore = 0;
          let matchReason = '';

          // 检查1：完全包含关系
          if (cleanOption.includes(cleanAnswer)) {
            matchScore = cleanAnswer.length / cleanOption.length * 0.9; // 权重0.9
            matchReason = '答案完全包含在选项中';
          }
          else if (cleanAnswer.includes(cleanOption)) {
            matchScore = cleanOption.length / cleanAnswer.length * 0.8; // 权重0.8
            matchReason = '选项完全包含在答案中';
          }

          // 检查2：关键词匹配
          if (matchScore < 0.7) { // 如果完全包含匹配得分不高
            // 提取关键词
            const optionKeywords = cleanOption.replace(/[.,，。、；;:：!！?？()（）\[\]【】]/g, ' ')
              .split(/\s+/)
              .filter(word => word.length > 1);

            const answerKeywords = cleanAnswer.replace(/[.,，。、；;:：!！?？()（）\[\]【】]/g, ' ')
              .split(/\s+/)
              .filter(word => word.length > 1);

            // 计算关键词匹配数
            let keywordMatches = 0;
            let totalKeywords = Math.max(optionKeywords.length, 1);

            for (const keyword of optionKeywords) {
              for (const answerWord of answerKeywords) {
                if (keyword.includes(answerWord) || answerWord.includes(keyword) ||
                  calculateSimilarity(keyword, answerWord) > 0.7) {
                  keywordMatches++;
                  break;
                }
              }
            }

            const keywordScore = keywordMatches / totalKeywords * 0.85; // 权重0.85
            if (keywordScore > matchScore) {
              matchScore = keywordScore;
              matchReason = `关键词匹配度: ${(keywordScore * 100).toFixed(1)}%`;
            }
          }

          // 检查3：数值匹配
          const optionNumbers = option.match(/\d+(\.\d+)?/g);
          const answerNumbers = agrs.match(/\d+(\.\d+)?/g);

          if (optionNumbers && answerNumbers) {
            for (const optNum of optionNumbers) {
              if (answerNumbers.includes(optNum)) {
                const numberScore = 0.95; // 数值匹配给予高权重
                if (numberScore > matchScore) {
                  matchScore = numberScore;
                  matchReason = `数值精确匹配: ${optNum}`;
                }
                break;
              }
            }
          }

          // 更新最佳匹配
          if (matchScore > bestMatchScore) {
            bestMatchScore = matchScore;
            bestMatchIndex = idx;
            bestMatchReason = matchReason;
          }
        });

        // 如果找到了足够好的匹配
        if (bestMatchIndex !== -1 && bestMatchScore > 0.5) {
          logger(`第三层C：内容匹配成功，选项 ${_optionLabels[bestMatchIndex]}，原因: ${bestMatchReason}，得分: ${bestMatchScore.toFixed(2)}`, 'green');
          _i = bestMatchIndex;
        }
      }

      // 第三层D：特殊情况处理 - 数字匹配
      if (_i === -1) {
        // 检查答案是否为数字，如果是数字，尝试匹配选项中的数字
        const numericAnswer = agrs.match(/\d+(\.\d+)?/);
        if (numericAnswer) {
          const answerNum = numericAnswer[0];
          logger(`尝试匹配数字答案: ${answerNum}`, 'blue');

          _a.forEach((option, idx) => {
            const numericOption = option.match(/\d+(\.\d+)?/);
            if (numericOption && numericOption[0] === answerNum) {
              logger(`第三层D：数字匹配成功，选项 ${_optionLabels[idx]} 包含相同数字 ${answerNum}`, 'green');
              _i = idx;
              return false; // 中断循环
            }
          });
        }
      }

      // 第三层E：特殊字符匹配
      if (_i === -1) {
        const specialChars = ['√', '×', '对', '错', '是', '否', 'T', 'F', '正确', '错误'];
        const lowerAnswer = agrs.toLowerCase();

        for (const char of specialChars) {
          if (lowerAnswer.includes(char.toLowerCase())) {
            _a.forEach((option, idx) => {
              if (option.toLowerCase().includes(char.toLowerCase())) {
                logger(`第三层E：特殊字符匹配成功，选项 ${_optionLabels[idx]} 与答案都包含 ${char}`, 'green');
                _i = idx;
                return false; // 中断循环
              }
            });
            if (_i !== -1) break;
          }
        }
      }

      // 第三层F：最后尝试 - 最高相似度选择
      if (_i === -1) {
        let bestMatchIndex = -1;
        let bestMatchScore = 0.3; // 设置最低阈值

        _a.forEach((option, idx) => {
          const similarity = calculateSimilarity(option, agrs);
          logger(`选项 ${_optionLabels[idx]} 最终相似度评分: ${similarity.toFixed(2)}`, 'blue');

          if (similarity > bestMatchScore) {
            bestMatchScore = similarity;
            bestMatchIndex = idx;
          }
        });

        if (bestMatchIndex !== -1) {
          logger(`第三层F：最终选择最高相似度选项 ${_optionLabels[bestMatchIndex]}，相似度: ${bestMatchScore.toFixed(2)}`, 'green');
          _i = bestMatchIndex;
        }
      }

      // 如果所有匹配方法都失败，尝试随机选择一个选项
      if (_i === -1) {
        logger('所有匹配策略均失败，尝试随机选择一个选项', 'orange');
        // 随机选择一个选项
        _i = Math.floor(Math.random() * _answerTmpArr.length);
        logger(`随机选择选项: ${_i + 1}`, 'orange');
      }

      // 记录最终选择的答案
      logger(`AI成功回答，继续处理,`, 'green');
      if (_i === -1) {
        logger(`AI无法完美匹配正确答案,请手动选择，跳过此题`, 'red');
        setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
        return;
      }
    }

    setTimeout(() => {
      try {
        // 尝试多种点击方式
        const $option = $(_answerTmpArr[_i]);
        const $parent = $option.parent();

        // 记录选项文本，帮助调试
        const optionText = $option.text().trim();
        logger(`点击选项 ${_optionLabels[_i]}: ${optionText.substring(0, 20)}${optionText.length > 20 ? '...' : ''}`, 'green');

        // 确保选项可见和可点击
        try {
          // 滚动到选项元素，确保可见
          $option[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
          logger('已滚动到选项位置', 'green');

          // 尝试设置选项的样式，增加可见性
          $option.css('background-color', '#f0f7ff');
          $option.css('border', '1px solid #1890ff');
        } catch (e) {
          logger(`滚动到选项失败: ${e.message}`, 'orange');
        }

        // 获取祖父元素，用于更完整的点击策略
        const $grandparent = $parent.parent();

        // 获取题目ID，用于设置答案
        let questionId = '';

        // 检查是否是作业网页源代码2.html格式
        if (isWorkPage2Format) {
          // 从选项元素获取题目ID
          questionId = $option.attr('qid') || $(TiMuList[index]).attr('id') || $(TiMuList[index]).attr('data');

          if (questionId) {
            logger(`检测到题目ID: ${questionId}`, 'green');

            try {
              // 方法1: 尝试直接调用addChoice函数
              if (typeof addChoice === 'function') {
                logger('尝试直接调用addChoice函数', 'green');
                try {
                  // 直接调用页面的addChoice函数
                  addChoice($option[0]);
                  logger('addChoice函数调用成功', 'green');
                } catch (e) {
                  logger(`addChoice函数调用失败: ${e.message}，尝试其他方法`, 'red');
                }
              }

              // 方法2: 无论addChoice是否成功，都尝试直接设置答案值
              const answerInput = $(`#answer${questionId}`);
              if (answerInput.length > 0) {
                // 获取选项值 - 根据题型获取不同的数据
                let optionLabel = '';
                const qtype = $option.attr('qtype') || '0'; // 默认为单选题类型

                if (qtype === '3') { // 判断题
                  // 判断题需要获取true/false值
                  optionLabel = $option.find('.num_option').attr('data') || '';
                  logger(`判断题选项值: ${optionLabel}`, 'green');
                } else { // 单选题和其他题型
                  // 单选题获取选项标签(A/B/C/D) - 增强获取方式
                  optionLabel = $option.find('.num_option').attr('data') ||
                    $option.find('.num_option').text().trim() ||
                    _optionLabels[_i] || // 使用之前收集的选项标签
                    String.fromCharCode(65 + _i); // 根据索引生成A, B, C, D...
                  logger(`单选题选项值: ${optionLabel}`, 'green');
                }

                // 设置答案值 - 使用多种方式确保设置成功
                answerInput.val(optionLabel);
                logger(`设置答案输入框值: ${optionLabel}, 题型: ${qtype || '未知'}`, 'green');

                // 触发多种事件，确保值被正确应用
                answerInput.trigger('input').trigger('change').trigger('blur');

                // 尝试使用原生DOM方法设置值
                try {
                  const inputElement = answerInput[0];
                  if (inputElement) {
                    inputElement.value = optionLabel;
                    // 设置defaultValue属性
                    inputElement.defaultValue = optionLabel;
                    // 使用setAttribute方法
                    inputElement.setAttribute('value', optionLabel);
                    // 触发原生事件
                    const event = new Event('change', { bubbles: true });
                    inputElement.dispatchEvent(event);
                    logger('使用原生DOM方法设置值成功', 'green');
                  }
                } catch (e) {
                  logger(`使用原生DOM方法设置值失败: ${e.message}`, 'orange');
                }

                // 添加选中样式到num_option元素
                $option.find('.num_option').addClass('check_answer');
              }

              // 方法3: 添加选中样式
              const optionClass = `choice${questionId}`;
              $(`.${optionClass}`).removeClass('on check_answer');
              const $numOption = $option.find(`.${optionClass}`);
              if ($numOption.length > 0) {
                $numOption.addClass('on check_answer');
                logger(`为选项添加选中样式: ${optionClass}`, 'green');
              } else {
                // 如果找不到特定类名的元素，尝试找到任何num_option元素
                const $anyNumOption = $option.find('.num_option');
                if ($anyNumOption.length > 0) {
                  $anyNumOption.addClass('on check_answer');
                  logger(`为选项添加选中样式: .num_option`, 'green');
                }
              }

              // 方法3.5: 设置ARIA属性
              $option.attr('aria-checked', 'true');
              $option.attr('aria-pressed', 'true');

              // 方法4: 尝试直接执行onclick属性中的函数
              const onclickAttr = $option.attr('onclick');
              if (onclickAttr && onclickAttr.includes('addChoice')) {
                logger('尝试直接执行onclick属性中的函数', 'green');
                try {
                  // 尝试多种方式执行onclick函数
                  // 方法4.1: 直接eval执行
                  try {
                    eval(`(function() { ${onclickAttr} }).call($option[0])`);
                    logger('通过eval执行onclick成功', 'green');
                  } catch (e) {
                    logger(`eval执行onclick失败: ${e.message}`, 'red');
                  }

                  // 方法4.2: 使用Function构造函数
                  try {
                    const clickFunc = new Function('this.addChoice(this)');
                    clickFunc.call($option[0]);
                    logger('通过Function构造函数执行成功', 'green');
                  } catch (e) {
                    logger(`Function构造函数执行失败: ${e.message}`, 'red');
                  }

                  // 方法4.3: 直接调用全局addChoice函数
                  try {
                    if (typeof window.addChoice === 'function') {
                      window.addChoice($option[0]);
                      logger('通过window.addChoice调用成功', 'green');
                    }
                  } catch (e) {
                    logger(`window.addChoice调用失败: ${e.message}`, 'red');
                  }
                } catch (e) {
                  logger(`所有onclick执行方法均失败: ${e.message}`, 'red');
                }
              }

              // 方法5: 模拟点击事件序列
              logger('模拟点击事件序列', 'green');
              ['mousedown', 'mouseup', 'click'].forEach(eventType => {
                const event = new MouseEvent(eventType, {
                  bubbles: true,
                  cancelable: true,
                  view: window
                });
                $option[0].dispatchEvent(event);
              });

              // 方法6: 使用jQuery的多重点击方法
              $option.trigger('mousedown').trigger('mouseup').trigger('click');

              // 方法7: 直接修改DOM属性
              $option.attr('aria-checked', 'true');
              $option.attr('aria-pressed', 'true');

              // 方法8: 模拟用户点击行为
              try {
                // 获取选项的位置
                const rect = $option[0].getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;

                // 创建并分发鼠标事件
                const mouseEvents = [
                  new MouseEvent('mouseover', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY }),
                  new MouseEvent('mousedown', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY }),
                  new MouseEvent('mouseup', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY }),
                  new MouseEvent('click', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY })
                ];

                mouseEvents.forEach(event => $option[0].dispatchEvent(event));
                logger('模拟用户点击行为完成', 'green');
              } catch (e) {
                logger(`模拟用户点击行为失败: ${e.message}`, 'red');
              }

              logger('已尝试多种方法选中答案', 'green');

              // 成功处理后返回
              setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
              return;
            } catch (e) {
              logger(`处理作业网页源代码2格式失败: ${e.message}，尝试其他方法`, 'red');
            }
          }
        }

        // 检查是否有radio或checkbox输入元素
        const $radio = $option.find('input[type="radio"]');
        const $checkbox = $option.find('input[type="checkbox"]');

        // 尝试方法0: 如果有radio或checkbox，直接设置checked属性
        if ($radio.length > 0) {
          logger(`找到radio输入元素，设置checked`, 'green');
          $radio.prop('checked', true);
          $radio.attr('checked', 'checked');
          $radio.trigger('change');
          $radio.click();
        } else if ($checkbox.length > 0) {
          logger(`找到checkbox输入元素，设置checked`, 'green');
          $checkbox.prop('checked', true);
          $checkbox.attr('checked', 'checked');
          $checkbox.trigger('change');
          $checkbox.click();
        }

        // 尝试方法0.5: 检查是否支持addChoice函数 (针对作业网页源代码2.html)
        if (typeof addChoice === 'function') {
          try {
            logger('检测到addChoice函数，尝试直接调用', 'green');
            // 无论是否有onclick属性，都尝试调用addChoice函数
            addChoice($option[0]);

            // 获取题目ID
            const questionId = $option.attr('qid') || $(TiMuList[index]).attr('id') || $(TiMuList[index]).attr('data');
            if (questionId) {
              // 设置答案值
              const answerInput = $(`#answer${questionId}`);
              if (answerInput.length > 0) {
                // 获取选项值
                let optionLabel = $option.find('.num_option').attr('data') ||
                  $option.find('.num_option').text().trim();

                // 设置答案值
                answerInput.val(optionLabel);
                logger(`设置答案输入框值: ${optionLabel}`, 'green');

                // 触发change事件，确保值被正确应用
                answerInput.trigger('change');
              }

              // 添加选中样式到num_option元素
              $option.find('.num_option').addClass('check_answer');

              // 设置ARIA属性
              $option.attr('aria-checked', 'true');
              $option.attr('aria-pressed', 'true');
            }
          } catch (e) {
            logger(`addChoice函数调用失败: ${e.message}，尝试其他方法`, 'red');
          }
        }

        // 检查选项是否有特定的点击元素
        const $clickTarget = $option.find('a.check') ||
          $option.find('input[type="radio"]') ||
          $option.find('.check_answer') ||
          $option.find('[class*="check"]') ||
          $option;

        // 尝试方法1: 直接点击目标元素
        $clickTarget.click();

        // 尝试方法2: 点击选项本身
        setTimeout(() => {
          $option.click();
        }, 50);

        // 尝试方法3: 点击父元素
        setTimeout(() => {
          $parent.click();
        }, 100);

        // 尝试方法4: 点击祖父元素
        setTimeout(() => {
          $grandparent.click();
        }, 150);

        // 尝试方法5: 模拟点击事件 - 使用更多事件类型
        setTimeout(() => {
          // 模拟鼠标点击
          ['mousedown', 'mouseup', 'click'].forEach(eventType => {
            const event = new MouseEvent(eventType, {
              bubbles: true,
              cancelable: true,
              view: window
            });
            _answerTmpArr[_i].dispatchEvent(event);
          });
        }, 200);

        // 尝试方法6: jQuery触发多种事件
        setTimeout(() => {
          $option.trigger('mousedown').trigger('mouseup').trigger('click');
        }, 250);

        // 尝试方法7: 查找并点击选项内的单选按钮
        setTimeout(() => {
          const radioBtn = $option.find('input[type="radio"]')[0];
          if (radioBtn) {
            radioBtn.checked = true;
            radioBtn.dispatchEvent(new Event('change', { bubbles: true }));
            logger('通过设置单选按钮checked属性完成选择', 'green');
          }
        }, 300);

        // 尝试方法8: 添加选中样式类
        setTimeout(() => {
          $option.addClass('chosen selected checked active on');
          $parent.addClass('chosen selected checked active on');
        }, 350);

        // 尝试方法9: 查找并点击可能的标记元素
        setTimeout(() => {
          const $possibleMarks = $option.find('span, i, em, b, strong, label, div');
          if ($possibleMarks.length > 0) {
            $possibleMarks.click();
          }
        }, 400);

        // 尝试方法10: 检查特殊属性并调用相关函数
        setTimeout(() => {
          // 检查是否有onclick属性
          const onclickAttr = $option.attr('onclick');
          if (onclickAttr) {
            logger(`检测到onclick属性: ${onclickAttr}`, 'green');

            // 如果包含addChoice函数
            if (onclickAttr.includes('addChoice')) {
              try {
                // 尝试获取qid和qtype
                const qid = $option.attr('qid');
                const qtype = $option.attr('qtype');

                if (qid && qtype) {
                  logger(`找到qid=${qid}, qtype=${qtype}，尝试手动设置答案`, 'green');

                  // 尝试直接设置答案输入框的值
                  const answerInput = $(`#answer${qid}`);
                  if (answerInput.length > 0) {
                    const optionLabel = $option.find('.num_option').attr('data') || '';
                    answerInput.val(optionLabel);
                    logger(`已设置答案输入框的值为: ${optionLabel}`, 'green');
                  }
                }
              } catch (e) {
                logger(`处理特殊属性失败: ${e.message}`, 'red');
              }
            }
          }
        }, 450);

        // 尝试方法11: 直接设置表单值
        setTimeout(() => {
          // 尝试获取题目ID
          const questionId = $(TiMuList[index]).attr('id') || $(TiMuList[index]).attr('data');
          if (questionId) {
            const numericId = questionId.replace(/\D/g, '');
            if (numericId) {
              // 尝试直接设置答案表单值
              const answerInput = $(`#answer${numericId}`);
              if (answerInput.length > 0) {
                const answerType = $(`#answertype${numericId}`).val();
                if (answerType === '0') { // 单选题
                  const optionLabel = _optionLabels[_i];
                  answerInput.val(optionLabel);
                  logger(`直接设置答案表单值: ${optionLabel}`, 'green');
                }
              }
            }
          }
        }, 450);

        logger('已尝试多种方式点击选项，准备切换下一题', 'green');
        setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
      } catch (e) {
        logger(`点击选项失败: ${e.message}，尝试备用方法`, 'red');

        try {
          // 尝试通过jQuery触发点击
          $(_answerTmpArr[_i]).trigger('click');
          logger('使用jQuery触发点击成功', 'green');

          // 尝试设置样式来模拟选中状态
          $(_answerTmpArr[_i]).addClass('chosen selected');
          $(_answerTmpArr[_i]).css('background-color', '#f0f7ff');
          $(_answerTmpArr[_i]).find('input[type="radio"]').prop('checked', true);

          logger('已通过多种方式尝试选中选项', 'green');
        } catch (err) {
          logger(`所有点击方法都失败: ${err.message}，跳过此题`, 'red');
        }

        setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
      }
    }, 300)
  }).catch((error) => {
    logger(`获取答案失败: ${error}，尝试随机选择并继续处理下一题`, 'red');

    // 随机选择一个选项
    const randomIndex = Math.floor(Math.random() * _answerTmpArr.length);
    logger(`随机选择选项: ${randomIndex + 1}`, 'orange');

    // 尝试点击随机选项
    try {
      const $option = $(_answerTmpArr[randomIndex]);

      // 检查是否是作业网页源代码2.html格式
      if (isWorkPage2Format) {
        // 尝试使用addChoice函数
        if (typeof addChoice === 'function') {
          logger('尝试直接调用addChoice函数', 'green');
          try {
            addChoice($option[0]);
            logger('addChoice函数调用成功', 'green');
          } catch (e) {
            logger(`addChoice函数调用失败: ${e.message}，尝试其他方法`, 'red');
            $option.click();
          }
        } else {
          $option.click();
        }
      } else {
        $option.click();
      }
    } catch (e) {
      logger(`点击随机选项失败: ${e.message}`, 'red');
    }

    // 无论点击是否成功，都继续处理下一题
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
  })
}

/**
 * 处理多选题
 */
function handleMultiChoice(index, TiMuList, _question, _type) {
  // 获取选项列表 - 适配不同页面结构
  let _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.answer_p')

  // 记录是否是作业网页源代码2.html格式
  let isWorkPage2Format = false;

  // 作业页面特殊选择器 - 增加多种可能的选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.mark_letter').find('li')

    // 尝试其他常见的作业选项选择器
    if (_answerTmpArr.length === 0) {
      _answerTmpArr = $(TiMuList[index]).find('.mark_letter li')
    }

    if (_answerTmpArr.length === 0) {
      _answerTmpArr = $(TiMuList[index]).find('ul.mark_letter li')
    }

    if (_answerTmpArr.length === 0) {
      _answerTmpArr = $(TiMuList[index]).find('.mark_letter > li')
    }

    // 添加对作业网页源代码2.html中的选择器支持
    if (_answerTmpArr.length === 0) {
      const answerBgElements = $(TiMuList[index]).find('.clearfix.answerBg');
      if (answerBgElements.length > 0) {
        _answerTmpArr = answerBgElements;
        isWorkPage2Format = true;
        logger('检测到作业网页源代码2格式的多选题', 'blue');
      }
    }
  }

  // 章节测验选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('li')
  }

  // 如果仍然没找到选项，尝试其他可能的选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.fl')

    if (_answerTmpArr.length === 0) {
      logger(`第 ${index + 1} 题无法找到选项列表，尝试其他方式`, 'orange')

      // 尝试更多可能的选择器
      const possibleSelectors = [
        'ul.ulTop li',
        'ul li',
        '.mark_option li',
        '.mark_option div',
        '.mark_option > div',
        '.mark_option .option',
        '.que-answer li',
        '.que-answer .option',
        '.que-answer-content li',
        '.que-answer-options li',
        '.mark_letter .mark_letter_item',
        '.mark_cont li',
        '.clearfix.answerBg', // 作业网页源代码2.html中的选择器
        '.stem_answer .clearfix', // 通用选择器
        '.stem_answer [class*="answer"]', // 包含answer的元素
        '[class*="option"]', // 包含option的元素
        '[class*="choice"]', // 包含choice的元素
        '[role="checkbox"]' // ARIA角色为checkbox的元素
      ];

      // 尝试每个可能的选择器
      for (const selector of possibleSelectors) {
        _answerTmpArr = $(TiMuList[index]).find(selector);
        if (_answerTmpArr.length > 0) {
          logger(`使用选择器 "${selector}" 找到了选项列表`, 'green');
          break;
        }
      }
    }
  }

  // 如果还是没找到选项
  if (_answerTmpArr.length === 0) {
    logger(`第 ${index + 1} 题无法找到选项列表，跳过此题`, 'red')
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time)
    return
  }

  // 检查题目是否已作答
  let isAnswered = false;
  let answeredCount = 0;
  for (var i = 0; i < _answerTmpArr.length; i++) {
    // 检查多种可能的已答标记
    const $option = $(_answerTmpArr[i]);
    const $parent = $option.parent();

    if (
      ($parent.find('span.check_answer').length > 0) ||
      ($parent.find('span.check_answer_dx').length > 0) ||
      ($option.hasClass('chosen')) ||
      ($option.hasClass('selected')) ||
      ($option.attr('aria-selected') === 'true')
    ) {
      answeredCount++;
    }
  }

  // 如果有任何选项被选中，认为题目已作答
  if (answeredCount > 0) {
    logger(`第 ${index + 1} 题已作答(${answeredCount}个选项)，准备切换下一题`, 'green')
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, 300);
    return;
  }

  // 构建题目内容，包含选项
  var mergedAnswers = []
  _answerTmpArr.each(function () {
    var answerText = $(this).text().replace(/[ABCD]/g, '').trim()
    mergedAnswers.push(answerText)
  })
  mergedAnswers = mergedAnswers.join("|")
  _question = "多选题:" + _question + '\n' + mergedAnswers

  // 获取答案
  getAnswer(_type, _question).then((agrs) => {
    // 调试信息
    logger(`获取到答案: ${agrs}`, 'blue')

    // 检查答案是否为空或无效
    if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案' || agrs.includes('错误:') || agrs.includes('题目找到但答案无效') || agrs.includes('未找到有效答案')) {
      logger('未获取到有效答案或题库返回错误，跳过此题', 'red');
      localStorage.setItem('GPTJsSetting.sub', false);
      setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
      return;
    }

    // 修改题目显示答案（如果设置允许）
    if (localStorage.getItem('GPTJsSetting.alterTitle') === 'true') {
      let timuele = $(TiMuList[index]).find('.mark_name')
      timuele.html(timuele.html() + "<p></p>" + agrs)
    }

    // 选择匹配的选项
    let selectedOptions = 0;
    $.each(_answerTmpArr, (i, t) => {
      const optionText = cleanTextContent($(t).html());
      const $option = $(_answerTmpArr[i]);
      const $parent = $option.parent();

      // 尝试精确匹配
      if (agrs.indexOf(optionText) !== -1) {
        selectedOptions++;
        logger(`匹配选项 ${i + 1}: ${optionText}`, 'green');

        // 尝试多种点击方式
        setTimeout(() => {
          try {
            // 特殊处理作业网页源代码2.html格式
            if (isWorkPage2Format) {
              // 从选项元素获取题目ID
              const questionId = $option.attr('qid');

              if (questionId) {
                logger(`检测到题目ID: ${questionId}`, 'green');

                try {
                  // 方法1: 尝试直接调用addChoice函数
                  if (typeof addChoice === 'function') {
                    logger('尝试直接调用addChoice函数', 'green');
                    try {
                      // 直接调用页面的addChoice函数
                      addChoice($option[0]);
                      logger('addChoice函数调用成功', 'green');
                    } catch (e) {
                      logger(`addChoice函数调用失败: ${e.message}，尝试其他方法`, 'red');
                    }
                  }

                  // 获取选项标签
                  const optionLabel = $option.find('.num_option').attr('data') || '';

                  // 方法2: 无论addChoice是否成功，都尝试直接设置答案值
                  const answerInput = $(`#answer${questionId}`);
                  if (answerInput.length > 0) {
                    // 获取当前值
                    const currentValue = answerInput.val() || '';
                    // 添加新选项，确保不重复
                    if (!currentValue.includes(optionLabel)) {
                      const newValue = currentValue ? (currentValue + optionLabel) : optionLabel;
                      answerInput.val(newValue);
                      logger(`设置多选答案表单值: ${newValue}`, 'green');

                      // 触发change事件，确保值被正确应用
                      answerInput.trigger('change');
                    }
                  }

                  // 方法3: 添加选中样式
                  const optionClass = `choice${questionId}`;
                  $option.find(`.${optionClass}`).addClass('on');

                  // 方法4: 尝试直接执行onclick属性中的函数
                  const onclickAttr = $option.attr('onclick');
                  if (onclickAttr && onclickAttr.includes('addChoice')) {
                    logger('尝试直接执行onclick属性中的函数', 'green');
                    try {
                      // 创建并执行一个新的函数，内容是onclick属性的值
                      const clickFunc = new Function($option[0], onclickAttr);
                      clickFunc($option[0]);
                      logger('onclick函数执行成功', 'green');
                    } catch (e) {
                      logger(`onclick函数执行失败: ${e.message}`, 'red');
                    }
                  }

                  // 方法5: 模拟点击事件
                  logger('模拟点击事件', 'green');
                  const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                  });
                  $option[0].dispatchEvent(clickEvent);

                  // 方法6: 使用jQuery的click方法
                  $option.click();

                  logger('已尝试多种方法选中答案', 'green');

                  return; // 成功处理后返回，不执行后续点击尝试
                } catch (e) {
                  logger(`处理作业网页源代码2格式失败: ${e.message}，尝试其他方法`, 'red');
                }
              }
            }

            // 常规处理 - 检查是否支持addChoice函数
            if (typeof addChoice === 'function' && $option.attr('onclick') && $option.attr('onclick').includes('addChoice')) {
              try {
                logger('检测到addChoice函数，尝试直接调用', 'green');
                addChoice($option[0]);
              } catch (e) {
                logger(`addChoice函数调用失败: ${e.message}`, 'red');
              }
            }

            // 尝试方法1: 直接点击选项
            $option.click();

            // 尝试方法2: 点击父元素
            setTimeout(() => {
              $parent.click();
            }, 50);

            // 尝试方法3: 模拟点击事件
            setTimeout(() => {
              const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
              });
              _answerTmpArr[i].dispatchEvent(clickEvent);
            }, 100);

            // 尝试方法4: jQuery触发点击
            setTimeout(() => {
              $option.trigger('click');
            }, 150);

            // 尝试方法5: 设置表单值
            setTimeout(() => {
              // 尝试获取题目ID和选项标签
              const questionId = $(TiMuList[index]).attr('id') || $(TiMuList[index]).attr('data');
              const optionLabel = $option.find('.num_option').attr('data') ||
                $option.find('.num_option').text().trim() ||
                String.fromCharCode(65 + i); // A, B, C, D...

              if (questionId) {
                const numericId = questionId.replace(/\D/g, '');
                if (numericId) {
                  // 尝试直接设置答案表单值
                  const answerInput = $(`#answer${numericId}`);
                  if (answerInput.length > 0) {
                    // 获取当前值
                    const currentValue = answerInput.val() || '';
                    // 添加新选项，确保不重复
                    if (!currentValue.includes(optionLabel)) {
                      const newValue = currentValue ? (currentValue + optionLabel) : optionLabel;
                      answerInput.val(newValue);
                      logger(`设置多选答案表单值: ${newValue}`, 'green');
                    }
                  }
                }
              }
            }, 200);
          } catch (e) {
            logger(`点击选项 ${i + 1} 失败: ${e.message}`, 'red');
          }
        }, 300 + (i * 200)); // 错开点击时间，避免冲突
      } else {
        // 如果精确匹配失败，尝试模糊匹配
        const similarity = calculateSimilarity(optionText, agrs);
        if (similarity > 0.7) { // 设置更高的阈值，因为是多选题
          selectedOptions++;
          logger(`模糊匹配选项 ${i + 1}: ${optionText}，相似度: ${similarity}`, 'green');

          setTimeout(() => {
            try {
              $option.click();
              setTimeout(() => { $parent.click(); }, 50);
            } catch (e) {
              logger(`点击选项 ${i + 1} 失败: ${e.message}`, 'red');
            }
          }, 300 + (i * 200));
        }
      }
    });

    if (selectedOptions > 0) {
      logger(`自动选择了 ${selectedOptions} 个选项，准备切换下一题`, 'green');
    } else {
      logger('未能匹配到任何选项，可能需要手动选择', 'red');
    }

    // 无论如何，继续下一题
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time + (selectedOptions * 200));
  }).catch((agrs) => {
    logger('获取答案失败，跳过此题', 'red');
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
  })
}

/**
 * 处理填空题
 */
function handleFillBlank(index, TiMuList, _question, _type) {
  logger(`开始处理第 ${index + 1} 题填空题`, 'blue');

  // 检查是否为作业页面的填空题
  const currentQuestion = $(TiMuList[index]);
  const questionId = currentQuestion.attr('id') || currentQuestion.attr('data');

  // 检查题目类型
  if (questionId) {
    const answerTypeInput = currentQuestion.find(`#answertype${questionId}`);
    if (answerTypeInput.length > 0) {
      const answerType = answerTypeInput.val();
      if (answerType === '2') {
        // 这是作业页面的填空题，使用专门的处理函数
        return handleWorkPageFillBlank(index, TiMuList, currentQuestion, questionId, _question, _type);
      }
    }
  }

  // 获取文本框列表 - 增强查找能力
  let _textareaList = [];

  // 尝试多种可能的填空框选择器
  const possibleSelectors = [
    // 标准选择器
    '.stem_answer .Answer .divText .textDIV textarea',
    '.stem_answer textarea',
    // 作业页面常见选择器
    '.mark_answer input[type="text"]',
    '.mark_answer textarea',
    '.mark_answer .blank',
    '.mark_answer [class*="blank"]',
    '.mark_answer [class*="fill"]',
    '.mark_answer .fillblank',
    '.mark_answer .fill_answer',
    // 通用选择器
    'input[type="text"]',
    'textarea.blank',
    '.blank',
    '[class*="blank"]',
    '[class*="fill"]'
  ];

  // 在当前题目中尝试各种选择器
  for (const selector of possibleSelectors) {
    const elements = $(TiMuList[index]).find(selector);
    if (elements.length > 0) {
      logger(`使用选择器 "${selector}" 找到 ${elements.length} 个填空框`, 'green');
      _textareaList = elements.toArray();
      break;
    }
  }

  // 如果仍然没找到，尝试在整个文档中查找
  if (_textareaList.length === 0) {
    logger('在题目内未找到填空框，尝试在整个文档中查找', 'orange');

    // 尝试查找可能与当前题目相关的填空框
    const questionId = $(TiMuList[index]).attr('id') || '';
    const questionIndex = index + 1;

    for (const selector of possibleSelectors) {
      // 先尝试查找与题目ID或索引相关的元素
      let elements = $(`[id*="${questionId}"] ${selector}, [id*="question${questionIndex}"] ${selector}, [id*="q${questionIndex}"] ${selector}`);

      // 如果没找到，尝试一般选择器
      if (elements.length === 0) {
        elements = $(selector);
      }

      if (elements.length > 0) {
        logger(`在文档中使用选择器 "${selector}" 找到 ${elements.length} 个填空框`, 'green');
        _textareaList = elements.toArray();
        break;
      }
    }

    // 尝试在iframe中查找
    if (_textareaList.length === 0) {
      try {
        const iframes = document.querySelectorAll('iframe');
        logger(`尝试在 ${iframes.length} 个iframe中查找填空框`, 'blue');

        for (const iframe of iframes) {
          try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

            // 在iframe中尝试所有可能的选择器
            for (const selector of possibleSelectors) {
              const elements = $(iframeDoc).find(selector);
              if (elements.length > 0) {
                logger(`在iframe中使用选择器 "${selector}" 找到 ${elements.length} 个填空框`, 'green');
                _textareaList = elements.toArray();
                break;
              }
            }

            if (_textareaList.length > 0) break;
          } catch (e) {
            // 跨域iframe无法访问
            continue;
          }
        }
      } catch (e) {
        logger(`在iframe中查找填空框时出错: ${e.message}`, 'red');
      }
    }
  }

  // 如果仍然没找到填空框
  if (_textareaList.length === 0) {
    logger(`第 ${index + 1} 题未找到填空框，跳过此题`, 'red');
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
    return;
  }

  logger(`找到 ${_textareaList.length} 个填空框`, 'green');

  // 不再判断题目是否已作答，而是清空现有内容并重新填写
  // 清空所有文本框内容
  for (const textarea of _textareaList) {
    const $textarea = $(textarea);
    const _id = $textarea.attr('id');

    // 检查UEditor内容并清空
    if (_id && typeof UE !== 'undefined' && UE.getEditor && UE.getEditor(_id)) {
      const editor = UE.getEditor(_id);
      const content = editor.getContent();
      if (content && content !== '') {
        logger(`清空第 ${index + 1} 题已有内容，准备重新填写`, 'orange');
        editor.setContent(''); // 清空内容
      }
    } else if ($textarea.is('input') || $textarea.is('textarea')) {
      // 清空普通input/textarea内容
      if ($textarea.val() && $textarea.val().trim() !== '') {
        $textarea.val('');
        $textarea.trigger('input').trigger('change');
      }
    }
  }

  // 构建题目内容
  _question = "填空题,用\"|\"分割多个答案:" + _question;

  // 获取答案
  getAnswer(_type, _question).then((agrs) => {
    logger(`获取到答案: ${agrs}`, 'green');

    // 解析答案，处理多个空的情况
    let _answerTmpArr = agrs.split('|');
    logger(`解析出 ${_answerTmpArr.length} 个答案`, 'blue');

    // 填写答案到每个文本框
    let filledCount = 0;

    for (let i = 0; i < _textareaList.length; i++) {
      const textarea = _textareaList[i];
      const $textarea = $(textarea);
      const _currentId = $textarea.attr('id');

      // 获取当前空对应的答案，如果没有对应索引的答案，使用第一个答案
      const answerText = _answerTmpArr[i] || _answerTmpArr[0];

      try {
        // 尝试使用UEditor填写
        if (_currentId && typeof UE !== 'undefined' && UE.getEditor) {
          try {
            let editor = UE.getEditor(_currentId);
            if (editor) {
              setTimeout(() => {
                try {
                  editor.setContent(answerText);
                  logger(`使用UEditor成功填写第 ${i + 1} 个空: ${answerText}`, 'green');
                  filledCount++;
                } catch (e) {
                  logger(`使用UEditor填写第 ${i + 1} 个空失败: ${e.message}`, 'red');
                }
              }, 300);
              continue; // 如果使用UEditor成功，跳过后续方法
            }
          } catch (e) {
            logger(`使用UEditor填写第 ${i + 1} 个空时出错: ${e.message}`, 'red');
          }
        }

        // 尝试直接设置input/textarea值
        if ($textarea.is('input') || $textarea.is('textarea')) {
          $textarea.val(answerText);

          // 触发change事件，确保值被正确处理
          $textarea.trigger('input').trigger('change');

          logger(`直接设置第 ${i + 1} 个空的值成功: ${answerText}`, 'green');
          filledCount++;
        } else {
          // 如果不是标准输入元素，尝试其他方法
          $textarea.text(answerText);
          $textarea.attr('value', answerText);
          logger(`尝试其他方法设置第 ${i + 1} 个空的值: ${answerText}`, 'orange');
          filledCount++;
        }
      } catch (e) {
        logger(`填写第 ${i + 1} 个空时出错: ${e.message}`, 'red');
      }
    }

    if (filledCount > 0) {
      logger(`成功填写了 ${filledCount}/${_textareaList.length} 个空，准备切换下一题`, 'green');
    } else {
      logger('填写答案可能不成功，但仍将继续下一题', 'orange');
    }

    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
  }).catch((error) => {
    logger(`获取答案失败: ${error}，使用默认答案`, 'orange');

    // 使用默认答案
    const defaultAnswer = '6666';
    logger(`使用默认答案: ${defaultAnswer}`, 'orange');

    // 填写默认答案到每个文本框
    let filledCount = 0;

    for (let i = 0; i < _textareaList.length; i++) {
      const textarea = _textareaList[i];
      const $textarea = $(textarea);
      const _currentId = $textarea.attr('id');

      try {
        // 尝试使用UEditor填写
        if (_currentId && typeof UE !== 'undefined' && UE.getEditor) {
          try {
            let editor = UE.getEditor(_currentId);
            if (editor) {
              setTimeout(() => {
                try {
                  editor.setContent(defaultAnswer);
                  logger(`使用UEditor成功填写第 ${i + 1} 个空: ${defaultAnswer}`, 'green');
                  filledCount++;
                } catch (e) {
                  logger(`使用UEditor填写第 ${i + 1} 个空失败: ${e.message}`, 'red');
                }
              }, 300);
              continue; // 如果使用UEditor成功，跳过后续方法
            }
          } catch (e) {
            logger(`使用UEditor填写第 ${i + 1} 个空时出错: ${e.message}`, 'red');
          }
        }

        // 尝试直接设置input/textarea值
        if ($textarea.is('input') || $textarea.is('textarea')) {
          $textarea.val(defaultAnswer);

          // 触发change事件，确保值被正确处理
          $textarea.trigger('input').trigger('change');

          logger(`直接设置第 ${i + 1} 个空的值成功: ${defaultAnswer}`, 'green');
          filledCount++;
        } else {
          // 如果不是标准输入元素，尝试其他方法
          $textarea.text(defaultAnswer);
          $textarea.attr('value', defaultAnswer);
          logger(`尝试其他方法设置第 ${i + 1} 个空的值: ${defaultAnswer}`, 'orange');
          filledCount++;
        }
      } catch (e) {
        logger(`填写第 ${i + 1} 个空时出错: ${e.message}`, 'red');
      }
    }

    if (filledCount > 0) {
      logger(`成功填写了 ${filledCount}/${_textareaList.length} 个空，准备切换下一题`, 'green');
    } else {
      logger('填写答案可能不成功，但仍将继续下一题', 'orange');
    }

    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
  });
}

/**
 * 处理作业页面的填空题
 * 专门处理answertype=2的填空题
 */
function handleWorkPageFillBlank(index, TiMuList, currentQuestion, questionId, _question, _type) {
  logger(`开始处理作业页面的填空题，题目ID: ${questionId}`, 'blue');

  // 获取填空数量
  const tiankongSizeInput = currentQuestion.find(`input[name="tiankongsize${questionId}"]`);
  const blankCount = parseInt(tiankongSizeInput.val() || "1");
  logger(`填空题有 ${blankCount} 个空`, 'blue');

  // 构建题目内容
  _question = "填空题,用\"|\"分割多个答案:" + _question;

  // 获取答案
  getAnswer(_type, _question).then((agrs) => {
    logger(`获取到答案: ${agrs}`, 'green');

    // 检查答案是否为空或无效
    if (!agrs || agrs.trim() === '' || agrs.includes('未找到答案') || agrs === '暂无答案' || agrs.includes('错误:') || agrs.includes('题目找到但答案无效') || agrs.includes('未找到有效答案')) {
      logger('获取到的答案无效，使用默认答案: 6666', 'orange');
      agrs = '6666'; // 设置默认答案
    }

    // 解析答案，处理多个空的情况
    let blankAnswers = [];

    // 尝试多种分隔符
    if (agrs.includes('###')) {
      blankAnswers = agrs.split('###');
    } else if (agrs.includes('；')) {
      blankAnswers = agrs.split('；');
    } else if (agrs.includes(';')) {
      blankAnswers = agrs.split(';');
    } else if (agrs.includes('、')) {
      blankAnswers = agrs.split('、');
    } else if (agrs.includes(',')) {
      blankAnswers = agrs.split(',');
    } else if (agrs.includes('，')) {
      blankAnswers = agrs.split('，');
    } else if (agrs.includes('|')) {
      blankAnswers = agrs.split('|');
    } else {
      // 如果没有分隔符，整个答案作为一个空的答案
      blankAnswers = [agrs];
    }

    // 清理答案
    blankAnswers = blankAnswers.map(ans => ans.trim()).filter(ans => ans);
    logger(`解析出 ${blankAnswers.length} 个空的答案`, 'blue');

    // 填写每个空的答案
    let filledCount = 0;

    for (let i = 1; i <= blankCount; i++) {
      const editorId = `answerEditor${questionId}${i}`;

      // 获取当前空对应的答案，如果没有对应索引的答案，使用第一个答案
      const answerContent = (i <= blankAnswers.length) ?
        blankAnswers[i - 1] :
        (blankAnswers.length > 0 ? blankAnswers[0] : "");

      try {
        // 尝试使用UEditor填写
        if (typeof UE !== 'undefined' && UE.getEditor) {
          let editor = UE.getEditor(editorId);
          if (editor) {
            setTimeout(() => {
              try {
                editor.setContent(`<p>${answerContent}</p>`);
                editor.sync(); // 同步内容到表单
                logger(`成功填写第 ${i} 个空: ${answerContent}`, 'green');
                filledCount++;
              } catch (e) {
                logger(`填写第 ${i} 个空失败: ${e.message}`, 'red');
              }
            }, 300 * i); // 错开时间，避免冲突
          } else {
            // 如果找不到编辑器，尝试直接设置表单值
            const formField = $(`#${editorId}`);
            if (formField.length > 0) {
              formField.val(`<p>${answerContent}</p>`);
              formField.trigger('change');
              logger(`直接设置第 ${i} 个空的表单值: ${answerContent}`, 'green');
              filledCount++;
            } else {
              logger(`未找到第 ${i} 个空的编辑器或表单字段`, 'red');
            }
          }
        } else {
          logger('UEditor未定义，无法填写答案', 'red');
        }
      } catch (e) {
        logger(`处理第 ${i} 个空时出错: ${e.message}`, 'red');
      }
    }

    if (filledCount > 0) {
      logger(`成功填写了 ${filledCount}/${blankCount} 个空，准备切换下一题`, 'green');
    } else {
      logger('填写答案可能不成功，但仍将继续下一题', 'orange');
    }

    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time + (blankCount * 300));
  }).catch((error) => {
    logger(`获取答案失败: ${error}，使用默认答案`, 'orange');

    // 使用默认答案
    const defaultAnswer = '6666';
    logger(`使用默认答案: ${defaultAnswer}`, 'orange');

    // 填写默认答案到每个空
    let filledCount = 0;

    for (let i = 1; i <= blankCount; i++) {
      const editorId = `answerEditor${questionId}${i}`;

      try {
        // 尝试使用UEditor填写
        if (typeof UE !== 'undefined' && UE.getEditor) {
          let editor = UE.getEditor(editorId);
          if (editor) {
            setTimeout(() => {
              try {
                editor.setContent(`<p>${defaultAnswer}</p>`);
                editor.sync(); // 同步内容到表单
                logger(`成功填写第 ${i} 个空: ${defaultAnswer}`, 'green');
                filledCount++;
              } catch (e) {
                logger(`填写第 ${i} 个空失败: ${e.message}`, 'red');
              }
            }, 300 * i); // 错开时间，避免冲突
          } else {
            // 如果找不到编辑器，尝试直接设置表单值
            const formField = $(`#${editorId}`);
            if (formField.length > 0) {
              formField.val(`<p>${defaultAnswer}</p>`);
              formField.trigger('change');
              logger(`直接设置第 ${i} 个空的表单值: ${defaultAnswer}`, 'green');
              filledCount++;
            } else {
              logger(`未找到第 ${i} 个空的编辑器或表单字段`, 'red');
            }
          }
        } else {
          logger('UEditor未定义，无法填写答案', 'red');
        }
      } catch (e) {
        logger(`处理第 ${i} 个空时出错: ${e.message}`, 'red');
      }
    }

    if (filledCount > 0) {
      logger(`成功填写了 ${filledCount}/${blankCount} 个空，准备切换下一题`, 'green');
    } else {
      logger('填写答案可能不成功，但仍将继续下一题', 'orange');
    }

    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time + (blankCount * 300));
  });
}

/**
 * 处理判断题
 */
function handleJudgment(index, TiMuList, _question, _type) {
  // 获取选项列表 - 适配不同页面结构
  let _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.answer_p')

  // 作业页面特殊选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.mark_letter').find('li')
  }

  // 章节测验选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('li')
  }

  // 添加对作业网页源代码2.html中的选择器支持
  if (_answerTmpArr.length === 0) {
    // 先尝试查找判断题的选择器(clearfix answerBg)
    const answerBgElements = $(TiMuList[index]).find('.clearfix.answerBg');
    if (answerBgElements.length > 0) {
      _answerTmpArr = answerBgElements;
      logger('检测到作业网页源代码2格式的判断题', 'blue');
    } else {
      // 再尝试查找单选题的选择器(clearfix answerBg workTextWrap)
      const answerBgWorkTextElements = $(TiMuList[index]).find('.clearfix.answerBg.workTextWrap');
      if (answerBgWorkTextElements.length > 0) {
        _answerTmpArr = answerBgWorkTextElements;
        logger('检测到作业网页源代码2格式的单选题作为判断题处理', 'blue');
      }
    }
  }

  // 如果仍然没找到选项，尝试其他可能的选择器
  if (_answerTmpArr.length === 0) {
    _answerTmpArr = $(TiMuList[index]).find('.stem_answer').find('.fl')
    if (_answerTmpArr.length === 0) {
      logger(`第 ${index + 1} 题无法找到选项列表，尝试其他方式`, 'orange')
      _answerTmpArr = $(TiMuList[index]).find('ul.ulTop li')
      if (_answerTmpArr.length === 0) {
        _answerTmpArr = $(TiMuList[index]).find('ul li')
      }
    }
  }

  // 如果还是没找到选项
  if (_answerTmpArr.length === 0) {
    logger(`第 ${index + 1} 题无法找到选项列表，跳过此题`, 'red')
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time)
    return
  }

  // 检查题目是否已作答
  let isAnswered = false;
  for (var i = 0; i < _answerTmpArr.length; i++) {
    // 检查多种可能的已答标记
    const $option = $(_answerTmpArr[i]);
    const $parent = $option.parent();

    if (
      ($parent.find('span.check_answer').length > 0) ||
      ($parent.find('span.check_answer_dx').length > 0) ||
      ($option.hasClass('chosen')) ||
      ($option.hasClass('selected')) ||
      ($option.attr('aria-selected') === 'true')
    ) {
      logger(`第 ${index + 1} 题已作答，准备切换下一题`, 'green')
      isAnswered = true;
      break;
    }
  }

  if (isAnswered) {
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, 300);
    return;
  }

  // 构建题目内容
  _question = "判断题(只回答正确或错误):" + _question
  if (_answerTmpArr.length > 0) {
    _question += '\n' + _answerTmpArr.text()
  }

  let _a = []
  $.each(_answerTmpArr, (i, t) => {
    _a.push($(t).text().trim())
  })

  // 获取答案
  getAnswer(_type, _question).then((agrs) => {
    let _true = '正确|是|对|√|T|ri'
    let _false = '错误|否|错|×|F|wr'
    let _i = -1

    // 检查是否为错误信息
    if (agrs.includes('未找到答案') || agrs === '暂无答案' || agrs.includes('错误:') || agrs.includes('题目找到但答案无效') || agrs.includes('未找到有效答案')) {
      // 记录错误但继续尝试处理
      logger('题库返回错误信息，尝试随机选择', 'orange');
      // 随机选择一个选项
      _i = Math.floor(Math.random() * _answerTmpArr.length);
      logger(`随机选择选项: ${_i + 1}`, 'orange');
    } else if (_true.indexOf(agrs) != -1) {
      _i = _a.findIndex((item) => _true.indexOf(item) != -1)
      logger(`匹配到"正确"选项`, 'green');
    } else if (_false.indexOf(agrs) != -1) {
      _i = _a.findIndex((item) => _false.indexOf(item) != -1)
      logger(`匹配到"错误"选项`, 'green');
    } else {
      logger('答案匹配出错，尝试随机选择', 'red')
      // 随机选择一个选项
      _i = Math.floor(Math.random() * _answerTmpArr.length);
      logger(`随机选择选项: ${_i + 1}`, 'orange');
    }

    // 如果仍然没有找到匹配项，随机选择一个选项
    if (_i === -1) {
      logger('所有匹配方法均失败，随机选择一个选项', 'orange');
      _i = Math.floor(Math.random() * _answerTmpArr.length);
      logger(`随机选择选项: ${_i + 1}`, 'orange');
    }

    if (_i !== -1) {
      setTimeout(() => {
        try {
          // 尝试多种点击方式
          const $option = $(_answerTmpArr[_i]);
          const $parent = $option.parent();
          const $grandparent = $parent.parent();

          logger(`点击判断题选项: ${_i + 1}`, 'green');

          // 获取题目ID
          const questionId = $(TiMuList[index]).attr('id') || $(TiMuList[index]).attr('data');
          if (questionId) {
            logger(`题目ID: ${questionId}`, 'green');

            // 尝试方法1: 直接调用addChoice函数
            if (typeof addChoice === 'function') {
              logger('尝试直接调用addChoice函数', 'green');
              try {
                // 直接调用页面的addChoice函数
                addChoice($option[0]);
                logger('addChoice函数调用成功', 'green');
              } catch (e) {
                logger(`addChoice函数调用失败: ${e.message}，尝试其他方法`, 'red');
              }
            }

            // 尝试方法2: 设置答案值和ARIA属性
            const answerInput = $(`#answer${questionId}`);
            if (answerInput.length > 0) {
              // 获取选项值 - 判断题需要获取true/false值
              let optionLabel = $option.find('.num_option').attr('data') || '';

              // 设置答案值
              answerInput.val(optionLabel);
              logger(`设置答案输入框值: ${optionLabel}`, 'green');

              // 触发change事件，确保值被正确应用
              answerInput.trigger('change');

              // 添加选中样式到num_option元素
              $option.find('.num_option').addClass('check_answer');
            }

            // 设置ARIA属性
            $option.attr('aria-checked', 'true');
            $option.attr('aria-pressed', 'true');
          }

          // 尝试方法3: 直接点击选项
          $option.click();

          // 尝试方法4: 点击父元素
          setTimeout(() => {
            $parent.click();
          }, 50);

          // 尝试方法5: 点击祖父元素
          setTimeout(() => {
            $grandparent.click();
          }, 100);

          // 尝试方法6: 模拟点击事件序列
          setTimeout(() => {
            // 模拟鼠标点击
            ['mousedown', 'mouseup', 'click'].forEach(eventType => {
              const event = new MouseEvent(eventType, {
                bubbles: true,
                cancelable: true,
                view: window
              });
              _answerTmpArr[_i].dispatchEvent(event);
            });
          }, 150);

          // 尝试方法7: jQuery触发多种事件
          setTimeout(() => {
            $option.trigger('mousedown').trigger('mouseup').trigger('click');
          }, 200);

          // 尝试方法8: 模拟用户点击行为
          setTimeout(() => {
            try {
              // 获取选项的位置
              const rect = $option[0].getBoundingClientRect();
              const centerX = rect.left + rect.width / 2;
              const centerY = rect.top + rect.height / 2;

              // 创建并分发鼠标事件
              const mouseEvents = [
                new MouseEvent('mouseover', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY }),
                new MouseEvent('mousedown', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY }),
                new MouseEvent('mouseup', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY }),
                new MouseEvent('click', { bubbles: true, cancelable: true, view: window, clientX: centerX, clientY: centerY })
              ];

              mouseEvents.forEach(event => $option[0].dispatchEvent(event));
              logger('模拟用户点击行为完成', 'green');
            } catch (e) {
              logger(`模拟用户点击行为失败: ${e.message}`, 'red');
            }
          }, 250);

          logger('自动答题成功，准备切换下一题', 'green')
          setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time)
        } catch (e) {
          logger(`点击选项失败: ${e.message}，尝试备用方法`, 'red')
          try {
            // 尝试通过jQuery触发点击
            $(_answerTmpArr[_i]).trigger('click');
            logger('使用jQuery触发点击成功', 'green')
          } catch (err) {
            logger(`所有点击方法都失败: ${err.message}，跳过此题`, 'red')
          }
          setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time)
        }
      }, 300)
    } else {
      logger('无法找到匹配的判断选项，跳过此题', 'red')
      setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time)
    }
  }).catch((error) => {
    logger(`获取答案失败: ${error}，尝试随机选择并继续处理下一题`, 'red');

    // 随机选择一个选项
    const randomIndex = Math.floor(Math.random() * _answerTmpArr.length);
    logger(`随机选择选项: ${randomIndex + 1}`, 'orange');

    // 尝试点击随机选项
    try {
      const $option = $(_answerTmpArr[randomIndex]);

      // 尝试使用addChoice函数
      if (typeof addChoice === 'function') {
        logger('尝试直接调用addChoice函数', 'green');
        try {
          addChoice($option[0]);
          logger('addChoice函数调用成功', 'green');
        } catch (e) {
          logger(`addChoice函数调用失败: ${e.message}，尝试其他方法`, 'red');
          $option.click();
        }
      } else {
        $option.click();
      }
    } catch (e) {
      logger(`点击随机选项失败: ${e.message}`, 'red');
    }

    // 无论点击是否成功，都继续处理下一题
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
  })
}

/**
 * 处理简答题、写作题、翻译题等需要文本输入的题目
 */
function handleEssay(index, TiMuList, _question, _type) {
  logger(`开始处理第 ${index + 1} 题简答题`, 'blue');

  // 获取文本框 - 增强查找能力
  let _textareaList = [];
  let editorFound = false;

  // 尝试多种可能的文本框选择器
  const possibleSelectors = [
    // 标准选择器
    '.stem_answer .eidtDiv textarea',
    '.stem_answer textarea',
    // 作业页面常见选择器
    '.mark_answer textarea',
    '.mark_answer .edui-editor',
    '.mark_answer .edui',
    '.mark_answer [class*="editor"]',
    '.mark_answer [id*="editor"]',
    '.mark_answer .answer_p textarea',
    '.mark_answer .textDIV textarea',
    '.mark_answer .textDIV',
    '.mark_answer [contenteditable="true"]',
    // 通用选择器
    'textarea',
    '.edui-editor',
    '[contenteditable="true"]',
    '[class*="editor"]',
    '[id*="editor"]'
  ];

  // 记录尝试查找的过程
  logger(`尝试查找简答题文本框...`, 'blue');

  // 首先尝试查找UEditor实例
  try {
    // 检查页面上是否有UEditor实例
    if (typeof UE !== 'undefined' && UE.instants) {
      const editorIds = Object.keys(UE.instants);
      if (editorIds.length > 0) {
        logger(`找到 ${editorIds.length} 个UEditor实例`, 'green');

        // 查找当前题目相关的编辑器
        const currentEditorIds = [];
        for (const editorId of editorIds) {
          const editorElement = document.getElementById(editorId);
          if (editorElement) {
            // 检查编辑器是否在当前题目内
            const $editor = $(editorElement);
            const $question = $(TiMuList[index]);

            if ($question.find($editor).length > 0 || $question.has($editor).length > 0 ||
              $editor.parents().filter($question).length > 0) {
              currentEditorIds.push(editorId);
              logger(`找到当前题目的编辑器: ${editorId}`, 'green');

              // 将编辑器对应的textarea添加到列表
              const textarea = document.getElementById(editorId);
              if (textarea) {
                _textareaList.push(textarea);
                editorFound = true;
              }
            }
          }
        }

        // 如果没有找到特定于当前题目的编辑器，使用第一个可用的
        if (currentEditorIds.length === 0 && editorIds.length > 0) {
          logger(`未找到当前题目专属编辑器，使用第一个可用的: ${editorIds[0]}`, 'orange');
          const textarea = document.getElementById(editorIds[0]);
          if (textarea) {
            _textareaList.push(textarea);
            editorFound = true;
          }
        }
      }
    }
  } catch (e) {
    logger(`查找UEditor实例时出错: ${e.message}`, 'red');
  }

  // 如果没有通过UEditor实例找到文本框，尝试DOM选择器
  if (!editorFound) {
    // 在当前题目中尝试各种选择器
    for (const selector of possibleSelectors) {
      const elements = $(TiMuList[index]).find(selector);
      if (elements.length > 0) {
        logger(`使用选择器 "${selector}" 找到 ${elements.length} 个文本框`, 'green');
        _textareaList = elements.toArray();
        break;
      }
    }

    // 如果仍然没找到，尝试在整个文档中查找
    if (_textareaList.length === 0) {
      logger('在题目内未找到文本框，尝试在整个文档中查找', 'orange');

      // 尝试查找可能与当前题目相关的文本框
      const questionId = $(TiMuList[index]).attr('id') || '';
      const questionIndex = index + 1;

      for (const selector of possibleSelectors) {
        // 先尝试查找与题目ID或索引相关的元素
        let elements = $(`[id*="${questionId}"] ${selector}, [id*="question${questionIndex}"] ${selector}, [id*="q${questionIndex}"] ${selector}`);

        // 如果没找到，尝试一般选择器
        if (elements.length === 0) {
          elements = $(selector);
        }

        if (elements.length > 0) {
          logger(`在文档中使用选择器 "${selector}" 找到 ${elements.length} 个文本框`, 'green');
          _textareaList = elements.toArray();
          break;
        }
      }
    }

    // 尝试在iframe中查找
    if (_textareaList.length === 0) {
      try {
        const iframes = document.querySelectorAll('iframe');
        logger(`尝试在 ${iframes.length} 个iframe中查找填空框`, 'blue');

        for (const iframe of iframes) {
          try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

            // 在iframe中尝试所有可能的选择器
            for (const selector of possibleSelectors) {
              const elements = $(iframeDoc).find(selector);
              if (elements.length > 0) {
                logger(`在iframe中使用选择器 "${selector}" 找到 ${elements.length} 个填空框`, 'green');
                _textareaList = elements.toArray();
                break;
              }
            }

            if (_textareaList.length > 0) break;
          } catch (e) {
            // 跨域iframe无法访问
            continue;
          }
        }
      } catch (e) {
        logger(`在iframe中查找填空框时出错: ${e.message}`, 'red');
      }
    }
  }

  // 如果仍然没找到填空框
  if (_textareaList.length === 0) {
    logger(`第 ${index + 1} 题未找到填空框，跳过此题`, 'red');
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
    return;
  }

  logger(`找到 ${_textareaList.length} 个填空框`, 'green');

  // 不再判断题目是否已作答，而是清空现有内容并重新填写
  // 清空所有文本框内容
  for (const textarea of _textareaList) {
    const $textarea = $(textarea);
    const _id = $textarea.attr('id');

    // 检查UEditor内容并清空
    if (_id && typeof UE !== 'undefined' && UE.getEditor && UE.getEditor(_id)) {
      const editor = UE.getEditor(_id);
      const content = editor.getContent();
      if (content && content !== '') {
        logger(`清空第 ${index + 1} 题已有内容，准备重新填写`, 'orange');
        editor.setContent(''); // 清空内容
      }
    } else if ($textarea.is('input') || $textarea.is('textarea')) {
      // 清空普通input/textarea内容
      if ($textarea.val() && $textarea.val().trim() !== '') {
        $textarea.val('');
        $textarea.trigger('input').trigger('change');
      }
    }
  }

  // 构建题目内容
  _question = "填空题,用\"|\"分割多个答案:" + _question;

  // 获取答案
  getAnswer(_type, _question).then((agrs) => {
    logger(`获取到答案: ${agrs}`, 'green');

    // 解析答案，处理多个空的情况
    let _answerTmpArr = agrs.split('|');
    logger(`解析出 ${_answerTmpArr.length} 个答案`, 'blue');

    // 填写答案到每个文本框
    let filledCount = 0;

    for (let i = 0; i < _textareaList.length; i++) {
      const textarea = _textareaList[i];
      const $textarea = $(textarea);
      const _currentId = $textarea.attr('id');

      // 获取当前空对应的答案，如果没有对应索引的答案，使用第一个答案
      const answerText = _answerTmpArr[i] || _answerTmpArr[0];

      try {
        // 尝试使用UEditor填写
        if (_currentId && typeof UE !== 'undefined' && UE.getEditor) {
          try {
            let editor = UE.getEditor(_currentId);
            if (editor) {
              setTimeout(() => {
                try {
                  editor.setContent(answerText);
                  logger(`使用UEditor成功填写第 ${i + 1} 个空: ${answerText}`, 'green');
                  filledCount++;
                } catch (e) {
                  logger(`使用UEditor填写第 ${i + 1} 个空失败: ${e.message}`, 'red');
                }
              }, 300);
              continue; // 如果使用UEditor成功，跳过后续方法
            }
          } catch (e) {
            logger(`使用UEditor填写第 ${i + 1} 个空时出错: ${e.message}`, 'red');
          }
        }

        // 尝试直接设置input/textarea值
        if ($textarea.is('input') || $textarea.is('textarea')) {
          $textarea.val(answerText);

          // 触发change事件，确保值被正确处理
          $textarea.trigger('input').trigger('change');

          logger(`直接设置第 ${i + 1} 个空的值成功: ${answerText}`, 'green');
          filledCount++;
        } else {
          // 如果不是标准输入元素，尝试其他方法
          $textarea.text(answerText);
          $textarea.attr('value', answerText);
          logger(`尝试其他方法设置第 ${i + 1} 个空的值: ${answerText}`, 'orange');
          filledCount++;
        }
      } catch (e) {
        logger(`填写第 ${i + 1} 个空时出错: ${e.message}`, 'red');
      }
    }

    if (filledCount > 0) {
      logger(`成功填写了 ${filledCount}/${_textareaList.length} 个空，准备切换下一题`, 'green');
    } else {
      logger('填写答案可能不成功，但仍将继续下一题', 'orange');
    }

    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
  }).catch((error) => {
    logger(`获取答案失败: ${error}，但仍将继续下一题`, 'orange');
    setTimeout(() => { doWorkViewQuestion(index + 1, TiMuList) }, setting.time);
  });
}