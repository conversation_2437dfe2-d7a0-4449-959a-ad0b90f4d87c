<!doctype html>
<html>
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no" />
	<meta http-equiv="pragma" content="no-cache"/>
	<meta http-equiv="cache-control" content="no-cache" />
	<meta http-equiv="expires" content="0"/>
	<meta charset="utf-8">
	<title>提示页面</title>
	<style type="text/css">
		*{margin:0;padding:0;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;-webkit-appearance:none;}
		body{font:14px/1.5 微软雅黑;background:#fff;color:#333;}
		ul{list-style-type:none;}
		input,select,textarea{vertical-align:middle; font:14px 微软雅黑; color:#333;}
		a:link,a:hover,a:active,a:visited{text-decoration:none;color:#333;}
		table{border-spacing:0px; border-collapse:collapse;width:100%; border:0px;margin:0;padding:0;}
		img{border:0px;}
		em{font-style:normal;}
		.clearfix{overflow:hidden;zoom:1;}

		.warn{display:inline-block;width:18px;height:18px;background:url(data:img/jpg;base64,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) no-repeat;background-size:100%;vertical-align:middle;margin-right:5px}
		.yzmTips{text-align:center;font-size:15px;width:95%;margin:0 auto;margin-top:40px;color:#666}
		.yzmInp{width:64%;height:44px;border:solid 1px #e7e7e7;border-radius:3px;outline:none;padding:0 10px;float:left}
		.marTop{margin-top:40px}
		.yzmImg{float:right;width:104px;height:44px;border:solid 1px #e7e7e7;border-radius:3px;overflow:hidden;cursor:pointer}
		.submit{width:95%;height:44px;background:#0099ff;color:#fff;line-height:44px;text-align:center;display:block;margin:30px auto;border-radius:3px;font-size:14px;border:none;cursor:pointer}

		@media screen and (min-width: 700px) {
			.yzmTips{margin-top:15%}
			.yzmTips,.submit{width:320px}
			.submit{font-size:16px;height:36px;line-height:36px}

		}
	</style>
</head>

<body>

<form action="/html/processVerify.ac" onsubmit="return /[0-9a-zA-Z]{4}/g.test(document.getElementById('ucode').value)">
	<div class="yzmTips">
		<p>【9010】操作异常，请输入图片中的验证码</p>
		<p class="marTop clearfix">
			<input type="hidden" name="app" value="0"/>
			<input type="text" class="yzmInp" placeholder="输入验证码" id="ucode" name="ucode" maxlength="4" autofocus>
			<span class="yzmImg"><img id="ccc" onclick="this.src='/processVerifyPng.ac?t='+Math.floor(2147483647 * Math.random())" width="104" height="44"></span>
		</p>
	</div>
	<input type="submit" class="submit" value="提交">
</form>
<script>
	setTimeout(function(){
		document.getElementById("ccc").click();
	},1000);
</script>
</body>
</html>
