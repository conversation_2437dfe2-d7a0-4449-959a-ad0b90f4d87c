# 超星学习通作业管理系统说明文档

## 系统概述

超星学习通作业管理系统是一套用于自动化获取、管理和完成超星学习通平台作业的Python工具集。该系统主要由以下几个核心组件构成：

1. **chaoxing_api_server.py** - 提供API服务，接收用户账号密码并返回作业列表
2. **chaoxing_homework_manager.py** - 作业管理核心类，实现登录、获取课程、获取作业等功能
3. **batch_fill_homework.py** - 批量填充作业答案的工具，支持AI自动回答
4. **get_all_homeworks.py** - 获取所有课程作业列表的命令行工具
5. **get_homework_params.py** - 获取作业参数的命令行工具

## 核心功能详解

### 1. 登录加密机制

超星学习通采用AES-CBC模式加密实现登录机制：

- 使用固定密钥 `u2oh6Vu^HWe4_AES` 对用户名和密码进行AES加密
- 加密后的内容使用Base64编码转换为字符串
- 发送到登录接口 `https://passport2.chaoxing.com/fanyalogin`

加密实现代码：

```python
class AESCipher:
    def __init__(self):
        self.key = AES_KEY.encode("utf8")
        self.iv = AES_KEY.encode("utf8")

    def encrypt(self, plaintext: str):
        ciphertext = b""
        cbc = pyaes.AESModeOfOperationCBC(self.key, self.iv)
        plaintext = plaintext.encode("utf-8")
        blocks = split_to_data_blocks(pkcs7_padding(plaintext))
        for b in blocks:
            ciphertext = ciphertext + cbc.encrypt(b)
        base64_text = base64.b64encode(ciphertext).decode("utf8")
        return base64_text
```

### 2. 课程获取流程

1. 登录成功后访问 `https://mooc2-ans.chaoxing.com/visit/courselistdata`
2. 解析HTML内容，提取课程信息：
   - 课程ID (courseId)
   - 班级ID (clazzId)
   - 个人ID (curPersonId)
   - 课程名称和教师名称
3. 可选择过滤已结束的课程

### 3. 作业列表获取流程

获取作业列表是系统的核心功能，涉及多个关键步骤：

1. 首先获取课程的enc参数和stuenc参数：
   - 访问课程详情页 `https://mooc1.chaoxing.com/visit/stucoursemiddle`
   - 从页面中提取enc和stuenc参数，或通过模拟点击作业按钮获取
   - 这些参数对于访问作业列表至关重要

2. 使用获取到的参数访问作业列表页面：
   - URL格式: `https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}&stuenc={stuenc}&enc={enc}`

3. 解析作业列表页面，提取每个作业的信息：
   - 作业ID (workId)
   - 答案ID (answerId)
   - 作业标题和状态
   - 作业链接及enc参数

4. 支持多线程并发获取多个课程的作业列表，提高效率

### 4. 作业参数格式

系统使用统一的作业参数格式，便于后续处理：
```
课程ID|班级ID|个人ID|作业ID|答案ID|enc参数
```

例如：
```
123456|654321|987654|112233|445566|abcdef123456
```

### 5. 批量填充作业答案

系统支持批量填充作业答案，主要流程：

1. 获取作业详情页面，提取题目信息和表单数据
2. 调用AI接口获取题目答案
3. 构建提交表单，包含所有必要参数
4. 提交到 `https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb` 接口

### 6. API服务接口

chaoxing_api_server.py提供了两个主要API接口：

1. **/get_homeworks** - 获取单个账号的作业列表
   - 请求参数：username, password, include_finished, include_ended_courses
   - 响应格式：JSON，包含作业列表、状态和统计信息

2. **/batch_get_homeworks** - 批量获取多个账号的作业列表
   - 请求参数：accounts数组(包含多个账号密码)、全局参数
   - 响应格式：JSON，包含每个账号的作业列表和统计信息

### 7. 性能优化

系统实现了多项性能优化措施：

1. **多线程并发**：
   - 课程线程池：并发处理多个课程
   - 页面线程池：并发获取多页作业列表
   - 批量账号线程池：并发处理多个账号

2. **连接池优化**：
   - 全局连接池配置，提高连接复用效率
   - 为每个会话添加连接池适配器

3. **缓存机制**：
   - 全局参数缓存，避免重复获取相同的参数
   - 在获取课程参数时自动更新缓存

4. **反爬策略**：
   - 随机User-Agent
   - 请求间隔随机化
   - 指数退避重试策略

## 命令行工具

### get_all_homeworks.py

获取所有课程作业列表并保存到文件的命令行工具。

**用法**：
```bash
python get_all_homeworks.py -u 用户名 -p 密码 [选项]
```

**主要选项**：
- `-u/--username`: 超星账号
- `-p/--password`: 超星密码
- `-o/--output`: 输出文件名
- `-t/--threads`: 并发线程数
- `--include-finished`: 包含已完成作业
- `--include-ended-courses`: 包含已结束课程

### get_homework_params.py

获取作业参数的命令行工具，输出格式为"课程ID|班级ID|个人ID|作业ID|答案ID|enc参数"。

**用法**：
```bash
python get_homework_params.py -u 用户名 -p 密码 [选项]
```

**主要选项**：
- `-u/--username`: 超星账号
- `-p/--password`: 超星密码
- `-t/--threads`: 并发线程数
- `--with-title`: 显示作业标题
- `--with-status`: 显示作业状态

## 系统入口

1. **API服务入口**：
   ```bash
   python chaoxing_api_server.py [--host 地址] [--port 端口] [--threads 线程数]
   ```

2. **批量填充作业入口**：
   ```bash
   python batch_fill_homework.py
   ```

3. **获取作业列表入口**：
   ```bash
   python get_all_homeworks.py -u 用户名 -p 密码
   ```

4. **获取作业参数入口**：
   ```bash
   python get_homework_params.py -u 用户名 -p 密码
   ```

## 技术细节

### 关键类和函数

1. **ChaoxingHomeworkManager类**：
   - `login()` - 登录超星学习通
   - `get_course_list()` - 获取课程列表
   - `get_course_enc()` - 获取课程enc参数
   - `get_homework_list()` - 获取作业列表

2. **批量处理函数**：
   - `get_all_homeworks()` - 获取所有课程的作业列表
   - `process_course_homework()` - 处理单个课程的作业

3. **API接口函数**：
   - `api_get_homeworks()` - 处理获取作业列表的API请求
   - `api_batch_get_homeworks()` - 处理批量获取作业列表的API请求

### 依赖库

- Flask: Web框架，提供API服务
- requests: HTTP请求库
- BeautifulSoup4: HTML解析库
- pyaes: AES加密库
- concurrent.futures: 并发处理库

## 安全性考虑

1. 账号密码使用AES-CBC模式加密，确保安全传输
2. 服务默认仅在本地运行，不对外网开放
3. 不保存任何用户凭据信息
4. 请求头随机化，降低被反爬风险

## 注意事项

1. 本系统仅供学习和研究使用，请勿用于非法用途
2. 请勿频繁请求API，避免触发超星学习通的反爬机制
3. 批量处理时建议控制并发数，避免账号被封禁 