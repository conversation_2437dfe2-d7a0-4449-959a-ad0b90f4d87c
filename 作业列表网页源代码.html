<!DOCTYPE html>
<html lang="en">

<script>
	_HOST_ = "//mooc1.chaoxing.com";
	_CP_ = "/mooc-ans";
	_HOST_CP1_ = "//mooc1.chaoxing.com/mooc-ans";
	// _HOST_CP2_ = _HOST_ + _CP_;
	_HOST_CP2_ = _CP_;
</script>

<head>
	<meta charset="UTF-8">
	<title>作业列表</title>
	<link href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/work-list.css?v=2024-0913-1500" type="text/css"
		rel="stylesheet" />
	<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/taskStudent_pop.css" />
	<script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/common/jquery.min.js"></script>
	<script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/common/jquery-migrate.min.js"></script>
	<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/js/jquery.nicescroll.min.js"></script>
	<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/poplayout.js"></script>
	<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/xlPaging.js"></script>
	<script src="//mooc1.chaoxing.com/mooc-ans/js/ServerHost.js"></script>
	<style>
		.check_repeat {
			display: inline-block;
			padding: 0 12px;
			background: #F2F4F7;
			border-radius: 35px;
			line-height: 30px;
			color: #474C59;
			font-size: 14px;
			cursor: pointer;
		}

		.check_repeat i {
			display: inline-block;
			width: 94px;
			height: 18px;
			float: left;
			margin: 6px 10px 0 0;
		}

		.check_repeat i img {
			width: 100%;
			height: 100%;
			display: block;
		}

		/*无障碍元素隐藏*/
		.element-invisible-hidden {
			position: absolute !important;
			clip: rect(1px 1px 1px 1px);
			/* IE6, IE7 */
			clip: rect(1px, 1px, 1px, 1px);
		}
	</style>
</head>

<body style="background: transparent;">
	<div id="workFocus" tabindex="0" aria-label=" 作业已刷新，请按tab键" role="option"></div>
	<div class="box">
		<div class="content">
			<div class="main task-list">
				<div class="top-back" tabindex="0">
					<h2>2023高职食品检验检测技术4班</h2>
				</div>

				<input type="hidden" id="courseId" value="249886567" />
				<input type="hidden" id="classId" value="116635292" />
				<input type="hidden" id="cpi" value="348369757" />
				<input type="hidden" id="enc" value="8640c003ffa257584cf610623491c1e5" />
				<input type="hidden" id="status" value="0" />
				<input type="hidden" id="topicId" value="0" />

				<div class="has-content" style="display: block;" tabindex="-1">
					<div class="filter">
						<div class="check_repeat fr" onclick="gotoDaya();"><i><img
									src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/daya.png"></i>提交的作业将经过大雅相似度分析系统，请勿抄袭
						</div>
						<span class="title">筛选</span>
						<span class="ipt-radio circl-choosed">
							<input name="group-radio" type="radio" onclick="changeStatus(this);" checked data="0"
								tabindex="-1" /><i class="icon-radio"></i>
						</span>
						<div class="operate-list-group fs12 color181E33 lineheight20 inlineBlock">全部</div>

						<span class="ipt-radio circl-choosed">
							<input name="group-radio" type="radio" onclick="changeStatus(this);" data="2"
								tabindex="-1" /><i class="icon-radio"></i>
						</span>
						<div class="operate-list-group fs12 color181E33 lineheight20 inlineBlock">已完成</div>

						<span class="ipt-radio circl-choosed">
							<input name="group-radio" type="radio" onclick="changeStatus(this);" data="1"
								tabindex="-1" /><i class="icon-radio"></i>
						</span>
						<div class="operate-list-group fs12 color181E33 lineheight20 inlineBlock">未完成</div>
					</div>

					<div class="bottomList" tabindex="0" aria-label="作业列表">
						<ul>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=42858464&answerId=53406255&enc=9787f8ee1ba9e8ae953a37c5b585ab2a"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="脂肪测定练习 ; 未交"
								role="link">
								<div class="tag icon-zy"></div>
								<div class="right-content">
									<p class="overHidden2 fl">脂肪测定练习</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>

									<a href="javascript:;"
										onclick="answerList(42858464, 53406255, '9787f8ee1ba9e8ae953a37c5b585ab2a');"
										class="listSubmit fl insightBtn"><i><img
												src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/answer-list.png"></i>作答记录</a>

								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=44493050&answerId=0&enc=7239528e5a5ac0435769444233b1a8d5"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="多选题复习 ; 未交"
								role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">多选题复习</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>


								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=44493042&answerId=0&enc=a44b8f2f1a26fceefb8b378e935245de"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="判断题复习 ; 未交"
								role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">判断题复习</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>


								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=44493023&answerId=0&enc=3b802ff3351b3350e0c8f900cc8c26d3"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="单选题复习 ; 未交"
								role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">单选题复习</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>


								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=44493008&answerId=0&enc=ba2ff396e1bb1ce237a5dac80c39e6d4"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="期末判断题复习 ; 未交" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">期末判断题复习</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>


								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=44493000&answerId=0&enc=3084a62b4e97c1a26da7c65ca73568a7"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="期末多选题复习 ; 未交" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">期末多选题复习</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>


								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=44492979&answerId=0&enc=bd40b4e981ad0b6c8460ff2ba606ac6e"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="期末单选题复习 ; 未交" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">期末单选题复习</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>


								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=43877841&answerId=53543301&enc=4d62e0e6278b2e08d4bb6a10ed630cc8"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="可可粉中甜味剂的测定 ; 待批阅" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">可可粉中甜味剂的测定</p>
									<div class="clear"></div>
									<p class="status fl">待批阅</p>


									<a href="https://stat2-ans.chaoxing.com/study-knowledge/ans?courseid=249886567&cpi=348369757&clazzid=116635292&ut=s&relationId=43877841&type=2"
										class="listSubmit fl insightBtn" target="_blank" tabindex="-1"><i><img
												src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/insight.png"></i>智能分析</a>
								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=43043186&answerId=53430476&enc=fc20872f9cf7fde1975bcacf62dfd63d"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="蛋白质测定预习 ; 已完成" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">蛋白质测定预习</p>
									<div class="clear"></div>
									<p class="status fl">已完成</p>


									<a href="https://stat2-ans.chaoxing.com/study-knowledge/ans?courseid=249886567&cpi=348369757&clazzid=116635292&ut=s&relationId=43043186&type=2"
										class="listSubmit fl insightBtn" target="_blank" tabindex="-1"><i><img
												src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/insight.png"></i>智能分析</a>
								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=42556483&answerId=53382368&enc=008d2db92a494978065245ead326708f"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="酸度测定练习题 ; 已完成" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">酸度测定练习题</p>
									<div class="clear"></div>
									<p class="status fl">已完成</p>


									<a href="https://stat2-ans.chaoxing.com/study-knowledge/ans?courseid=249886567&cpi=348369757&clazzid=116635292&ut=s&relationId=42556483&type=2"
										class="listSubmit fl insightBtn" target="_blank" tabindex="-1"><i><img
												src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/insight.png"></i>智能分析</a>
								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=42556421&answerId=53384723&enc=020053857882812e6a5086c998605cec"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="糖含量测定练习题 ; 已完成" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">糖含量测定练习题</p>
									<div class="clear"></div>
									<p class="status fl">已完成</p>


									<a href="https://stat2-ans.chaoxing.com/study-knowledge/ans?courseid=249886567&cpi=348369757&clazzid=116635292&ut=s&relationId=42556421&type=2"
										class="listSubmit fl insightBtn" target="_blank" tabindex="-1"><i><img
												src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/insight.png"></i>智能分析</a>
								</div>
								<div class="clearfix"></div>
							</li>





							<li onclick="goTask(this);"
								data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=249886567&classId=116635292&cpi=348369757&workId=42556391&answerId=0&enc=3d0034780abcf82cb1053c860110179f"
								onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0"
								aria-label="水分测定练习题 ; 未交" role="link">
								<div class="tag icon-zy-g"></div>
								<div class="right-content">
									<p class="overHidden2 fl">水分测定练习题</p>
									<div class="clear"></div>
									<p class="status fl">未交</p>


								</div>
								<div class="clearfix"></div>
							</li>
						</ul>
					</div>
				</div>
				<div style="width:100%;height:110px;"></div>
				<div class="pagePosition">
					<div class="pageDiv" id="page"></div>
				</div>
			</div>
		</div>
	</div>

	<div class="maskDiv popMoveShowHide" style="display:none;" id="recordList">
		<div class="popDiv wid640 popMove">
			<div class="popHead">
				<a href="javascript:;" onclick="closeAnswers();" class="popClose popMoveDele fr"
					style="margin-top: 5px;"><img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/popClose.png" /></a>
				<p class="fl" style="color: #181E33;font-size: 18px;">查看作答记录</p>
			</div>
			<div class="het62"></div>
			<div id="content"></div>
		</div>
	</div>
	<script>

		var courseId = $("#courseId").val();
		var classId = $("#classId").val();
		var cpi = $("#cpi").val();
		var enc = $("#enc").val();
		var status = $("#status").val();
		var topicId = $("#topicId").val();

		$("#page").paging({
			nowPage: 1,
			pageNum: 2,
			buttonNum: 9, // 要展示的页码数量
			callback: function (num) {
				location.href = "/mooc-ans/mooc2/work/list?courseId=" + courseId + "&classId=" + classId + "&cpi=" + cpi
					+ "&enc=" + enc + "&status=" + status + "&pageNum=" + num + "&topicId=" + topicId;
			}
		});

		function goTask(obj) {
			var url = $(obj).attr("data");
			window.open(url, '_blank');
		}

		function changeStatus(obj) {
			var data = $(obj).attr("data");
			location.href = "/mooc-ans/mooc2/work/list?courseId=" + courseId + "&classId=" + classId + "&cpi=" + cpi + "&enc=" + enc + "&status=" + data;
		}

		$(function () {
			$(".insightBtn").click(function (event) {
				event.stopPropagation();
			});

			$(".bottomList ul li").each(function () {
				var PLHeight = $(this).find(".right-content p.status").height();
				var PHeight = $(this).find(".right-content p:first-child").height();
				if (PLHeight == null) {
					var marginTop = (40 - PHeight) / 2
					$(this).find(".right-content p:first-child").css("margin-top", marginTop + "px");
				}
			});

			if ("0" == "1") {
				tabIntoAccessibleCustom();
			}
		});

		function tabIntoAccessibleCustom() {
			if ("0" == "1") {
				try {
					if (window.top && window.top.accessiblePlugs) {
						// window.top.accessiblePlugs.taggeriframe();
						window.top.accessiblePlugs.update();
					}
				} catch (e) {
					console.log(e)
				}
			}
		}

		function gotoDaya() {
			var url = ServerHost.dayaDomain;
			window.open(url, "_blank");
		}

		var recordLock = 0
		function answerList(workId, answerId, enc) {
			if (recordLock == 1) {
				return;
			}
			recordLock = 1;

			$.ajax({
				type: "get",
				url: _HOST_CP2_ + "/mooc2/work/answer-list",
				dataType: "html",
				data: {
					courseId: courseId,
					classId: classId,
					cpi: cpi,
					workId: workId,
					answerId: answerId,
					enc: enc
				},
				success: function (data) {
					$("#content").html(data);
					$(".popSetupShowHide").fullFadeIn();
					$("#recordList").show();
					MoveFixed();
				}
			});
		}

		function closeAnswers() {
			$("#recordList").hide();
			$('.popSetupShowHide').fullFadeOut();
			recordLock = 0;
		}

		function MoveFixed() {
			$('.popMove').css({
				top: function () {
					return ($(window).height() - $(this).height()) / 2;
				},
				left: function () {
					return ($(window).width() - $(this).width()) / 2;
				}
			});

			$("#boxscroll").niceScroll({
				cursorborder: "",
				cursorcolor: "#D9DDE1",
				boxzoom: false
			});
		}

		$(function () {
			if ("0" == "1") {
				setTimeout(function () {
					$('#workFocus').focus();
				}, 500)
			}
		})
	</script>
</body>

</html>