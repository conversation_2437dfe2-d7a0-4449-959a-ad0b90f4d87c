<html xmlns="http://www.w3.org/1999/xhtml"><head><script>
    _HOST_ = "//mooc1.chaoxing.com";
    _CP_ = "/exam-ans";
    _HOST_CP1_ = "//mooc1.chaoxing.com/exam-ans";
    // _HOST_CP2_ = _HOST_ + _CP_;
    _HOST_CP2_ = _CP_;
</script>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>考试</title>
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/common.css?v=2025-0424-1038">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/pop.css?v=2021-0311-1412">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/examStudent_pop.css?v=2025-0604-1759">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/style.css?v=2025-0424-1038">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/webmonitor.css?v=2025-0318-1909">
<script src="//mooc1.chaoxing.com/exam-ans/js/ServerHost.js"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/common/jquery.min.js"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/common/jquery-migrate.min.js"></script><script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poptoast.js?v=2021-0917-1623"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poplayout.js"></script>
<style>.clearfix:after{content: " ";display: block;height: 0px;clear: both;}</style><script type="text/javascript" async="" src="https://captcha.chaoxing.com/light.js?t=29229708"></script><style>.u-captcha{margin-left: 56px;border: 1px solid #e4e7eb;border-radius: 2px;background-color: #fff;}.cx_image_margin{ -webkit-user-select: none;}.u-opacity{ position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, .5); display: none;z-index:99999}.u-layer{position: fixed;top: 50%;left: 50%;margin-top: -150px;margin-left: -176px;display: none;z-index:100000}.u-layer .cx_modal_title { display: block;}.cx_image_margin {position:relative; width:320px; line-height: 40px;text-align: center;z-index: 99;margin:16px 0;}.u-layer .cx_image_margin{margin:16px auto}.cx_imgBg {width: 320px;height: 160px;z-index: 9;}.cx_imgBtn {display: none;-webkit-user-select: none;width: 44px;height: 171px;position: absolute;left: 0;}.cx_modal_title {box-sizing: content-box;-webkit-user-select: none; width: 320px; padding: 0 15px; height: 50px; line-height: 50px; text-align: left; font-size: 16px; color: #45494c; border-bottom: 1px solid #e4e7eb; display: none;}.cx_modal_close { position: absolute; top: 13px; right: 9px; width: 24px; height: 24px; text-align: center; cursor: pointer; }.cx_icon-close { display: inline-block; vertical-align: top; margin-top: 6px; width: 11px; height: 11px; vertical-align: top; background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7); background-position: 0 -53px; background-size: 52px 792px; }.cx_comImageValidate {position: relative;}.cx_refresh { z-index: 1000;position: absolute;width: 30px;height: 30px;right: 4px;top: 4px;cursor: pointer;background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7); background-position: 0 -299px; background-size: 52px 792px;}.cx_refresh:hover{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -266px;background-size: 52px 792px}.cx_success_refresh .cx_refresh{display:none}.cx_hkinnerWrap{position: relative;border: 1px solid #e4e7eb;background-color: #f7f9fa;height: 40px; margin-top:16px; border-radius: 2px; z-index: 9999;}.cx_slider_indicator{position: absolute;top: -1px;left: -1px;width: 0;border: 1px solid transparent;height: 40px; border-radius: 2px;display: none;}.cx_moving .cx_slider_indicator{border-color: #1991fa;background-color: #d1e9fe;display: block;}.cx_success .cx_slider_indicator{border-color: #52ccba;background-color: #d2f4ef;display: block;}.cx_error .cx_slider_indicator {border-color: #f57a7a;background-color: #fce1e1;display: block;}.cx_rightBtn{display: none;position: absolute;top: 0;left: 0;height: 100%;background-color: #fff;box-shadow: 0 0 3px rgba(0, 0, 0, .3);cursor: pointer;transition: background .2s linear;width: 40px; border-radius: 2px;}.cx_rightBtnAni{ -webkit-transition: left .5s ease;}.cx_rightBtn:hover{background-color: #1991fa}.cx_rightBtn .notSel{position: absolute;top: 50%;margin-top: -6px;left: 50%;margin-left: -6px;width: 14px;height: 12px;background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -26px;background-size: 52px 792px}.cx_rightBtn:hover .notSel{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 0;background-size: 52px 792px;}.cx_slider_tip{text-align: center;color: #45494c;}.cx_tip_icon{display: inline-block;zoom: 1;vertical-align: top}.cx_tip_text{vertical-align: middle;font-size: 14px;-webkit-user-select: none;}.cx_tip_answer {vertical-align: middle;font-weight: 700}.cx_tip_answer.hide{display: none}.cx_tip_point{display: inline}.cx_tip_img{display: none}.cx_tip_answer{width: 80px;height: 20px;margin-left: 8px;overflow: hidden;position: relative}.cx_tip_point{display: none}.cx_tip_img{display: block;position: absolute;bottom: -60px;left: 0;width: 400%;height: 1200%}.cx_tip_img{bottom: -40px}.cx_click-tip{ line-height: 39px;display: none;}.cx_icon-point{position: absolute;z-index: 10;width: 26px;height: 33px;cursor: pointer;background-repeat: no-repeat}.cx_icon-point.cx_point-1{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -511px;background-size: 52px 792px;}.cx_icon-point.cx_point-2{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -367px;background-size: 52px 792px}.cx_icon-point.cx_point-3{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -475px;background-size: 52px 792px}.cx_icon-point.cx_point-4{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -439px;background-size: 52px 792px}.cx_icon-point.cx_point-5{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -403px;background-size: 52px 792px}.cx_click .cx_slider_indicator, .cx_click .cx_rightBtn, .cx_click .cx_slider_tip{ display: none;}.cx_click .cx_click-tip{ display: block;}.cx_click-success  .cx_click-control{border-color: #52ccba;background-color: #d2f4ef;}.cx_click-success .cx_click-tip{ display: block; color: #52ccba;}.cx_click-success .cx_tip_icon{width: 17px;background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -97px;background-size: 52px 792px;}.cx_click_success{border-color: #52ccba;background-color: #d2f4ef;}.cx_click_success .cx_tip_icon{margin-right: 5px;width: 17px;height: 12px;vertical-align: middle;background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -97px;background-size: 52px 792px;}.cx_click_success .cx_tip_text{ color: #52ccba;}.cx_success .cx_rightBtn{background-color: #52ccba}.cx_success:hover .notSel,.cx_success .notSel{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -13px;background-size: 52px 792px;}.cx_error.cx_click{border-color: #f57a7a;background-color: #fce1e1}.cx_error .cx_rightBtn{background-color: #f57a7a}.cx_error:hover .notSel,.cx_error .notSel{background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -68px;background-size: 52px 792px}.cx_error .cx_tip_text{color: #f57a7a}.cx_error .cx_slider-tip .cx_tip_icon{}.cx_error .cx_click-tip .cx_tip_icon{margin-right: 5px;width: 12px;height: 12px;vertical-align: middle;background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -82px;background-size: 52px 792px;}@keyframes cx_ball-scale-multiple{0% {transform: scale(.22);opacity: 0}5% {opacity: 1}to {transform: scale(1);opacity: 0}}.vcode-mask{position: fixed;z-index: 60002;top: 0;left: 0;width: 100%;height: 100%;opacity: .7;background: #000;}.vcode-body{width: 305px;position: fixed;left: 50%;top: 50%;margin-left: -152.5px;margin-top: -173.5px; z-index: 60003;overflow-y: auto;-webkit-border-radius: 13px;-webkit-transform-style: preserve-3d;}.mod-vcodes{-webkit-transform-style: preserve-3d;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}.mod-vcode-content{padding: 40px 16px 18px; text-align: center; background: #fff;}.mod-font-gray{font-size:14px;color:#ccc;text-align:center}.mod-font-gray02{font-size:18px;color:#000;text-align:center}.mode-vcode-imgbox{width:170px;height:170px;margin:30px auto;position:relative}.mode-vcode-imgbox img{width:100%;height:100%;transform:translate(0,0);}img.mode-vcode-img{position:absolute;top:0;left:0}.mode-reference-img{position:absolute;top:0;left:0}.mode-btn{overflow:hidden;width:320px;position: relative;height: 42px;border: 1px solid #e0e0e0; clear: both;border-radius: 42px;margin:0 auto;background-color: #f7f7f7;}.mode-btn-tips{position:absolute;left:0;top:42px;width:100%;height:100%;line-height:42px;font-size:16px;display:none}.mode-btn-slider{position: absolute;left: 0;top: 0;width: 40px;height: 40px;border-radius: 40px;border: 1px solid #e0e0e0; background-color: #fff;}.mode-btn-focus .mode-btn-slider{background:#1a91ed;border-color:#1a91ed}.mode-btn-error .mode-btn-slider{background:#e01116;border-color:#e01116}.mode-btn-success .mode-btn-slider{background:#32cd32;border-color:#32cd32}.mode-btn-slider i{display: block;width: 20px;height: 20px;background-size: 100% 100%; margin: 10px;background-image:url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -823px;background-size: 20px 305px}.cx_rightBtn.mode-btn-slider:hover i{background-image:url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -847px;background-size: 20px 305px}.mode-btn-error .mode-btn-slider i{background-image:url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -403px;background-size: 52px 792px}.mode-btn-success .mode-btn-slider i{background-image:url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -403px;background-size: 52px 792px}.mode-img-status{position:absolute;width:150px;height:150px;background-color:rgba(0,0,0,.4);top:10px;left:10px;border-radius:100%;display:none;background-repeat: no-repeat;background-position: center center;background-size:30%}.status-success{display:block;background-image:url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -521px;background-size: 40px 610px;width:40px;height:40px;position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.status-error{display:block;background-image:url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -570px;background-size: 40px 610px;width:40px;height:40px;position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.mode-btn-error .mode-btn-tips{top:0;display:block}.cx_captcha_error .cx_rightBtn{display:none}.cx_captcha_error .cx_slider_tip{border-color: #f57a7a; background-color: #fce1e1;}.cx_captcha_error .cx_fallback_tip{color: #f57a7a;}.cx_max_error .cx_slider_tip{border-color: #f57a7a; background-color: #fce1e1;}.cx_max_error .cx_click-tip{border-color: #f57a7a; background-color: #fce1e1;}.cx_max_error .cx_fallback_tip{color: #f57a7a;}.cx_max_error .cx_fallback_tip:hover{text-decoration:underline;cursor:pointer;}.cx_max_error .cx_rightBtn{display:none}.cx_max_error .cx_tip_icon{margin-right: 5px;width: 12px;height: 12px;vertical-align: middle;background-image: url(https://captcha.chaoxing.com/captcha/icon_captcha.png?t=1.0.7);background-position: 0 -82px;background-size: 52px 792px;}.cx_max_error .cx_refresh:hover,.cx_loading .cx_refresh:hover{cursor:not-allowed}.cx_tips__answer{display:inline-block;vertical-align:middle;position:relative;top:-4px}
.cx_tips__answer span{width:30px;height:30px;display:inline-block;vertical-align:middle;margin:0 2px}
.cx_tips__answer span img{width:70%;height:70%}.cx_tips__answer.cx_icon_error{display:none !important}.cx_tips__answer_div{width: 84px;height:17px;margin-left:8px;overflow:hidden;position:relative;display:inline-block;vertical-align:-9px}.cx_tips__answer_img{display:block;position:absolute;top:-144px;left:0;width:340%}@font-face {    font-family: "FZDBSJW";    src: url("https://captcha.chaoxing.com/FZDBSJW.eot") format("embedded-opentype"),        url("https://captcha.chaoxing.com/FZDBSJW.woff2") format("woff2"),        url("https://captcha.chaoxing.com/FZDBSJW.woff") format("woff"),        url("https://captcha.chaoxing.com/FZDBSJW.TTF") format("truetype"),        url("https://captcha.chaoxing.com/FZDBSJW.svg") format("svg");    font-weight: normal;    font-style: normal;}</style></head>

<body style="zoom: 1; background: none 0% 0% / auto repeat scroll padding-box border-box rgb(242, 244, 247);">

      <input type="hidden" id="examId" name="examId" value="6991302" class="clearfix">
  <input type="hidden" id="answerId" name="answerId" value="*********">
   <input type="hidden" id="type" value="">
 <input type="hidden" id="qbanksystem" name="qbanksystem" value="0">
<input type="hidden" id="qbankbackurl" name="qbankbackurl" value="">
 <input type="hidden" id="webSnapshotMonitor" name="webSnapshotMonitor" value="0">
 <input type="hidden" id="faceimgs" name="faceimgs" value="">
<input type="hidden" id="needCode" value="0">
<input type="hidden" id="examCheck" value="1">
<input type="hidden" id="captchaCheck" value="1">
<input type="hidden" id="snapshotMonitor" name="snapshotMonitor" value="0">
<input type="hidden" id="checkSecondDeviceLive" value="">
<input type="hidden" id="secondDeviceLiveKey" value="">
<input type="hidden" id="facekey" value="">
<input type="hidden" id="workExamUploadUrl" name="workExamUploadUrl" value="">
<input type="hidden" id="workExamUploadCrcUrl" name="workExamUploadCrcUrl" value="">
<input type="hidden" id="examPreparationTimeMillSeconds" value="0">
<input type="hidden" id="isChaoxingExamPc" value="">
<input type="hidden" id="examPreparationTime" value="0">
<input type="hidden" id="limitTime" value="60">
<input type="hidden" id="expiredAutoSubmit" value="1">
<input type="hidden" id="selftestMode" name="selftestMode" value="0">
<input type="hidden" id="isAccessibleCustomFid" name="isAccessibleCustomFid" value="0">
<input type="hidden" id="faceRecognitionCompare" name="faceRecognitionCompare" value="">
<input type="hidden" id="reset" name="reset" value="false">
<input type="hidden" id="checkLiveDetectionStatus" value="false">
<input type="hidden" id="electronSnapshotMonitor" value="0">
<input type="hidden" id="captchaCaptchaId" value="Ew0z9skxsLzVKQjmeObQiRVLxkxbPkRF">
<input type="hidden" id="captchavalidate" value="validate_qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv_1CB28BB95F7CF6BADEDDE2817B684B08">
<input type="hidden" id="browserLocale" value="chinese">
<input type="hidden" id="pcExamClientSign" value="true">
<input type="hidden" id="scoreStandard" value="1">
<input type="hidden" id="signConfig" value="">
<input type="hidden" id="userId" value="305967988">
<input type="hidden" id="studentInfoUrl" value="">

<input type="hidden" id="isStartPage" value="1">
<form id="submitTest">
	<input type="hidden" id="courseId" name="courseId" value="221247981">
	<input type="hidden" id="testPaperId" name="testPaperId" value="6991302">
	<input type="hidden" id="testUserRelationId" name="testUserRelationId" value="*********">
	<input type="hidden" id="classId" name="classId" value="122555170">
	<input type="hidden" id="cpi" name="cpi" value="356795813">
    	</form>


     <div class="subNav" style="z-index:3" tabindex="0" role="option" aria-label="考试 页面">考试</div>
  
<div class="het40"></div>

<div class="main1200">
	
	
	<div class="pad30lr">
		<div class="fanyaInvig">
			          			  
										<div class="InstructionDiv">
						
				     						
					<div class="examinfo">
						<table width="100%" border="0" cellspacing="0" cellpadding="0" role="table" tabindex="0">
							
							<tbody><tr role="row" tabindex="0">
								<td align="left" class="color3">考试名称</td>
								<td align="left">2025年生态环境学院实验室准入资格考试（学生）</td>
							</tr>
							<!--
							<tr>
								<td align="left" class="color3">考试地点</td>
								<td align="left">教一楼3区204</td>
							</tr>
							-->
						 							<tr role="row" tabindex="0">
								<td align="left" class="color3">考试时长（分钟）</td>
								<td align="left">60 分钟</td>
							</tr>						
							<tr role="row" tabindex="0">
								<td align="left" class="color3">考试时间</td>
									<td align="left">06-03 12:00 至 07-31 12:00</td>
							</tr>
						
						</tbody></table>
					</div>
					<div class="exam_descrip fs16 color2" tabindex="0" role="option">
						            			      <p><strong>考试说明</strong>：<br>1、离开或退出考试界面答题计时不停止，请不要中途离开考试界面。<br>2、保持座位前的桌面干净，不要有与考试无关的内容。<br>3、考试时间截止或答题时间结束，如果处于答题页面，将自动提交试卷。<br>4、考试过程中如果出现页面卡死、题目空白情况，请尝试切换网络或退出重新进入考试。<br></p> 
            			
        				        									</div>
					<div class="face_agreement" tabindex="0" role="radio" onclick="checkLoadError();"><i class="agree_check agree_checked"></i><span>我已阅读并同意</span></div>
					
					
					<div class="AutographDiv">
						
						<!--
						<div class="fl">
							<a href="#" class="addAutograph"><i></i>添加签名</a>
							<div class="add_autog_ewm" style="display:none">
								<dl>
									<dt><img src="temp/ewm.png" /></dt>
									<dd>扫一扫添加签名</dd>
								</dl>
							</div>
						</div>
						<div class="Autog_name fl" style="display:none">
							<div class="namePic fl"><img src="temp/name.png" /></div>
							<div class="name_oprea fr">
								<a href="#" class="name_rewrite"><i><img src="images/eidtIcon.png" /></i>重写</a>
								<a href="#" class="name_del"><i><img src="images/red_del.png" /></i>删除</a>
							</div>
						</div>
						-->
						
						<div class="next_btn_div fr pos_bottom">
														<a href="javascript:void(0);" onclick="preEnterExam()" data="/exam-ans/exam/test/reVersionTestStartNew?courseId=221247981&amp;classId=122555170&amp;tId=6991302&amp;id=*********&amp;p=1&amp;tag=1&amp;enc=?&amp;cpi=356795813&amp;openc=687cf461734107a032056957d0b52d39&amp;newMooc=true" class="fs14 disble_time_btn entrybtn" id="startBtn" tabindex="-1">
																	进入考试															</a>
						</div>
						<div class="clear" tabindex="0" role="option" aria-label="考试 结尾" id="tabExamNoteEnd"></div>
						
					</div>
				</div>
		

				
				
			</div>
			
	</div>
</div>

<div id="captcha" style="display: block;"><div class="u-opacity" style="display: block;"></div><div class="u-captcha u-layer" id="eject" style="display: block;">  <div class="cx_comImageValidate">
			<div class="cx_modal_title"><span id="prompt">请完成安全验证</span> <span class="cx_modal_close"><span class="cx_icon-close"></span></span></div>
			<div class="cx_image_margin" id="cx_image_margin">
        <div class="cx_imgBg" id="cx_imgBg" style="background: url(&quot;https://captcha-b.chaoxing.com/slide/big/C387D1BFDC1FFC2058696C42CDFB3050.jpg&quot;) rgb(255, 255, 255);">
            <div class="cx_imgBtn" style="display: block; left: 0px; top: 0px;">
                <img alt="" src="https://captcha-b.chaoxing.com/slide/small/C387D1BFDC1FFC2058696C42CDFB3050.jpg" draggable="false">
            </div>
            <img class="cx_imgDrag" id="cx_imgDrag" alt="" src="" draggable="true">
			 <span class="cx_refresh"></span>
            <div class="rotate-img"></div>            <canvas class="cx_obstacle_canvas" id="cx_obstacle_canvas" width="320" height="160" style="display: inline-block;position: absolute;top: 0;left: 0;z-index: 10"></canvas>
        </div>
        <div class="cx_hkinnerWrap">
		 	 <div class="cx_slider_indicator" style="width: 264px;"></div>
			 <div class="cx_rightBtn cx_rightBtnAni" style="display: block; left: 0px;"><span class="notSel"></span></div>
			 <div class="cx_slider_tip">
			 	<span class="cx_tip_icon"></span>
			 	<span class="cx_tip_text cx_fallback_tip" style="color: rgb(69, 73, 76);">向右拖动滑块填充拼图</span>
			 	<div class="cx_tip_answer hide">
			 		<span class="cx_tip_point"></span>
			 		<img class="cx_tip_img" alt="">
			 	</div>
			 </div>
			 <div class="cx_click-tip">
				 <span class="cx_tip_icon"></span>
				 <span class="cx_tip_text cx_fallback_tip" style="color: rgb(69, 73, 76);">向右拖动滑块填充拼图</span>
				 <div class="cx_tips__answer"></div>			 </div>
        </div>
        </div>
    </div>
</div></div>
<div class="maskDiv" style="display:none;z-index:2001;" id="timeOverSubmitConfirmPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">作答时间耗尽，试卷已提交</p>
			<p class="popWord fs16 colorIn" style="margin: 2px 2px;">试卷领取时间：</p>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">考试用时：<span class="consumeMinutes"></span>分钟</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv " style="display:none;z-index:2001;" id="showLoginInfo">
    <div class="popDiv liveEwmPop">
        <div class="popHead">
            <a href="#" class="popClose fr"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#showLoginInfo').fullFadeOut();"></a>
            <p class="fl fs18 colorDeep">直播二维码</p>
        </div>
        <div class="het62"></div>
        <div class="livePop_con">
            <div class="fs16 color1 liveweminfo">
                <p>账号：<span class="color0" id="loginName"></span></p>
                <p>密码：<span class="color0" id="password"></span></p>
            </div>
            <dl class="pop_ewm">
                <dd>二维码仅考试期间有效</dd>
                <dt><img id="ewmUrl" src="" onclick="rereshSecondDeviceQRCode()"></dt>
            </dl>
        </div>

    </div>
</div>

<div class="maskDiv" style="display:none;z-index:2000;" id="submitConfirmPop" tabindex="0" role="alertdialog" aria-label="交卷">
	<div class="popDiv wid440 Marking" style="left:39%;top:30%;width:450px;min-height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep" tabindex="0" role="option">提示</p>
				<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
					<img tabindex="-1" aria-hidden="true" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');">
				</a>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 30px 2px;" tabindex="0" role="option">确认交卷？</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">确定</a>
				<a href="javascript:" class="btnBlue btn_92_cancel fr fs14" onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');">取消</a>
			</div>
			<div class="het72" tabindex="0" role="option" aria-label="弹窗结尾"></div>
				</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="audioLimitTimesWin">
	<div class="popSetDiv wid440">
			<div class="popHead RadisTop">
				<a href="javascript:;" class="popClose fr" onclick="$('#audioLimitTimesWin').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 color1">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 color2 audioLimitTimesTip">此附件仅支持打开 <span></span> 次，你已打开 <span></span> 次，不能再次打开</p>
			<div class="popBottom RadisBom">
				<a href="javascript:;" class="jb_btn jb_btn_92 fr fs14" onclick="$('#audioLimitTimesWin').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv popMoveShowHide" id="confirmEnterWin" style="z-index: 1000; display: none;" tabindex="0" role="alertdialog" aria-label="进入考试">
	<div class="popDiv wid440 popMove" style="top: 272px; left: 731px;">
		<div class="popHead">
			<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
				<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#confirmEnterWin').fullFadeOut();">
			</a>
			<p class="fl fs18 colorDeep" style="font-size:18px;" tabindex="-1">提示</p>
		</div>
		<div class="het62"></div>
		<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option">
			<div class=" tip" style="line-height:26px;font-size:16px;min-height: 140px;width:100%;">本次考试答题时长为60分钟，进入考试后开始计时，中途退出或离开考试界面会继续计时，考试时间截止后系统将会自动提交试卷，确认进入考试？</div>
		</div>
		<div class="popBottom">
			<a tabindex="0" role="button" id="tabIntoexam2" href="javascript:;" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="enterExamCallBack()">进入考试</a>
			<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14" onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;">取消</a>
		</div>
		<div class="het72" tabindex="0" role="option" id="confirmEnterWinEnd" aria-label="弹窗结尾"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="multiTerminalWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:300px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:18px 2px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">继续考试</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;">退出考试</a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;" id="examTipsPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#examTipsPop').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="singleQuesLimitTimePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;"> 本题作答时间已用完，将进入下一题 </div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="singleQuesLimitTimeConfirm();"> 确定 </a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
			 当前考试需按分卷顺序作答，确认进入下一个分卷？ </div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
			<a href="javascript:" class="btnBlue btn_92_cancel fr fs14" onclick="$('#groupPaperLimitTimePop').fullFadeOut();$('#groupStart').val(0)">取消</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimeOverPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
							当前分卷作答时间已用完，将进入下一分卷					</div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimeSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">当前分卷需限时耗尽才允许提交</div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="switchScreenPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#switchScreenPop').fullFadeOut();">确定</a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv popMoveShowHide" id="confirmRetestWin" style="display:none;z-index:1000;" tabindex="0" role="alertdialog" aria-label="重考提示">
	<div class="popDiv wid440 popMove" style="top: 272px; left: 731px;">
		<div class="popHead">
			<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
				<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#confirmRetestWin').fullFadeOut();"></a>
			<p class="fl fs18 colorDeep" style="font-size:18px;">提示</p>
		</div>
		<div class="het62"></div>
		<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option">
			<div class=" tip" style="line-height:26px;font-size:16px;min-height: 80px;width:100%;">开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最高成绩为最终成绩，请确认是否重考？</div>
		</div>
		<div class="popBottom">
			<a tabindex="0" role="button" href="javascript:;" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">确定重考</a>
			<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14" onclick="$('#confirmRetestWin').fullFadeOut();" style="width:88px;">取消</a>
		</div>
					<div class="het72"></div>
			</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="exitTimesSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;" data="系统检测到你已离开考试{1}次，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitTimesSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="exitDurationSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;" data="系统检测到你的切屏时长已超过{1}秒，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitDurationSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:2001;" id="teacherNoticePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">教师提醒</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 10px 2px; height:200px;overflow:auto;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#teacherNoticePop').fullFadeOut()">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv taskStatusShowHide" style="display:none;z-index:1000;" id="stuSelfTestAutoPaper">
	<div class="popDiv wid440 centered">
		<div class="popHead">
			<a href="javascript:" class="popClose popMoveDele fr"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="barDiv">
			<div class="barCon" style="width:10%"></div>
		</div>
		<p class="barInfo" style="margin-bottom:20px">自测试卷生成中，已完成<span id="taskrate">0%</span></p>
		<div class="popBottom">
								</div>
		<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;" id="faceRecognitionComparePop">
	<div class="popDiv iden_resultPop">
        		<div class="popHead">
        			<a href="javascript:" class="popClose fr" onclick="$('#faceRecognitionComparePop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
        			<p class="fl fs18 colorDeep">识别结果</p>
        		</div>
        		<div class="het62"></div>
                <div class="face_Box face_compared">
                    <p class="contrastTit textCenter face_result"><i class="icon"></i><span class="tip"></span></p>
                    <div class="contrastImgBox textCenter">
                        <dl>
                            <dt><img src="" class="currentFaceId"></dt>
                            <dd>本次采集</dd>
                        </dl>
                        <dl class="greenDL">
                            <dt><img src="" class="collectedFaceId"></dt>
                            <dd class="archivePhoto">档案照片</dd>
                        </dl>
                    </div>
                    <div class="face_video_btn textCenter marTop60 face_fail_actionbtn">
                        <a href="javascript:" class="btnBlue btnBlue_158 examAppeal" target="_blank">申诉</a>                        <a href="javascript:" class="disble_time_btn jb_btn_158 fs14 reFaceRecognition">重新识别</a>
                    </div>
                </div>
				<div class="face_Box face_comparing">
                    <p class="contrast_loading"><i><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/load.png"></i>人脸比对中...</p>
                </div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="forceSubmitTip">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#forceSubmitTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;" id="examAddTimeRemindTip">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;max-height:360px;">
			<div class="popHead"><p class="fl fs18 colorDeep">延时提醒</p></div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;">该考试教师进行延时操作</p>
			<p class="popWord fs16 colorIn minutes" style="margin:2px 2px;" data="延时时长：[1]分钟"></p>
			<p class="popWord fs16 colorIn remind" style="margin:6px 2px;overflow: auto;max-height:160px;word-break: break-all;" data="延时原因：[1]"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#examAddTimeRemindTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;" id="confirmPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#confirmPop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">删除后将无法恢复，确认删除？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmDelete" onclick="">确定</a>
				<a href="javascript:" onclick="$('#confirmPop').fullFadeOut();" class="btnBlue btn_92_cancel fr fs14">取消</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;" id="examWrongQuesPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#examWrongQuesPop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">您有正在进行的错题练习，是否继续上次作答？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 continueAnswer" onclick="">继续作答</a>
				<a href="javascript:" onclick="" class="btnBlue btn_92_cancel fr fs14 reAnswer">重刷</a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="lockExamWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">申诉</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="" style="width:88px;">退出考试</a>			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="exitexamForcesubmitWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;">退出考试将强制收卷，确认退出?</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">退出</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="$('#exitexamForcesubmitWin').fullFadeOut();" style="width:88px;">取消</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="exitexamForcesubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;">系统检测到曾退出考试，将强制收卷</div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitexamForcesubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<script type="text/javascript">
    I18N = {
        "examPreparationMinutesTip": "离考试开始还有[1]分钟",
        "examPreparationSecondTip": "离考试开始还有[1]秒",
        "intoexam": "进入考试",
        "confirmEnterTestTip": "本次考试答题时长为[1]分钟，进入考试后开始计时，中途退出或离开考试界面会继续计时，[2]确认进入考试？",
        "autoSubmitTestTip": "考试时间截止后系统将会自动提交试卷，",
        "faceRcognitionFailTip": "人脸照片采集失败，请重试",
        "serviceExceptionTip": "服务异常，请重新开始",
        "faceRcognitionCompareSucceTip": "对比相似度成功，正在进入考试...",
        "faceRcognitionCompareFailTip": "对比相似度失败",
        "faceRcognitionLiveStatusFailTip": "人脸活体识别未通过，匹配异常",
        "archivePhoto": "档案照片",
        "noArchivePhoto": "暂无档案照片",
        "retestDesTip": "开始重考后，请重新提交考试，否则可能影响最终成绩。请确认是否重考？",
        "retestDesTip1": "开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最高成绩为最终成绩，请确认是否重考？",
        "retestDesTip2": "开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最后一次考试成绩为最终成绩，请确认是否重考？",
		 "electronFaceRcognitionCompareSucceTip": "对比相似度成功，请进行下一步",
		 "reIdentify": "重新识别",
        "agreeExamInstruction": '请勾选“我已阅读并同意”',
        "enterExamCodeExam2": '请输入考试码'
    };
</script><script src="//captcha.chaoxing.com/load-t.min.js?_v=29229708"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/stu-web-examnotes.js?v=2025-0214-1711"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/stu-exam-share.js?v=2025-0605-1711"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/pc-exam-sign-min.js?v=2025-0606-2200"></script>
<script type="text/javascript">

$(function(){

	$(".face_agreement").click(function() {
		if ($(this).find(".agree_check").hasClass("agree_checked")) {
			$(this).find(".agree_check").removeClass("agree_checked");
			$('.face_recognition_start').addClass('opa40');
			$('.entrybtn').addClass('opa40');
		} else {
			$(this).find(".agree_check").addClass("agree_checked");
			$('.face_recognition_start').removeClass('opa40');
			if(!$('.entrybtn').hasClass("opa40_freeze")){
				$('.entrybtn').removeClass('opa40');
			}
		}
	})


	$(".AutographDiv .addAutograph").hover(function() {
    		$(".add_autog_ewm").show()
    	}, function() {
    		$(".add_autog_ewm").hide()
    	});

		examPreparationTimeAction();

       
	  
	 typeof(notifyExamSignatureCheck) != 'undefined' && notifyExamSignatureCheck(function(signk,code,count,ecode){});

	if("0" == "1"){
		var firstFocus = $(".subNav");
		if (firstFocus) {
			setTimeout(function() {
				try {
					if (window.top && window.top.accessiblePlugs) {
						window.top.accessiblePlugs.update();
					}
				} catch (e) {
					console.log(e)
				}
				firstFocus.eq(0).focus();
			}, 300)
		}
	}
});
function tabExamNoteTop() {
	if(event.keyCode == 9  && !event.shiftKey) {
		event.preventDefault();
		var firstFocus = $(".subNav");
		if (firstFocus) {
			firstFocus.eq(0).focus();
		}
	}
}
function tabExamNoteEnd() {
	if(event.keyCode == 9  && event.shiftKey) {
		event.preventDefault();
		$("#tabExamNoteEnd").focus();
	}
}
function tabAgreeCheck(obj) {
	if(event.keyCode == 13 || event.keyCode == 32) {
		event.preventDefault();
		var iDom = $(obj).find("i");
		if(event.keyCode == 32){
			$(iDom).addClass("agree_checked");
			$('.face_recognition_start').removeClass('opa40');
			if(!$('.entrybtn').hasClass("opa40_freeze")){
				$('.entrybtn').removeClass('opa40');
			}
			$("#startBtn").attr("tabindex","0")
			return;
		}
		if (iDom){
			$(obj).attr("aria-checked","");
			$(obj).attr("aria-pressed","");
			if ($(iDom).hasClass("agree_checked")) {
				$(iDom).removeClass("agree_checked");
				$('.face_recognition_start').addClass('opa40');
				$('.entrybtn').addClass('opa40');
				$("#startBtn").attr("tabindex","-1")
				if(event.keyCode == 13){
					$(obj).attr("aria-checked","false");
					$(obj).attr("aria-pressed","false");
				}
			} else {
				$(iDom).addClass("agree_checked");
				$('.face_recognition_start').removeClass('opa40');
				if(!$('.entrybtn').hasClass("opa40_freeze")){
					$('.entrybtn').removeClass('opa40');
				}
				$("#startBtn").attr("tabindex","0")
				if(event.keyCode == 13){
					$(obj).attr("aria-checked","true");
					$(obj).attr("aria-pressed","true");
				}
				// $("#startBtn").focus();
			}
		}
	}
}

function checkLoadError(){
     if(typeof jQuery == 'undefined'){
    	 document.getElementById('loadErrorPop').style.display = 'block';
      }
}
</script>
<div class="maskDiv" style="display:none;z-index:1000;" id="loadErrorPop">
	<div class="popSetDiv wid440">
			<div class="popHead RadisTop">
				<a href="javascript:;" class="popClose fr" onclick="document.getElementById('loadErrorPop').style.display='none';"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 color1">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 color2">页面加载异常，请检查网络设置或刷新页面</p>
			<div class="popBottom RadisBom">
				<a href="javascript:;" class="jb_btn jb_btn_92 fr fs14" onclick="document.getElementById('loadErrorPop').style.display='none';" "="">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>
 <link rel="stylesheet" href="//mooc1.chaoxing.com/exam-ans/css/statistic/phone/circle.css?v=1">
<div class="Wrappadding" id="Wrappadding">
	<div class="ui-loader ui-corner-all ui-body-a ui-loader-default"><span class="ui-icon ui-icon-loading"></span></div>
</div>
</body></html>