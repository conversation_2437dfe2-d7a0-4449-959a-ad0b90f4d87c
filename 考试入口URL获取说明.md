# 超星学习通考试入口URL获取脚本使用说明

## 脚本简介

`获取考试入口URL.py` 是基于 `章节测验.py` 开发的专用脚本，用于批量获取超星学习通课程中所有考试的入口链接。

## 主要功能

- ✅ **自动登录**：支持超星学习通账号登录
- ✅ **课程选择**：交互式选择要获取考试的课程
- ✅ **批量获取**：一次性获取课程中所有考试的入口URL
- ✅ **双重URL**：提供考试入口URL和直接考试URL
- ✅ **多格式导出**：支持CSV和JSON格式导出
- ✅ **详细信息**：包含考试标题、状态、ID等完整信息

## 使用方法

### 1. 基本使用

```bash
python 获取考试入口URL.py -u 用户名 -p 密码
```

### 2. 交互式使用

```bash
python 获取考试入口URL.py
```
程序会提示输入用户名和密码，然后显示课程列表供选择。

### 3. 指定课程

```bash
python 获取考试入口URL.py -u 用户名 -p 密码 --course-id 123456 --clazz-id 789012 --person-id 345678
```

### 4. 指定导出格式

```bash
# 只导出CSV格式
python 获取考试入口URL.py -u 用户名 -p 密码 --format csv

# 只导出JSON格式
python 获取考试入口URL.py -u 用户名 -p 密码 --format json

# 导出两种格式（默认）
python 获取考试入口URL.py -u 用户名 -p 密码 --format both
```

### 5. 自定义输出文件名

```bash
python 获取考试入口URL.py -u 用户名 -p 密码 --output 我的考试列表
```

## 命令行参数

| 参数 | 说明 | 必需 | 示例 |
|------|------|------|------|
| `-u, --username` | 超星学习通用户名 | 否* | `-u 13800138000` |
| `-p, --password` | 超星学习通密码 | 否* | `-p mypassword` |
| `--course-id` | 课程ID | 否 | `--course-id 253891757` |
| `--clazz-id` | 班级ID | 否 | `--clazz-id 124398558` |
| `--person-id` | 个人ID | 否 | `--person-id 404722142` |
| `--format` | 导出格式 | 否 | `--format csv` |
| `--output` | 输出文件名前缀 | 否 | `--output 期末考试` |

*如果不提供用户名和密码，程序会提示输入

## 输出文件格式

### CSV格式示例

```csv
考试标题,考试状态,考试ID,入口URL,直接URL,课程ID,班级ID,个人ID
期末考试,未开始,166273372,https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId=253891757&classId=124398558&examId=166273372&cpi=404722142,https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId=253891757&classId=124398558&examId=166273372...,253891757,124398558,404722142
```

### JSON格式示例

```json
{
  "export_time": "2025-01-27 14:30:00",
  "total_exams": 2,
  "exams": [
    {
      "title": "期末考试",
      "status": "未开始",
      "course_id": "253891757",
      "clazz_id": "124398558",
      "cpi": "404722142",
      "exam_id": "166273372",
      "relation_id": "7689933",
      "enc": "56d18f6943af73f3fd9162fb846b85ae",
      "test_paper_id": "1",
      "is_retest": "false",
      "end_time": "1",
      "entrance_url": "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId=253891757&classId=124398558&examId=166273372&cpi=404722142",
      "direct_url": "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId=253891757&classId=124398558&examId=166273372&examAnswerRelationId=7689933&endTime=1&testPaperId=1&isRetest=false&enc=56d18f6943af73f3fd9162fb846b85ae"
    }
  ]
}
```

## URL类型说明

### 1. 入口URL（entrance_url）
- 格式：`https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?...`
- 用途：考试入口页面，包含考试说明、注意事项等
- 特点：需要点击"进入考试"按钮才能开始考试

### 2. 直接URL（direct_url）
- 格式：`https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?...`
- 用途：直接进入考试页面
- 特点：可能需要处理验证码，包含完整的考试参数

## 工作流程

1. **登录验证**：使用提供的账号密码登录学习通
2. **课程选择**：如果未指定课程，显示课程列表供用户选择
3. **获取考试列表**：从指定课程获取所有考试信息
4. **解析考试信息**：提取考试标题、状态、ID等信息
5. **生成URL**：为每个考试生成入口URL和直接URL
6. **导出结果**：将结果保存到CSV和/或JSON文件

## 注意事项

1. **网络连接**：确保网络连接稳定，能够访问学习通服务器
2. **账号权限**：确保账号有访问指定课程的权限
3. **考试状态**：脚本会获取所有考试，包括未开始、进行中、已结束的考试
4. **URL有效性**：生成的URL包含时效性参数，建议及时使用
5. **文件覆盖**：如果输出文件已存在，会被覆盖

## 错误处理

- **登录失败**：检查用户名和密码是否正确
- **课程未找到**：检查课程ID、班级ID、个人ID是否正确
- **考试列表为空**：可能课程中没有考试，或者权限不足
- **网络错误**：检查网络连接，重试操作

## 与章节测验.py的关系

本脚本基于 `章节测验.py` 开发，复用了以下核心功能：
- 登录逻辑
- 课程获取逻辑
- 考试列表获取和解析逻辑
- HTML解析功能

主要区别：
- **专注URL获取**：不进入考试，只获取入口链接
- **批量处理**：一次性处理所有考试
- **结果导出**：支持多种格式导出
- **简化流程**：去除了验证码处理等复杂逻辑

## 使用场景

1. **批量考试管理**：获取课程中所有考试的链接，便于管理
2. **考试监控**：定期获取考试列表，监控考试状态变化
3. **数据分析**：导出考试数据进行分析
4. **自动化集成**：作为其他自动化脚本的数据源

## 示例输出

```
正在登录学习通...
登录成功！
正在获取课程列表...

找到 3 门课程:
1. 高等数学 - 张教授
2. 大学英语 - 李老师
3. 计算机基础 - 王老师

请选择要获取考试URL的课程编号: 1

已选择课程: 高等数学
课程ID: 253891757
班级ID: 124398558
个人ID: 404722142

正在获取考试列表...

找到 2 个考试:

1. 期中考试 - 已完成
   考试ID: 166273371
   入口URL: https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId=253891757&classId=124398558&examId=166273371&cpi=404722142
   直接URL: https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId=253891757&classId=124398558&examId=166273371...

2. 期末考试 - 未开始
   考试ID: 166273372
   入口URL: https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId=253891757&classId=124398558&examId=166273372&cpi=404722142
   直接URL: https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId=253891757&classId=124398558&examId=166273372...

正在导出考试信息...
考试信息已导出到CSV文件: 考试入口URL_20250127_143000.csv
考试信息已导出到JSON文件: 考试入口URL_20250127_143000.json

✓ 成功获取 2 个考试的入口URL
✓ 结果已导出到文件

考试汇总:
- 期中考试 (已完成)
- 期末考试 (未开始)
```
