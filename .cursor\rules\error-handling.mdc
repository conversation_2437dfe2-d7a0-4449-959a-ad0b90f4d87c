---
description:
globs:
alwaysApply: false
---
# 错误处理规范

## 异常类型

项目应定义自己的异常类型层次结构：

```python
# 基础异常类
class ChaoxingError(Exception):
    """超星学习通API基础异常类"""
    pass

# 认证相关异常
class AuthError(ChaoxingError):
    """认证相关异常"""
    pass

class LoginError(AuthError):
    """登录失败异常"""
    pass

# 网络请求相关异常
class RequestError(ChaoxingError):
    """网络请求相关异常"""
    pass

class APIError(RequestError):
    """API调用异常"""
    pass

# 业务逻辑相关异常
class BusinessError(ChaoxingError):
    """业务逻辑相关异常"""
    pass

class HomeworkError(BusinessError):
    """作业相关异常"""
    pass

class PermissionError(BusinessError):
    """权限相关异常"""
    pass
```

## 异常处理原则

1. **具体异常优先**：捕获最具体的异常类型，而不是通用的Exception
2. **提供上下文**：异常信息应包含足够的上下文，便于定位问题
3. **记录异常**：使用日志记录异常信息，而不仅仅是打印到控制台
4. **优雅恢复**：尽可能从异常中恢复，保持程序继续运行
5. **不吞没异常**：不要捕获异常后什么都不做

## 错误处理示例

### 函数级别错误处理

```python
def get_homework_list(session, course_id, clazz_id, person_id):
    """
    获取作业列表
    :param session: 会话对象
    :param course_id: 课程ID
    :param clazz_id: 班级ID
    :param person_id: 个人ID
    :return: 作业列表
    :raises RequestError: 请求失败时抛出
    :raises PermissionError: 权限不足时抛出
    :raises HomeworkError: 作业相关错误时抛出
    """
    try:
        # 参数验证
        if not all([course_id, clazz_id, person_id]):
            raise ValueError("课程ID、班级ID和个人ID不能为空")
            
        # 构建请求
        url = f"https://mooc1.chaoxing.com/mooc2/work/list"
        params = {
            "courseId": course_id,
            "classId": clazz_id,
            "cpi": person_id,
            # 其他参数...
        }
        
        # 发送请求
        response = session.get(url, params=params, timeout=10)
        
        # 检查HTTP状态码
        if response.status_code != 200:
            raise RequestError(f"请求失败，状态码: {response.status_code}")
            
        # 解析响应内容
        soup = BeautifulSoup(response.text, "html.parser")
        
        # 检查是否有错误信息
        error_msg = soup.select_one(".word_null p")
        if error_msg and "无权限" in error_msg.text:
            raise PermissionError(f"获取作业列表失败: {error_msg.text.strip()}")
        elif error_msg:
            raise HomeworkError(f"获取作业列表失败: {error_msg.text.strip()}")
            
        # 处理正常响应
        homework_items = soup.select(".bottomList li")
        # 解析作业列表...
        
        return homeworks
        
    except (requests.Timeout, requests.ConnectionError) as e:
        # 网络错误处理
        logger.error(f"网络错误: {e}")
        raise RequestError(f"网络错误: {e}")
    except ValueError as e:
        # 参数错误处理
        logger.error(f"参数错误: {e}")
        raise
    except Exception as e:
        # 未预期的错误处理
        logger.error(f"获取作业列表时发生未知错误: {e}")
        raise HomeworkError(f"获取作业列表时发生未知错误: {e}")
```

### 调用者错误处理

```python
try:
    homeworks = get_homework_list(session, course_id, clazz_id, person_id)
    print(f"找到 {len(homeworks)} 个作业")
    # 处理作业列表...
except PermissionError as e:
    print(f"权限错误: {e}")
    print("请检查您的登录状态或课程访问权限")
except RequestError as e:
    print(f"请求错误: {e}")
    print("请检查您的网络连接或稍后重试")
except HomeworkError as e:
    print(f"作业错误: {e}")
    print("获取作业列表失败，请稍后重试")
except Exception as e:
    print(f"发生未知错误: {e}")
    print("程序将退出")
    sys.exit(1)
```

## 日志记录

错误处理应与日志记录结合使用：

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chaoxing.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("chaoxing")

# 在异常处理中使用日志
try:
    # 执行操作...
except Exception as e:
    logger.error(f"操作失败: {e}", exc_info=True)
    # 处理异常...
```
