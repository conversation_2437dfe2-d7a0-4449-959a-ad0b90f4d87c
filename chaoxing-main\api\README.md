## 模块说明

- `__init__.py`: 提供格式化输出辅助函数
- `base.py`: 提供核心功能，包含主要的Chaoxing类和学习功能
- `answer.py`: 提供多种题库接口和答题功能
- `answer_check.py`: 答案检查和验证
- `cipher.py`: AES加密解密功能
- `config.py`: 全局配置常量
- `cookies.py`: Cookie管理
- `cxsecret_font.py`: 超星字体解析
- `decode.py`: 解析超星页面数据
- `exceptions.py`: 自定义异常类
- `font_decoder.py`: 字体解码器
- `logger.py`: 日志功能
- `notification.py`: 通知功能
- `process.py`: 进度显示工具
- `captcha.py`: 验证码识别模块