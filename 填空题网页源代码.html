<div class="Py-mian1 singleQuesId" data="404289749" style="">
	<div style="">
		<div class="Py-m1-title fs16 fontLabel" tabindex="0" role="text">
			6.<span class="quesType">[名词解释]</span>
			《禹贡》
		</div>

		<input type="hidden" id="answertype404289749" name="answertype404289749" value="5">

		<input type="hidden" id="wordNum" value="">
		<input type="hidden" id="minimumWords" value="">
		<input type="hidden" id="isAccessibleCustomFid" name="isAccessibleCustomFid" value="0">

		<div class="Briefanswer" style="">
			<div class="jdt" style="padding: 0px; border: none;">
				<div id="ananas-editor-answer404289749" class="ananas-editor-answer" style="">
					<div class="x-component ans-editor-body x-component-default edui-default" id="ans-inner-editor-1001" style="">
						<div id="edui13" class="edui-editor  edui-default" style="width: 754px; z-index: 999;">
							<div id="edui13_toolbarbox" class="edui-editor-toolbarbox edui-default">
								<div id="edui13_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;">
									<div id="edui13_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui13&quot;].showWordImageDialog();">点击上传</div>
									<div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui13&quot;].hideToolbarMsg();">x</div>
									<div id="edui13_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div>
									<div style="height:0;overflow:hidden;clear:both;" class=" edui-default"></div>
								</div>
							</div>
							<div id="edui13_iframeholder" class="edui-editor-iframeholder edui-default" style="overflow: hidden; height: 200px;">
								<iframe id="ueditor_0" width="100%" height="102%" frameborder="0" src="javascript:void(function(){document.open();document.domain=&quot;chaoxing.com&quot;;document.write(&quot;<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.0 Transitional//EN' 'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd'><html xmlns='http://www.w3.org/1999/xhtml' class='view'><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8' /><link rel='stylesheet' type='text/css' href='/ananas/ueditor/themes/iframe_wap_editor.css'/><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:100%;}
body{margin:8px;font-family:sans-serif;font-size:16px;}p{margin:5px 0;}</style></head><body></body><script>window.parent.UE.instants['ueditorInstant0']._setup(document);</script></html>&quot;);document.close();}())"></iframe>
							</div>
							<div id="edui13_bottombar" class="edui-editor-bottomContainer edui-default">
								<table class=" edui-default">
									<tbody class=" edui-default">
										<tr class=" edui-default">
											<td id="edui13_elementpath" class="edui-editor-bottombar edui-default"></td>
											<td id="edui13_wordcount" class="edui-editor-wordcount edui-default"></td>
											<td id="edui13_scale" class="edui-editor-scale edui-default" style="display: none;">
												<div class="edui-editor-icon edui-default"></div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div id="edui13_scalelayer" class=" edui-default"></div>
						</div>
					</div>
				</div>
				<div class="BanswerIcon" style="position: relative;">
					<span class="photoClass" id="fileUploader_0" data-editorindex="0" style="position: relative; z-index: 1;">
						<img class="pic-btn noimg" width="100%" height="100%" src="//mooc1.chaoxing.com/mooc-ans/css/work/phone/s/images/photo.png">
					</span>
					<span class="attachClass webuploader-container" id="webUploader_0" data-editorindex="0">
						<div class="webuploader-pick">
							<img class="noimg" width="100%" height="100%" src="//mooc1.chaoxing.com/mooc-ans/css/work/phone/s/images/add.png">
						</div>
						<div id="rt_rt_1ivj5b3dh1l2umcq5e21tsfm6v1" style="position: absolute; inset: 0px auto auto 0px; width: 20px; height: 19px; overflow: hidden;">
							<input type="file" name="file" class="webuploader-element-invisible" multiple="multiple">
							<label style="opacity: 0; width: 100%; height: 100%; display: block; cursor: pointer; background: rgb(255, 255, 255);"></label>
						</div>
					</span>
					<div id="html5_1ivj5b3dflfdsra5qeh03em4_container" class="moxie-shim moxie-shim-html5" style="position: absolute; top: 25px; left: 25px; width: 20px; height: 20px; overflow: hidden; z-index: 0;">
						<input id="html5_1ivj5b3dflfdsra5qeh03em4" type="file" style="font-size: 999px; opacity: 0; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;" accept="image/jpeg,image/gif,image/png">
					</div>
				</div>
				<textarea id="answer404289749" name="answer404289749" step="0" style="display:none;"></textarea>
			</div>
		</div>

		<script>
			var wordCount = true, wordNum = 0,minimumWords=0;
			        var isAccessibleCustomFid = $("#isAccessibleCustomFid").val()
			        if (isAccessibleCustomFid == "1") {
			            wordCount = false;
			        }
			        wordNum = '';
			        if (wordNum == "") {
			            wordNum = 0;
			        }
			        minimumWords = '';
			        if (minimumWords == "") {
			            minimumWords = 0;
			        }
			        // alert("min:" + minimumWords + "max:" + wordNum)
			        var body = Ext.getBody();
			        editors[0] = Ext.create('ananas.InnerEditor', {
			            renderTo: "ananas-editor-answer404289749",
			                    ueditorConfig: {
			                initialFrameHeight: 200,
			                autoHeightEnabled: true,
			                wordCount : wordCount,
			                minimumWords : minimumWords,
			                maximumWords : wordNum,
			                wordMinManage: '最少'+' {#minword} '+'字',
			                wordMaxManage: '最多'+' {#maxword} '+'字',
			                wordManage: '已输入'+' {#count} '+'字',
			                wordOverFlowMsg: '<span style="color:red;margin-bottom:0;line-height:25px;">'+ '已超出'+' {#overs} '+'字' +'</span>',
			                wordNeedFlowMsg: '<span style="color:red;margin-bottom:0;line-height:25px;">'+ '还需输入'+' {#needs} '+'字' +'</span>',
			                iframeCssUrl: '/ananas/ueditor/themes/iframe_wap_editor.css'
			            }
			        });
			        editors[0].on('ready', function () {
			            var ueditor = editors[0].ueditor;
			            document.activeElement.blur();
			            var c = $("#answer404289749").val();
			            var ctrim = c.trim();
			            if (ctrim) {
							ueditor.setContent(c);
						} else {
							ueditor.setContent("");
						}
						
						ueditor && ueditor.addListener('contentChange', function (e) {
			                var str = ueditor.getContent();
			                var truelyChange = str != $("#answer404289749").val();
			                $("#answer404289749").val(str);
			                if(truelyChange){
			                    stopSwipe();
			                }
			            });
						
			    		ueditor && ueditor.addListener('blur', function (e) {
			                var str = ueditor.getContent();
			                $("#answer404289749").val(str);
						});
			             
						ueditor.emptyPlaceHolder && ueditor.emptyPlaceHolder('请输入答案');
						
			            var imgs = $('.ans-ued-img', ueditor.document.body);
			            if (imgs && imgs.length > 0) {
			                var img = imgs[imgs.length - 1];
			                img.onload = function () {
			                    ueditor.adjustHeight();
			                }
			            }
			        });
			
			                var fileUploaderId = "fileUploader_0";
			        var fileUploaderItem = ("#" + fileUploaderId);
			        initImgWebUpload(fileUploaderId, fileUploaderItem);
			
			        var webUploaderId = "webUploader_0";
			        initWebuploader(webUploaderId);
		</script>


	</div>
</div>