# 章节测验全自动化使用说明

## 更新内容

本次更新将章节测验.py从依赖浏览器自动化的版本升级为完全基于HTTP请求的全自动化版本。

### 主要改进

1. **移除浏览器依赖**：不再需要安装Chrome浏览器或Selenium
2. **纯HTTP请求**：使用requests库进行所有网络操作
3. **优化验证码处理**：基于52pojie文章建议，提高滑块验证码识别准确率
4. **增强重试机制**：支持最多15次验证码处理重试
5. **多策略位置调整**：使用多种像素级微调策略提高成功率

### 技术优化

#### 验证码处理优化

- **直接使用ddddocr识别结果**：基于52pojie文章建议，不对识别结果进行额外调整
- **多位置尝试策略**：对识别位置进行±1到±5像素的微调
- **增加重试次数**：从原来的3次增加到15次
- **智能等待机制**：失败后等待3-5秒再重试

#### 代码结构优化

- **移除Selenium相关代码**：删除所有浏览器自动化相关函数
- **简化依赖关系**：只依赖CaptchaHandler模块和基础HTTP库
- **保持向后兼容**：保留原有的API接口和参数

## 使用方法

### 基本用法

```bash
# 获取考试列表
python 章节测验.py -u 用户名 -p 密码

# 直接进入指定考试
python 章节测验.py -u 用户名 -p 密码 --course-id 课程ID --clazz-id 班级ID --person-id 个人ID --exam-id 考试ID --enter
```

### 参数说明

- `-u, --username`：超星学习通用户名
- `-p, --password`：超星学习通密码
- `--course-id`：课程ID
- `--clazz-id`：班级ID
- `--person-id`：个人ID
- `--exam-id`：考试ID
- `--enter`：直接进入指定考试

### 验证码处理

系统会自动处理滑块验证码，无需人工干预：

1. **自动识别**：使用ddddocr识别滑块位置
2. **多次尝试**：最多尝试15次，每次使用不同的位置微调
3. **智能重试**：失败后自动等待并重新获取验证码
4. **详细日志**：显示每次尝试的详细信息

## 依赖要求

### 必需模块

- `requests`：HTTP请求库
- `BeautifulSoup4`：HTML解析库
- `ddddocr`：验证码识别库
- `loguru`：日志库

### 验证码处理模块

确保`250403超星学习验证码`目录存在且包含`CaptchaHandler.py`文件。

## 故障排除

### 常见问题

1. **无权限访问考试**
   - 系统会自动重试并添加必要的权限验证
   - 检查考试时间是否在有效范围内
   - 确认是否完成了前置任务点
   - 查看详细的错误分析输出

2. **HTTP请求返回404错误**
   - 系统会自动检查登录状态并重新登录
   - 如果仍然失败，请检查账号权限和考试有效性
   - 查看调试输出中的详细错误信息

2. **验证码识别失败**
   - 检查网络连接
   - 确认CaptchaHandler模块正常工作
   - 查看日志中的详细错误信息

3. **登录失败**
   - 检查用户名和密码是否正确
   - 确认账号未被锁定
   - 系统会自动重试登录

4. **考试进入失败**
   - 检查考试是否在有效时间内
   - 确认是否完成了前置任务点
   - 系统会显示具体的错误原因

### 调试信息

系统会自动保存以下调试文件：

- `exam_captcha_page.html`：包含验证码的页面
- `exam_page.html`：考试页面
- `debug_shade.jpg`：验证码背景图
- `debug_cutout.jpg`：验证码滑块图

## 性能优化

### 验证码成功率提升

通过以下策略提高验证码处理成功率：

1. **精确识别**：使用ddddocr的slide_match方法
2. **位置微调**：尝试±10像素范围内的多个位置
3. **重试机制**：失败后重新获取验证码图片
4. **等待策略**：适当的等待时间避免请求过快

### 网络优化

- **会话复用**：使用同一个session处理所有请求
- **超时设置**：合理的超时时间避免长时间等待
- **错误处理**：完善的异常处理机制

## 更新日志

### v2.3.0 (2025-07-26) - URL参数修复版

- **修复URL参数问题**：优先使用代码构建的URL，避免无效的enc=?参数
- **智能URL选择**：检查页面URL的enc参数有效性，只在有效时使用页面URL
- **改进openc参数处理**：确保openc参数正确添加且不重复
- **添加URL调试输出**：显示最终使用的考试URL，便于问题诊断
- **完善参数验证**：检查所有关键参数的有效性

### v2.2.0 (2025-07-26) - 权限访问修复版

- **修复权限访问问题**：解决"无权限访问"错误，成功进入考试页面
- **移除不必要的同意请求**：发现同意协议只是前端UI交互，无需HTTP请求
- **添加考试专用headers**：模拟完整的浏览器请求头，提高权限验证成功率
- **改进成功检查逻辑**：使用多种指标判断是否成功进入考试
- **增强错误诊断**：详细分析失败原因（权限、时间、前置条件等）
- **添加权限重试机制**：检测到权限问题时自动重试

### v2.1.0 (2025-07-26) - 登录状态修复版

- **修复登录状态问题**：解决HTTP请求返回404错误的问题
- **新增登录状态检查**：添加check_login_status函数自动检测登录状态
- **自动重新登录**：检测到登录失效时自动重新登录
- **改进错误处理**：添加详细的响应状态码和URL检查
- **增强调试信息**：提供更详细的错误信息和调试输出
- **重定向检查**：检测并处理到登录页面的重定向

### v2.0.0 (2025-07-26)

- 移除Selenium浏览器自动化依赖
- 实现纯HTTP请求方式的验证码处理
- 基于52pojie文章优化验证码识别策略
- 增加验证码处理重试次数到15次
- 添加多种位置微调策略
- 完善错误处理和日志记录

### v1.x.x (之前版本)

- 基于Selenium的浏览器自动化
- 基础的验证码处理功能
- 考试列表获取和进入功能
