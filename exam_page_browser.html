<html xmlns="http://www.w3.org/1999/xhtml"><head><script>
    _HOST_ = "//mooc1.chaoxing.com";
    _CP_ = "/exam-ans";
    _HOST_CP1_ = "//mooc1.chaoxing.com/exam-ans";
    // _HOST_CP2_ = _HOST_ + _CP_;
    _HOST_CP2_ = _CP_;
</script>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>考试</title>
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/common.css?v=2025-0424-1038">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/pop.css?v=2021-0311-1412">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/examStudent_pop.css?v=2025-0604-1759">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/style.css?v=2025-0424-1038">
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/webmonitor.css?v=2025-0318-1909">
<script src="//mooc1.chaoxing.com/exam-ans/js/ServerHost.js"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/common/jquery.min.js"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/common/jquery-migrate.min.js"></script><script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poptoast.js?v=2021-0917-1623"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poplayout.js"></script>
<style>.clearfix:after{content: " ";display: block;height: 0px;clear: both;}</style></head>

<body style="zoom: 1;">

      <input type="hidden" id="examId" name="examId" value="7689933" class="clearfix">
  <input type="hidden" id="answerId" name="answerId" value="*********">
   <input type="hidden" id="type" value="">
 <input type="hidden" id="qbanksystem" name="qbanksystem" value="0">
<input type="hidden" id="qbankbackurl" name="qbankbackurl" value="">
 <input type="hidden" id="webSnapshotMonitor" name="webSnapshotMonitor" value="0">
 <input type="hidden" id="faceimgs" name="faceimgs" value="">
<input type="hidden" id="needCode" value="0">
<input type="hidden" id="examCheck" value="1">
<input type="hidden" id="captchaCheck" value="1">
<input type="hidden" id="snapshotMonitor" name="snapshotMonitor" value="0">
<input type="hidden" id="checkSecondDeviceLive" value="">
<input type="hidden" id="secondDeviceLiveKey" value="">
<input type="hidden" id="facekey" value="">
<input type="hidden" id="workExamUploadUrl" name="workExamUploadUrl" value="">
<input type="hidden" id="workExamUploadCrcUrl" name="workExamUploadCrcUrl" value="">
<input type="hidden" id="examPreparationTimeMillSeconds" value="0">
<input type="hidden" id="isChaoxingExamPc" value="">
<input type="hidden" id="examPreparationTime" value="0">
<input type="hidden" id="limitTime" value="60">
<input type="hidden" id="expiredAutoSubmit" value="1">
<input type="hidden" id="selftestMode" name="selftestMode" value="0">
<input type="hidden" id="isAccessibleCustomFid" name="isAccessibleCustomFid" value="0">
<input type="hidden" id="faceRecognitionCompare" name="faceRecognitionCompare" value="">
<input type="hidden" id="reset" name="reset" value="false">
<input type="hidden" id="checkLiveDetectionStatus" value="false">
<input type="hidden" id="electronSnapshotMonitor" value="0">
<input type="hidden" id="captchaCaptchaId" value="Ew0z9skxsLzVKQjmeObQiRVLxkxbPkRF">
<input type="hidden" id="captchavalidate" value="">
<input type="hidden" id="browserLocale" value="chinese">
<input type="hidden" id="pcExamClientSign" value="true">
<input type="hidden" id="scoreStandard" value="1">
<input type="hidden" id="signConfig" value="">
<input type="hidden" id="userId" value="340938867">
<input type="hidden" id="studentInfoUrl" value="">

<input type="hidden" id="isStartPage" value="1">
<form id="submitTest">
	<input type="hidden" id="courseId" name="courseId" value="253891757">
	<input type="hidden" id="testPaperId" name="testPaperId" value="7689933">
	<input type="hidden" id="testUserRelationId" name="testUserRelationId" value="*********">
	<input type="hidden" id="classId" name="classId" value="124398558">
	<input type="hidden" id="cpi" name="cpi" value="404720615">
    	</form>


     <div class="subNav" style="z-index:3" tabindex="0" role="option" aria-label="考试 页面">考试</div>
  
<div class="het40"></div>

<div class="main1200">
	
	
	<div class="pad30lr">
		<div class="fanyaInvig">
			          			  
										<div class="InstructionDiv">
						
				     						
					<div class="examinfo">
						<table width="100%" border="0" cellspacing="0" cellpadding="0" role="table" tabindex="0">
							
							<tbody><tr role="row" tabindex="0">
								<td align="left" class="color3">考试名称</td>
								<td align="left">大学生安全教育测试</td>
							</tr>
							<!--
							<tr>
								<td align="left" class="color3">考试地点</td>
								<td align="left">教一楼3区204</td>
							</tr>
							-->
						 							<tr role="row" tabindex="0">
								<td align="left" class="color3">考试时长（分钟）</td>
								<td align="left">60 分钟</td>
							</tr>						
							<tr role="row" tabindex="0">
								<td align="left" class="color3">考试时间</td>
									<td align="left">07-02 19:09 至 07-28 23:59</td>
							</tr>
						
						</tbody></table>
					</div>
					<div class="exam_descrip fs16 color2" tabindex="0" role="option">
						            				 <h2 class="title">考试说明：</h2> <p>1、离开或退出考试界面答题计时不停止，请不要中途离开考试界面。</p> <p>2、保持座位前的桌面干净，不要有与考试无关的内容。</p> <p>3、考试时间截止或答题时间结束，如果处于答题页面，将自动提交试卷。</p> 						
        				        									</div>
					<div class="face_agreement" tabindex="0" role="radio" onclick="checkLoadError();"><i class="agree_check"></i><span>我已阅读并同意</span></div>
					
					
					<div class="AutographDiv">
						
						<!--
						<div class="fl">
							<a href="#" class="addAutograph"><i></i>添加签名</a>
							<div class="add_autog_ewm" style="display:none">
								<dl>
									<dt><img src="temp/ewm.png" /></dt>
									<dd>扫一扫添加签名</dd>
								</dl>
							</div>
						</div>
						<div class="Autog_name fl" style="display:none">
							<div class="namePic fl"><img src="temp/name.png" /></div>
							<div class="name_oprea fr">
								<a href="#" class="name_rewrite"><i><img src="images/eidtIcon.png" /></i>重写</a>
								<a href="#" class="name_del"><i><img src="images/red_del.png" /></i>删除</a>
							</div>
						</div>
						-->
						
						<div class="next_btn_div fr pos_bottom">
														<a href="javascript:void(0);" onclick="preEnterExam()" data="/exam-ans/exam/test/reVersionTestStartNew?courseId=253891757&amp;classId=124398558&amp;tId=7689933&amp;id=*********&amp;p=1&amp;tag=1&amp;enc=?&amp;cpi=404720615&amp;openc=d9aaa0e7c50336cc356662c308403cb5&amp;newMooc=true" class="fs14 disble_time_btn opa40 entrybtn" id="startBtn" tabindex="-1">
																	进入考试															</a>
						</div>
						<div class="clear" tabindex="0" role="option" aria-label="考试 结尾" id="tabExamNoteEnd"></div>
						
					</div>
				</div>
		

				
				
			</div>
			
	</div>
</div>

<div id="captcha"></div>
<div class="maskDiv" style="display:none;z-index:2001;" id="timeOverSubmitConfirmPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">作答时间耗尽，试卷已提交</p>
			<p class="popWord fs16 colorIn" style="margin: 2px 2px;">试卷领取时间：</p>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">考试用时：<span class="consumeMinutes"></span>分钟</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv " style="display:none;z-index:2001;" id="showLoginInfo">
    <div class="popDiv liveEwmPop">
        <div class="popHead">
            <a href="#" class="popClose fr"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#showLoginInfo').fullFadeOut();"></a>
            <p class="fl fs18 colorDeep">直播二维码</p>
        </div>
        <div class="het62"></div>
        <div class="livePop_con">
            <div class="fs16 color1 liveweminfo">
                <p>账号：<span class="color0" id="loginName"></span></p>
                <p>密码：<span class="color0" id="password"></span></p>
            </div>
            <dl class="pop_ewm">
                <dd>二维码仅考试期间有效</dd>
                <dt><img id="ewmUrl" src="" onclick="rereshSecondDeviceQRCode()"></dt>
            </dl>
        </div>

    </div>
</div>

<div class="maskDiv" style="display:none;z-index:2000;" id="submitConfirmPop" tabindex="0" role="alertdialog" aria-label="交卷">
	<div class="popDiv wid440 Marking" style="left:39%;top:30%;width:450px;min-height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep" tabindex="0" role="option">提示</p>
				<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
					<img tabindex="-1" aria-hidden="true" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');">
				</a>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 30px 2px;" tabindex="0" role="option">确认交卷？</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">确定</a>
				<a href="javascript:" class="btnBlue btn_92_cancel fr fs14" onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');">取消</a>
			</div>
			<div class="het72" tabindex="0" role="option" aria-label="弹窗结尾"></div>
				</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="audioLimitTimesWin">
	<div class="popSetDiv wid440">
			<div class="popHead RadisTop">
				<a href="javascript:;" class="popClose fr" onclick="$('#audioLimitTimesWin').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 color1">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 color2 audioLimitTimesTip">此附件仅支持打开 <span></span> 次，你已打开 <span></span> 次，不能再次打开</p>
			<div class="popBottom RadisBom">
				<a href="javascript:;" class="jb_btn jb_btn_92 fr fs14" onclick="$('#audioLimitTimesWin').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv popMoveShowHide" id="confirmEnterWin" style="display:none;z-index:1000;" tabindex="0" role="alertdialog" aria-label="进入考试">
	<div class="popDiv wid440 popMove" style="top: 119px; left: 544px;">
		<div class="popHead">
			<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
				<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#confirmEnterWin').fullFadeOut();">
			</a>
			<p class="fl fs18 colorDeep" style="font-size:18px;" tabindex="-1">提示</p>
		</div>
		<div class="het62"></div>
		<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option">
			<div class=" tip" style="line-height:26px;font-size:16px;min-height: 140px;width:100%;"></div>
		</div>
		<div class="popBottom">
			<a tabindex="0" role="button" id="tabIntoexam2" href="javascript:;" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">进入考试</a>
			<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14" onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;">取消</a>
		</div>
		<div class="het72" tabindex="0" role="option" id="confirmEnterWinEnd" aria-label="弹窗结尾"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="multiTerminalWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:300px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:18px 2px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">继续考试</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;">退出考试</a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;" id="examTipsPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#examTipsPop').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="singleQuesLimitTimePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;"> 本题作答时间已用完，将进入下一题 </div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="singleQuesLimitTimeConfirm();"> 确定 </a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
			 当前考试需按分卷顺序作答，确认进入下一个分卷？ </div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
			<a href="javascript:" class="btnBlue btn_92_cancel fr fs14" onclick="$('#groupPaperLimitTimePop').fullFadeOut();$('#groupStart').val(0)">取消</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimeOverPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
							当前分卷作答时间已用完，将进入下一分卷					</div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimeSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">当前分卷需限时耗尽才允许提交</div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="switchScreenPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#switchScreenPop').fullFadeOut();">确定</a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv popMoveShowHide" id="confirmRetestWin" style="display:none;z-index:1000;" tabindex="0" role="alertdialog" aria-label="重考提示">
	<div class="popDiv wid440 popMove" style="top: 119px; left: 544px;">
		<div class="popHead">
			<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
				<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#confirmRetestWin').fullFadeOut();"></a>
			<p class="fl fs18 colorDeep" style="font-size:18px;">提示</p>
		</div>
		<div class="het62"></div>
		<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option">
			<div class=" tip" style="line-height:26px;font-size:16px;min-height: 80px;width:100%;">开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最高成绩为最终成绩，请确认是否重考？</div>
		</div>
		<div class="popBottom">
			<a tabindex="0" role="button" href="javascript:;" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">确定重考</a>
			<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14" onclick="$('#confirmRetestWin').fullFadeOut();" style="width:88px;">取消</a>
		</div>
					<div class="het72"></div>
			</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="exitTimesSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;" data="系统检测到你已离开考试{1}次，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitTimesSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="exitDurationSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;" data="系统检测到你的切屏时长已超过{1}秒，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitDurationSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:2001;" id="teacherNoticePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">教师提醒</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 10px 2px; height:200px;overflow:auto;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#teacherNoticePop').fullFadeOut()">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv taskStatusShowHide" style="display:none;z-index:1000;" id="stuSelfTestAutoPaper">
	<div class="popDiv wid440 centered">
		<div class="popHead">
			<a href="javascript:" class="popClose popMoveDele fr"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="barDiv">
			<div class="barCon" style="width:10%"></div>
		</div>
		<p class="barInfo" style="margin-bottom:20px">自测试卷生成中，已完成<span id="taskrate">0%</span></p>
		<div class="popBottom">
								</div>
		<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;" id="faceRecognitionComparePop">
	<div class="popDiv iden_resultPop">
        		<div class="popHead">
        			<a href="javascript:" class="popClose fr" onclick="$('#faceRecognitionComparePop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
        			<p class="fl fs18 colorDeep">识别结果</p>
        		</div>
        		<div class="het62"></div>
                <div class="face_Box face_compared">
                    <p class="contrastTit textCenter face_result"><i class="icon"></i><span class="tip"></span></p>
                    <div class="contrastImgBox textCenter">
                        <dl>
                            <dt><img src="" class="currentFaceId"></dt>
                            <dd>本次采集</dd>
                        </dl>
                        <dl class="greenDL">
                            <dt><img src="" class="collectedFaceId"></dt>
                            <dd class="archivePhoto">档案照片</dd>
                        </dl>
                    </div>
                    <div class="face_video_btn textCenter marTop60 face_fail_actionbtn">
                        <a href="javascript:" class="btnBlue btnBlue_158 examAppeal" target="_blank">申诉</a>                        <a href="javascript:" class="disble_time_btn jb_btn_158 fs14 reFaceRecognition">重新识别</a>
                    </div>
                </div>
				<div class="face_Box face_comparing">
                    <p class="contrast_loading"><i><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/load.png"></i>人脸比对中...</p>
                </div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="forceSubmitTip">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#forceSubmitTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;" id="examAddTimeRemindTip">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;max-height:360px;">
			<div class="popHead"><p class="fl fs18 colorDeep">延时提醒</p></div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;">该考试教师进行延时操作</p>
			<p class="popWord fs16 colorIn minutes" style="margin:2px 2px;" data="延时时长：[1]分钟"></p>
			<p class="popWord fs16 colorIn remind" style="margin:6px 2px;overflow: auto;max-height:160px;word-break: break-all;" data="延时原因：[1]"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#examAddTimeRemindTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;" id="confirmPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#confirmPop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">删除后将无法恢复，确认删除？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmDelete" onclick="">确定</a>
				<a href="javascript:" onclick="$('#confirmPop').fullFadeOut();" class="btnBlue btn_92_cancel fr fs14">取消</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;" id="examWrongQuesPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#examWrongQuesPop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">您有正在进行的错题练习，是否继续上次作答？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 continueAnswer" onclick="">继续作答</a>
				<a href="javascript:" onclick="" class="btnBlue btn_92_cancel fr fs14 reAnswer">重刷</a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;" id="lockExamWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">申诉</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="" style="width:88px;">退出考试</a>			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="exitexamForcesubmitWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;">退出考试将强制收卷，确认退出?</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">退出</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="$('#exitexamForcesubmitWin').fullFadeOut();" style="width:88px;">取消</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;" id="exitexamForcesubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;">系统检测到曾退出考试，将强制收卷</div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitexamForcesubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<script type="text/javascript">
    I18N = {
        "examPreparationMinutesTip": "离考试开始还有[1]分钟",
        "examPreparationSecondTip": "离考试开始还有[1]秒",
        "intoexam": "进入考试",
        "confirmEnterTestTip": "本次考试答题时长为[1]分钟，进入考试后开始计时，中途退出或离开考试界面会继续计时，[2]确认进入考试？",
        "autoSubmitTestTip": "考试时间截止后系统将会自动提交试卷，",
        "faceRcognitionFailTip": "人脸照片采集失败，请重试",
        "serviceExceptionTip": "服务异常，请重新开始",
        "faceRcognitionCompareSucceTip": "对比相似度成功，正在进入考试...",
        "faceRcognitionCompareFailTip": "对比相似度失败",
        "faceRcognitionLiveStatusFailTip": "人脸活体识别未通过，匹配异常",
        "archivePhoto": "档案照片",
        "noArchivePhoto": "暂无档案照片",
        "retestDesTip": "开始重考后，请重新提交考试，否则可能影响最终成绩。请确认是否重考？",
        "retestDesTip1": "开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最高成绩为最终成绩，请确认是否重考？",
        "retestDesTip2": "开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最后一次考试成绩为最终成绩，请确认是否重考？",
		 "electronFaceRcognitionCompareSucceTip": "对比相似度成功，请进行下一步",
		 "reIdentify": "重新识别",
        "agreeExamInstruction": '请勾选“我已阅读并同意”',
        "enterExamCodeExam2": '请输入考试码'
    };
</script><script src="//captcha.chaoxing.com/load-t.min.js?_v=29225275"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/stu-web-examnotes.js?v=2025-0214-1711"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/stu-exam-share.js?v=2025-0605-1711"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/pc-exam-sign-min.js?v=2025-0606-2200"></script>
<script type="text/javascript">

$(function(){

	$(".face_agreement").click(function() {
		if ($(this).find(".agree_check").hasClass("agree_checked")) {
			$(this).find(".agree_check").removeClass("agree_checked");
			$('.face_recognition_start').addClass('opa40');
			$('.entrybtn').addClass('opa40');
		} else {
			$(this).find(".agree_check").addClass("agree_checked");
			$('.face_recognition_start').removeClass('opa40');
			if(!$('.entrybtn').hasClass("opa40_freeze")){
				$('.entrybtn').removeClass('opa40');
			}
		}
	})


	$(".AutographDiv .addAutograph").hover(function() {
    		$(".add_autog_ewm").show()
    	}, function() {
    		$(".add_autog_ewm").hide()
    	});

		examPreparationTimeAction();

       
	  
	 typeof(notifyExamSignatureCheck) != 'undefined' && notifyExamSignatureCheck(function(signk,code,count,ecode){});

	if("0" == "1"){
		var firstFocus = $(".subNav");
		if (firstFocus) {
			setTimeout(function() {
				try {
					if (window.top && window.top.accessiblePlugs) {
						window.top.accessiblePlugs.update();
					}
				} catch (e) {
					console.log(e)
				}
				firstFocus.eq(0).focus();
			}, 300)
		}
	}
});
function tabExamNoteTop() {
	if(event.keyCode == 9  && !event.shiftKey) {
		event.preventDefault();
		var firstFocus = $(".subNav");
		if (firstFocus) {
			firstFocus.eq(0).focus();
		}
	}
}
function tabExamNoteEnd() {
	if(event.keyCode == 9  && event.shiftKey) {
		event.preventDefault();
		$("#tabExamNoteEnd").focus();
	}
}
function tabAgreeCheck(obj) {
	if(event.keyCode == 13 || event.keyCode == 32) {
		event.preventDefault();
		var iDom = $(obj).find("i");
		if(event.keyCode == 32){
			$(iDom).addClass("agree_checked");
			$('.face_recognition_start').removeClass('opa40');
			if(!$('.entrybtn').hasClass("opa40_freeze")){
				$('.entrybtn').removeClass('opa40');
			}
			$("#startBtn").attr("tabindex","0")
			return;
		}
		if (iDom){
			$(obj).attr("aria-checked","");
			$(obj).attr("aria-pressed","");
			if ($(iDom).hasClass("agree_checked")) {
				$(iDom).removeClass("agree_checked");
				$('.face_recognition_start').addClass('opa40');
				$('.entrybtn').addClass('opa40');
				$("#startBtn").attr("tabindex","-1")
				if(event.keyCode == 13){
					$(obj).attr("aria-checked","false");
					$(obj).attr("aria-pressed","false");
				}
			} else {
				$(iDom).addClass("agree_checked");
				$('.face_recognition_start').removeClass('opa40');
				if(!$('.entrybtn').hasClass("opa40_freeze")){
					$('.entrybtn').removeClass('opa40');
				}
				$("#startBtn").attr("tabindex","0")
				if(event.keyCode == 13){
					$(obj).attr("aria-checked","true");
					$(obj).attr("aria-pressed","true");
				}
				// $("#startBtn").focus();
			}
		}
	}
}

function checkLoadError(){
     if(typeof jQuery == 'undefined'){
    	 document.getElementById('loadErrorPop').style.display = 'block';
      }
}
</script>
<div class="maskDiv" style="display:none;z-index:1000;" id="loadErrorPop">
	<div class="popSetDiv wid440">
			<div class="popHead RadisTop">
				<a href="javascript:;" class="popClose fr" onclick="document.getElementById('loadErrorPop').style.display='none';"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"></a>
				<p class="fl fs18 color1">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 color2">页面加载异常，请检查网络设置或刷新页面</p>
			<div class="popBottom RadisBom">
				<a href="javascript:;" class="jb_btn jb_btn_92 fr fs14" onclick="document.getElementById('loadErrorPop').style.display='none';" "="">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>
 <link rel="stylesheet" href="//mooc1.chaoxing.com/exam-ans/css/statistic/phone/circle.css?v=1">
<div class="Wrappadding" id="Wrappadding">
	<div class="ui-loader ui-corner-all ui-body-a ui-loader-default"><span class="ui-icon ui-icon-loading"></span></div>
</div>
</body></html>