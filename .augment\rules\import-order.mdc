---
description: 
globs: 
alwaysApply: false
---
# Import Order Rules

## Rule: Standard Import Order

Imports should be grouped in the following order:

1. Standard library imports
2. Related third party imports
3. Local application/library specific imports

Each group should be separated by a blank line.

### Example:

```python
# Standard library imports
import os
import sys
import time
import re
import json
import random
import traceback
from collections import OrderedDict
from datetime import datetime, timedelta

# Third party imports
import requests
import parsel
from bs4 import BeautifulSoup
from loguru import logger
from ddddocr import DdddOcr
import urllib3
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from base64 import b64encode

# Local application imports
from API.Re import strip_title, strip_options
from API.Questionbank import questionbank, questionbank2, fujia
from Config.UserSql import OrderProcessorsql
```

## Rule: Import Style

1. Use absolute imports rather than relative imports
2. Avoid wildcard imports (`from module import *`)
3. Use aliases for imports when it helps readability

### Example:

```python
# Good
import numpy as np
from matplotlib import pyplot as plt

# Avoid
from numpy import *
```

## Rule: Import Organization

1. Alphabetize imports within each group
2. For `from` imports, place them after regular imports in their respective groups

### Example:

```python
# Standard library
import os
import sys
from collections import defaultdict
from datetime import datetime

# Third party
import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
```

