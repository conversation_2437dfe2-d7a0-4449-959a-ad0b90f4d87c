<!-- Generated by SpecStory -->

# 分析项目架构和核心流程 (2025-07-05 11:18Z)

_**User**_

帮我认真的分析这个项目的整体架构，然后分析一下核心流程，以及入口文件，最后分析一下当前函数，具体初始化了什么服务。然后分析完后在接着进入深度分析
最后使用cursor把Cursor Rules分层规则管理 写到.cursor\rules\mport-order.mdc文件里，根据系统的项目可以添加多个mdc文件到.cursor\rules里

---

_**Assistant**_

*You've hit your free requests limit. [Upgrade to Pro](https://www.cursor.com/api/auth/checkoutDeepControl?tier=pro) for more usage, frontier models, Background Agents, and more.*

---

