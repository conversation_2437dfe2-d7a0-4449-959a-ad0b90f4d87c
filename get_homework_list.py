import sys
import urllib3
import time
import json
import requests
from bs4 import BeautifulSoup
from chaoxing_homework_manager import ChaoxingHomeworkManager

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_homework_detail(session, homework_link):
    """
    获取作业详情
    :param session: 请求会话
    :param homework_link: 作业链接
    :return: 作业详情HTML
    """
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    }

    response = session.get(homework_link, headers=headers, verify=False)
    return response.text


def extract_form_data_and_questions(html_content):
    """
    从作业页面HTML中提取表单数据和题目信息
    :param html_content: 作业页面HTML内容
    :return: 表单数据字典、题目ID列表和题目内容字典
    """
    soup = BeautifulSoup(html_content, "html.parser")

    # 提取基本表单字段
    course_id = soup.select_one("#courseId").get("value")
    class_id = soup.select_one("#classId").get("value")
    cpi = soup.select_one("#cpi").get("value")
    work_id = soup.select_one("#workId").get("value")
    answer_id = soup.select_one("#answerId").get("value")
    standard_enc = (
        soup.select_one("#standardEnc").get("value")
        if soup.select_one("#standardEnc")
        else ""
    )
    enc_work = soup.select_one("#enc_work").get("value")
    total_question_num = soup.select_one("#totalQuestionNum").get("value")

    # 提取题目ID列表和题目内容
    question_ids = []
    question_contents = {}
    question_items = soup.select(".questionLi")

    for item in question_items:
        # 从data属性获取题目ID
        question_id = item.get("data")
        if question_id:
            question_ids.append(question_id)

            # 获取题目标题
            title_elem = item.select_one(".mark_name")
            title = (
                title_elem.get_text(strip=True) if title_elem else f"题目 {question_id}"
            )

            # 存储题目信息
            question_contents[question_id] = title

    # 构建表单数据
    form_data = {
        "courseId": course_id,
        "classId": class_id,
        "knowledgeid": "0",
        "cpi": cpi,
        "workRelationId": work_id,
        "workAnswerId": answer_id,
        "jobid": "",
        "standardEnc": standard_enc,
        "enc_work": enc_work,
        "totalQuestionNum": total_question_num,
        "pyFlag": "1",  # 保存模式
        "answerwqbid": ",".join(question_ids) + ",",
        "mooc2": "1",
        "randomOptions": "false",
        "workTimesEnc": "",
    }

    # 添加每个题目的答案类型
    for qid in question_ids:
        form_data[f"answertype{qid}"] = "4"  # 简答题类型

    return form_data, question_ids, question_contents


def submit_homework_answers(session, form_data, question_ids, answers, homework_link):
    """
    提交作业答案
    :param session: 请求会话
    :param form_data: 表单数据
    :param question_ids: 题目ID列表
    :param answers: 题目答案字典，键为题目ID，值为答案
    :param homework_link: 作业链接
    :return: 提交结果
    """
    # 添加答案到表单数据
    for qid in question_ids:
        if qid in answers:
            form_data[f"answer{qid}"] = answers[qid]

    # 构建提交URL
    url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb"

    # 添加URL参数
    url += f"?_classId={form_data['classId']}&courseid={form_data['courseId']}"
    url += f"&token={form_data['enc_work']}&totalQuestionNum={form_data['totalQuestionNum']}"
    url += "&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Pragma": "no-cache",
        "Cache-Control": "no-cache",
        "sec-ch-ua-platform": '"Windows"',
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "Origin": "https://mooc1.chaoxing.com",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": homework_link,
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    }

    # 打印提交的表单数据，用于调试
    print(f"提交URL: {url}")
    print(f"题目数量: {len(question_ids)}")
    print(f"已填写答案的题目数量: {len(answers)}")

    response = session.post(url, data=form_data, headers=headers, verify=False)
    return response.text


def main():
    """
    主函数，用于获取作业列表并为每个题目单独填写答案
    """
    # 用户名和密码，这里使用加密后的值
    username = "D2bCfRqh3U9bDhfsvVUneg=="
    password = "/H+E5YEkVpxVz37gYPB0xg=="

    # 创建作业管理器实例
    manager = ChaoxingHomeworkManager(username, password)

    # 登录
    login_result = manager.login()
    if not login_result:
        print("登录失败，请检查用户名和密码")
        return

    print("登录成功")
    print("等待2秒，确保登录状态生效...")
    time.sleep(2)

    # 获取课程列表
    print("\n获取课程列表...")
    courses = manager.get_course_list()

    if not courses:
        print("未找到课程")
        return

    print(f"找到 {len(courses)} 门课程:")
    for i, course in enumerate(courses):
        print(f"{i+1}. {course['course_name']} - {course['teacher_name']}")

    # 选择课程
    try:
        course_index = int(input("\n请选择要查看作业的课程编号: ")) - 1
        if course_index < 0 or course_index >= len(courses):
            print("无效的课程编号")
            return
    except ValueError:
        print("请输入有效的数字")
        return

    selected_course = courses[course_index]
    print(f"\n已选择课程: {selected_course['course_name']}")

    # 获取作业列表
    print("\n获取作业列表...")
    homeworks = manager.get_homework_list(
        selected_course["course_id"],
        selected_course["clazz_id"],
        selected_course["person_id"],
    )

    if not homeworks:
        print("未找到作业")
        return

    print(f"\n找到 {len(homeworks)} 个作业:")
    for i, homework in enumerate(homeworks):
        # 显示作业ID和标题
        print(
            f"{i+1}. 作业ID: {homework['work_id']} - {homework['title']} - {homework['status']}"
        )

    # 选择作业方式改为输入作业ID
    work_id = input("\n请输入要填写答案的作业ID: ")

    # 查找对应作业ID的作业
    selected_homework = None
    for homework in homeworks:
        if homework["work_id"] == work_id:
            selected_homework = homework
            break

    if not selected_homework:
        print(f"未找到ID为 {work_id} 的作业")
        return

    print(f"\n已选择作业: {selected_homework['title']}")

    # 获取作业详情
    print("\n获取作业详情...")
    homework_html = get_homework_detail(manager.session, selected_homework["link"])

    # 提取表单数据和题目信息
    form_data, question_ids, question_contents = extract_form_data_and_questions(
        homework_html
    )

    print(f"\n找到 {len(question_ids)} 个题目:")
    for i, qid in enumerate(question_ids):
        print(f"{i+1}. {question_contents[qid]}")

    # 为每个题目单独填写答案
    answers = {}
    print("\n请为每个题目填写答案（留空则跳过该题）:")
    for i, qid in enumerate(question_ids):
        print(f"\n题目 {i+1}: {question_contents[qid]}")
        answer = input("请输入答案: ")
        if answer:  # 只有输入了答案才添加
            answers[qid] = answer

    if not answers:
        print("\n没有填写任何答案，操作取消")
        return

    # 确认提交
    confirm = input(
        f"\n已填写 {len(answers)}/{len(question_ids)} 个题目的答案，是否提交？(y/n): "
    )
    if confirm.lower() != "y":
        print("操作已取消")
        return

    # 提交答案
    print("\n正在提交答案...")
    result = submit_homework_answers(
        manager.session, form_data, question_ids, answers, selected_homework["link"]
    )

    # 解析结果
    try:
        result_json = json.loads(result)
        if result_json.get("status"):
            print("\n答案保存成功！")
            print(f"响应信息: {result_json.get('msg', '')}")
        else:
            print(f"\n答案保存失败: {result_json.get('msg', '未知错误')}")
    except Exception as e:
        print(f"\n答案保存结果解析失败: {e}")
        print(f"原始响应: {result}")


if __name__ == "__main__":
    main()
