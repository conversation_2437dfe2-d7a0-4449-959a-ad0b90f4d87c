import sys
import urllib3
import time
import json
import requests
import base64
import pyaes  # 如果没有安装pyaes，请使用命令: pip install pyaes
from bs4 import BeautifulSoup
from chaoxing_homework_manager import ChaoxingHomeworkManager
import re
import os.path
import pickle
import concurrent.futures
import random
from queue import Queue
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# AI接口配置
AI_API_URL = "http://tiku6.cc/api.php?act=aimodel"
AI_API_KEY = "DnDW2hHGcHpiW2WQ"
AI_MODEL = "deepseek-chat"

# AES加密配置
AES_KEY = "u2oh6Vu^HWe4_AES"
COOKIES_PATH = "cookies.txt"

# 全局共享的enc参数缓存
ENC_CACHE = {}
ENC_CACHE_LOCK = threading.Lock()


# PKCS7填充
def pkcs7_padding(s, block_size=16):
    bs = block_size
    return s + (bs - len(s) % bs) * chr(bs - len(s) % bs).encode()


# 分割数据块
def split_to_data_blocks(byte_str, block_size=16):
    length = len(byte_str)
    j, y = divmod(length, block_size)
    blocks = []
    shenyu = j * block_size
    for i in range(j):
        start = i * block_size
        end = (i + 1) * block_size
        blocks.append(byte_str[start:end])
    stext = byte_str[shenyu:]
    if stext:
        blocks.append(stext)
    return blocks


# AES加密类
class AESCipher:
    def __init__(self):
        self.key = AES_KEY.encode("utf8")
        self.iv = AES_KEY.encode("utf8")

    def encrypt(self, plaintext: str):
        ciphertext = b""
        cbc = pyaes.AESModeOfOperationCBC(self.key, self.iv)
        plaintext = plaintext.encode("utf-8")
        blocks = split_to_data_blocks(pkcs7_padding(plaintext))
        for b in blocks:
            ciphertext = ciphertext + cbc.encrypt(b)
        base64_text = base64.b64encode(ciphertext).decode("utf8")
        return base64_text


# 保存cookies
def save_cookies(_session):
    with open(COOKIES_PATH, "wb") as f:
        pickle.dump(_session.cookies, f)


# 使用cookies
def use_cookies():
    if os.path.exists(COOKIES_PATH):
        with open(COOKIES_PATH, "rb") as f:
            _cookies = pickle.load(f)
        return _cookies
    return None


# 登录函数
def login(username, password):
    """
    使用明文用户名和密码登录超星学习通
    :param username: 明文用户名
    :param password: 明文密码
    :return: 登录成功返回会话对象，失败返回None
    """
    try:
        _session = requests.session()
        _session.verify = False
        _url = "https://passport2.chaoxing.com/fanyalogin"

        cipher = AESCipher()
        encrypted_username = cipher.encrypt(username)
        encrypted_password = cipher.encrypt(password)

        print(f"用户名加密结果: {encrypted_username}")
        print(f"密码加密结果: {encrypted_password}")

        _data = {
            "fid": "-1",
            "uname": encrypted_username,
            "password": encrypted_password,
            "refer": "https%3A%2F%2Fi.chaoxing.com",
            "t": "true",
            "forbidotherlogin": "0",
            "validate": "",
            "doubleFactorLogin": "0",
            "independentId": "0",
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "sec-ch-ua-platform": '"Windows"',
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-mobile": "?0",
            "Origin": "https://passport2.chaoxing.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://passport2.chaoxing.com/login?fid=&newversion=true&refer=https%3A%2F%2Fi.chaoxing.com",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }

        response = _session.post(_url, data=_data, headers=headers)

        try:
            result = json.loads(response.text)
            if result.get("status") and result.get("status") is True:
                print("登录成功")
                # 可以选择保存cookies
                # save_cookies(_session)
                return _session
            else:
                print(f"登录失败: {response.text}")
                return None
        except Exception as e:
            print(f"登录过程出错: {e}")
            print(f"响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"登录函数异常: {e}")
        return None


def get_ai_answer(question):
    """
    调用AI接口获取答案
    :param question: 问题内容
    :return: AI回答的内容
    """
    try:
        # 准备请求数据
        data = {
            "key": AI_API_KEY,
            "model": AI_MODEL,
            "question": question,
            "prompt": "你是一个专业的学习助手，请根据问题提供准确、简洁的答案，不要有多余的解释。",
        }

        # 发送请求
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Bearer {AI_API_KEY}",
        }

        response = requests.post(AI_API_URL, data=data, headers=headers, timeout=30)
        result = response.json()

        # 检查响应
        if result.get("code") == 1 and "answer" in result:
            return result["answer"]
        else:
            error_msg = result.get("msg", "未知错误")
            print(f"AI接口返回错误: {error_msg}")
            return f"AI回答失败: {error_msg}"
    except Exception as e:
        print(f"调用AI接口时出错: {e}")
        return f"获取AI回答时出错: {str(e)}"


def get_homework_detail(session, homework_link):
    """
    获取作业详情
    :param session: 请求会话
    :param homework_link: 作业链接
    :return: 作业详情HTML
    """
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    }

    response = session.get(homework_link, headers=headers, verify=False)
    return response.text


def extract_form_data_and_questions(html_content):
    """
    从作业页面HTML中提取表单数据和题目信息
    :param html_content: 作业页面HTML内容
    :return: 表单数据字典、题目ID列表和题目内容字典
    """
    soup = BeautifulSoup(html_content, "html.parser")

    # 提取基本表单字段
    course_id = soup.select_one("#courseId").get("value")
    class_id = soup.select_one("#classId").get("value")
    cpi = soup.select_one("#cpi").get("value")
    work_id = soup.select_one("#workId").get("value")
    answer_id = soup.select_one("#answerId").get("value")
    standard_enc = (
        soup.select_one("#standardEnc").get("value")
        if soup.select_one("#standardEnc")
        else ""
    )
    enc_work = soup.select_one("#enc_work").get("value")
    total_question_num = soup.select_one("#totalQuestionNum").get("value")

    # 提取题目ID列表和题目内容
    question_ids = []
    question_contents = {}
    question_items = soup.select(".questionLi")

    for item in question_items:
        # 从data属性获取题目ID
        question_id = item.get("data")
        if question_id:
            question_ids.append(question_id)

            # 获取题目标题
            title_elem = item.select_one(".mark_name")
            title = (
                title_elem.get_text(strip=True) if title_elem else f"题目 {question_id}"
            )

            # 存储题目信息
            question_contents[question_id] = title

    # 构建表单数据
    form_data = {
        "courseId": course_id,
        "classId": class_id,
        "knowledgeid": "0",
        "cpi": cpi,
        "workRelationId": work_id,
        "workAnswerId": answer_id,
        "jobid": "",
        "standardEnc": standard_enc,
        "enc_work": enc_work,
        "totalQuestionNum": total_question_num,
        "pyFlag": "1",  # 保存模式
        "answerwqbid": ",".join(question_ids) + ",",
        "mooc2": "1",
        "randomOptions": "false",
        "workTimesEnc": "",
    }

    # 添加每个题目的答案类型
    for qid in question_ids:
        form_data[f"answertype{qid}"] = "4"  # 简答题类型

    return form_data, question_ids, question_contents


def submit_homework_answers(session, form_data, question_ids, answers, homework_link):
    """
    提交作业答案
    :param session: 请求会话
    :param form_data: 表单数据
    :param question_ids: 题目ID列表
    :param answers: 题目答案字典，键为题目ID，值为答案
    :param homework_link: 作业链接
    :return: 提交结果
    """
    # 添加答案到表单数据
    for qid in question_ids:
        if qid in answers:
            form_data[f"answer{qid}"] = answers[qid]

    # 构建提交URL
    url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb"

    # 添加URL参数
    url += f"?_classId={form_data['classId']}&courseid={form_data['courseId']}"
    url += f"&token={form_data['enc_work']}&totalQuestionNum={form_data['totalQuestionNum']}"
    url += "&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Pragma": "no-cache",
        "Cache-Control": "no-cache",
        "sec-ch-ua-platform": '"Windows"',
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "Origin": "https://mooc1.chaoxing.com",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": homework_link,
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    }

    # 打印提交的表单数据，用于调试
    print(f"提交URL: {url}")
    print(f"题目数量: {len(question_ids)}")
    print(f"已填写答案的题目数量: {len(answers)}")

    response = session.post(url, data=form_data, headers=headers, verify=False)
    return response.text


def get_course_enc(session, course_id, clazz_id, cpi):
    """
    通过访问课程详情页面，模拟点击作业按钮，获取作业列表的enc参数
    :param session: 请求会话
    :param course_id: 课程ID
    :param clazz_id: 班级ID
    :param cpi: 个人ID
    :return: enc参数字典，包含stuenc和enc两个参数
    """
    try:
        # 构建课程详情页链接
        course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={cpi}&ismooc2=1"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }

        print(f"正在访问课程详情页: {course_url}")

        # 访问课程详情页，可能会重定向
        response = session.get(
            course_url, headers=headers, verify=False, allow_redirects=True
        )

        # 解析页面内容，获取作业按钮的相关信息
        soup = BeautifulSoup(response.text, "html.parser")

        # 首先尝试从页面中获取enc和stuenc参数
        enc = None
        stuenc = None

        # 从隐藏输入框中获取
        enc_input = soup.select_one("#enc")
        if enc_input and enc_input.get("value"):
            enc = enc_input.get("value")
            print(f"从课程详情页面input标签获取enc参数: {enc}")

        # 尝试获取workEnc参数，这可能是作业相关的enc参数
        work_enc_input = soup.select_one("#workEnc")
        if work_enc_input and work_enc_input.get("value"):
            work_enc = work_enc_input.get("value")
            print(f"从课程详情页面input标签获取workEnc参数: {work_enc}")
            # 如果找到workEnc参数，优先使用它作为enc参数
            enc = work_enc
            print(f"使用workEnc作为enc参数: {enc}")

        # 尝试获取其他可能的enc参数
        old_enc_input = soup.select_one("#oldenc")
        if old_enc_input and old_enc_input.get("value"):
            old_enc = old_enc_input.get("value")
            print(f"从课程详情页面input标签获取oldenc参数: {old_enc}")

        # 尝试获取stuenc参数（可能不存在）
        stuenc_input = soup.select_one("#stuenc")
        if stuenc_input and stuenc_input.get("value"):
            stuenc = stuenc_input.get("value")
            print(f"从课程详情页面input标签获取stuenc参数: {stuenc}")

        # 如果没有找到stuenc参数，可以尝试使用userId作为stuenc参数
        if not stuenc:
            user_id_input = soup.select_one("#userId")
            if user_id_input and user_id_input.get("value"):
                user_id = user_id_input.get("value")
                print(f"从课程详情页面input标签获取userId参数: {user_id}")

        # 如果已经获取到了enc参数，但没有stuenc参数，可以尝试使用enc作为stuenc
        if enc and not stuenc:
            print(f"没有找到stuenc参数，尝试使用enc参数作为stuenc: {enc}")
            stuenc = enc

        # 如果已经获取到了参数，可以直接返回
        if enc:  # 只要有enc参数就可以返回，stuenc可以为空
            print(f"从课程详情页面直接获取到参数: stuenc={stuenc}, enc={enc}")
            return {"stuenc": stuenc or enc, "enc": enc}

        # 如果没有直接获取到参数，继续尝试通过点击作业按钮获取
        print("开始查找作业按钮...")

        # 获取所有导航按钮，用于后续分析
        nav_buttons = soup.select(".nav-content ul li")
        print(f"找到 {len(nav_buttons)} 个导航按钮")

        # 记录每个按钮的dataname属性，帮助调试
        button_info = []
        for i, btn in enumerate(nav_buttons):
            dataname = btn.get("dataname", "无dataname")
            text = btn.text.strip() if btn.text else "无文本"
            button_info.append(
                {"index": i, "dataname": dataname, "text": text, "element": btn}
            )
            print(f"导航按钮 {i+1}: dataname={dataname}, text={text[:20]}...")

        # 重要发现：作业按钮(dataname='zy')点击后实际跳转到讨论页面，而考试按钮(dataname='ks')点击后实际跳转到作业页面
        # 因此，我们优先尝试点击考试按钮来获取作业列表

        # 首先尝试找到考试按钮(dataname='ks')
        exam_button_li = None
        for btn in button_info:
            if btn["dataname"] == "ks":
                exam_button_li = btn["element"]
                print(f"找到考试按钮(dataname='ks'): {btn['text']}")
                break

        if exam_button_li:
            # 获取考试按钮的链接
            exam_button = exam_button_li.select_one("a")
            if exam_button:
                # 优先使用data-url属性
                exam_url = exam_button.get("data-url")
                if not exam_url:
                    exam_url = exam_button.get("href")

                if exam_url:
                    print(f"考试按钮链接(实际是作业页面): {exam_url}")

                    # 如果是相对路径，转换为绝对路径
                    if exam_url.startswith("/"):
                        exam_url = f"https://mooc1.chaoxing.com{exam_url}"

                    # 如果是javascript:void(0)这样的链接，需要特殊处理
                    if exam_url.startswith("javascript:"):
                        # 尝试构建作业列表URL
                        exam_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={cpi}&ut=s&t={int(time.time() * 1000)}"
                        print(f"Javascript链接，构建默认作业列表URL: {exam_url}")

                    # 访问考试按钮链接(实际是作业页面)
                    print(f"访问考试按钮链接(实际是作业页面): {exam_url}")
                    exam_response = session.get(exam_url, headers=headers, verify=False)
                    print(f"考试按钮链接响应状态码: {exam_response.status_code}")
                    print(f"考试按钮链接响应URL: {exam_response.url}")

                    # 保存响应内容
                    with open("exam_button_response.html", "w", encoding="utf-8") as f:
                        f.write(exam_response.text)
                    print("已保存考试按钮响应内容到exam_button_response.html")

                    # 从响应URL中提取参数
                    stuenc_match = re.search(r"stuenc=([^&]+)", exam_response.url)
                    enc_match = re.search(r"enc=([^&]+)", exam_response.url)

                    if stuenc_match:
                        stuenc = stuenc_match.group(1)
                        print(f"从考试按钮响应URL中提取到stuenc参数: {stuenc}")

                    if enc_match:
                        enc = enc_match.group(1)
                        print(f"从考试按钮响应URL中提取到enc参数: {enc}")

                    # 检查pageHeader参数，确认是否为作业页面(pageHeader=8)
                    pageheader_match = re.search(r"pageHeader=(\d+)", exam_response.url)
                    if pageheader_match:
                        pageheader = pageheader_match.group(1)
                        print(f"页面pageHeader参数: {pageheader}")
                        if pageheader == "8":
                            print("确认当前页面是作业页面(pageHeader=8)")
                        else:
                            print(
                                f"警告: 当前页面不是作业页面，pageHeader={pageheader}"
                            )

                    # 尝试直接访问作业列表页面
                    if stuenc:
                        work_list_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={cpi}&ut=s&t={int(time.time() * 1000)}&stuenc={stuenc}"
                        if enc:
                            work_list_url += f"&enc={enc}"
                        print(f"直接访问作业列表页面: {work_list_url}")

                        try:
                            list_response = session.get(
                                work_list_url, headers=headers, verify=False
                            )
                        except Exception as e:
                            print(f"访问作业列表页面时出错: {e}")
                            return None

                        print(f"作业列表响应状态码: {list_response.status_code}")
                        print(f"作业列表响应URL: {list_response.url}")

                        # 保存作业列表响应内容
                        with open(
                            "work_list_response.html", "w", encoding="utf-8"
                        ) as f:
                            f.write(list_response.text)
                        print("已保存作业列表响应内容到work_list_response.html")

                        # 从响应URL中提取最终的enc参数
                        final_enc_match = re.search(r"enc=([^&]+)", list_response.url)
                        if final_enc_match:
                            enc = final_enc_match.group(1)
                            print(f"从作业列表响应URL中提取到最终enc参数: {enc}")

                        # 检查作业列表页面内容，查找可能包含enc参数的隐藏输入框
                        list_soup = BeautifulSoup(list_response.text, "html.parser")
                        enc_inputs = list_soup.select(
                            "input[type='hidden'][name='enc'], input[type='hidden'][id='enc']"
                        )
                        for input_elem in enc_inputs:
                            if input_elem.get("value"):
                                enc = input_elem.get("value")
                                print(f"从作业列表页面隐藏输入框中获取到enc参数: {enc}")
                                break

                        # 查找网络请求中的enc参数
                        work_list_links = list_soup.select("a[href*='mooc2/work/list']")
                        for link in work_list_links:
                            href = link.get("href", "")
                            enc_match = re.search(r"enc=([^&]+)", href)
                            if enc_match:
                                enc = enc_match.group(1)
                                print(f"从作业列表链接中获取到enc参数: {enc}")
                                break

        if stuenc and enc:
            return {"stuenc": stuenc, "enc": enc}

        # 如果上面的方法都失败了，尝试直接找到作业按钮(dataname='zy')
        work_button_li = None
        for btn in button_info:
            if btn["dataname"] == "zy":
                work_button_li = btn["element"]
                print(f"找到作业按钮(dataname='zy'): {btn['text']}")
                break

        # 如果找到了作业按钮，尝试获取其链接
        if work_button_li:
            # 获取作业按钮的链接
            work_button = work_button_li.select_one("a")
            if work_button:
                # 优先使用data-url属性
                work_url = work_button.get("data-url")
                if not work_url:
                    work_url = work_button.get("href")

                if work_url:
                    print(f"作业按钮链接(注意：实际可能是讨论页面): {work_url}")

                    # 如果是相对路径，转换为绝对路径
                    if work_url.startswith("/"):
                        work_url = f"https://mooc1.chaoxing.com{work_url}"

                    # 如果是javascript:void(0)这样的链接，需要特殊处理
                    if work_url.startswith("javascript:"):
                        # 尝试构建作业列表URL
                        work_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={cpi}&ut=s&t={int(time.time() * 1000)}"
                        print(f"Javascript链接，构建默认作业列表URL: {work_url}")

                    # 访问作业按钮链接
                    print(f"访问作业按钮链接: {work_url}")
                    work_response = session.get(work_url, headers=headers, verify=False)
                    print(f"作业按钮链接响应状态码: {work_response.status_code}")
                    print(f"作业按钮链接响应URL: {work_response.url}")

                    # 检查pageHeader参数，确认是否为讨论页面(pageHeader=2)
                    pageheader_match = re.search(r"pageHeader=(\d+)", work_response.url)
                    if pageheader_match:
                        pageheader = pageheader_match.group(1)
                        print(f"页面pageHeader参数: {pageheader}")
                        if pageheader == "2":
                            print("确认当前页面是讨论页面(pageHeader=2)，不是作业页面")
                        elif pageheader == "8":
                            print("确认当前页面是作业页面(pageHeader=8)")

                    # 从响应URL中提取参数
                    stuenc_match = re.search(r"stuenc=([^&]+)", work_response.url)
                    enc_match = re.search(r"enc=([^&]+)", work_response.url)

                    if stuenc_match:
                        stuenc = stuenc_match.group(1)
                        print(f"从作业按钮响应URL中提取到stuenc参数: {stuenc}")

                    if enc_match:
                        enc = enc_match.group(1)
                        print(f"从作业按钮响应URL中提取到enc参数: {enc}")

                    if stuenc and enc:
                        # 尝试直接访问作业列表页面
                        work_list_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={cpi}&ut=s&t={int(time.time() * 1000)}&stuenc={stuenc}&enc={enc}"
                        print(f"直接访问作业列表页面: {work_list_url}")

                        list_response = session.get(
                            work_list_url, headers=headers, verify=False
                        )
                        print(f"作业列表响应状态码: {list_response.status_code}")
                        print(f"作业列表响应URL: {list_response.url}")

                        # 从响应URL中提取最终的enc参数
                        final_enc_match = re.search(r"enc=([^&]+)", list_response.url)
                        if final_enc_match:
                            enc = final_enc_match.group(1)
                            print(f"从作业列表响应URL中提取到最终enc参数: {enc}")

                        return {"stuenc": stuenc, "enc": enc}

        # 如果所有方法都失败了，尝试直接构建作业列表URL
        print("尝试直接构建作业列表URL并访问")
        work_list_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={cpi}&ut=s&t={int(time.time() * 1000)}"
        print(f"直接构建的作业列表URL: {work_list_url}")

        list_response = session.get(work_list_url, headers=headers, verify=False)
        print(f"作业列表响应状态码: {list_response.status_code}")
        print(f"作业列表响应URL: {list_response.url}")

        # 保存作业列表响应内容
        with open("direct_work_list_response.html", "w", encoding="utf-8") as f:
            f.write(list_response.text)
        print("已保存直接访问的作业列表响应内容到direct_work_list_response.html")

        stuenc_match = re.search(r"stuenc=([^&]+)", list_response.url)
        enc_match = re.search(r"enc=([^&]+)", list_response.url)

        if stuenc_match:
            stuenc = stuenc_match.group(1)
            print(f"从直接访问的作业列表URL中提取到stuenc参数: {stuenc}")

        if enc_match:
            enc = enc_match.group(1)
            print(f"从直接访问的作业列表URL中提取到enc参数: {enc}")

        if stuenc and enc:
            return {"stuenc": stuenc, "enc": enc}

        # 如果URL中没有参数，尝试从页面内容中提取
        list_soup = BeautifulSoup(list_response.text, "html.parser")

        # 检查是否有错误信息
        error_msg = list_soup.select_one(".word_null p")
        if error_msg:
            print(f"直接访问作业列表出错: {error_msg.text.strip()}")

        # 尝试从页面中的隐藏输入框中提取enc参数
        enc_inputs = list_soup.select(
            "input[type='hidden'][name='enc'], input[type='hidden'][id='enc']"
        )
        for input_elem in enc_inputs:
            if input_elem.get("value"):
                enc = input_elem.get("value")
                print(f"从作业列表页面隐藏输入框中获取到enc参数: {enc}")
                break

        # 如果所有方法都失败了，但至少有一个参数，尝试返回
        if stuenc or enc:
            print(f"部分参数获取成功: stuenc={stuenc}, enc={enc}")
            return {"stuenc": stuenc or enc, "enc": enc or stuenc}

        print("无法获取必要的enc参数")
        return None

    except Exception as e:
        print(f"获取课程enc参数时出错: {e}")
        return None


def get_all_homework_lists(manager, max_workers=5, retry_count=3, retry_delay=5):
    """
    获取所有课程的作业列表，使用多线程并发处理
    :param manager: ChaoxingHomeworkManager 实例
    :param max_workers: 最大线程数
    :param retry_count: 重试次数
    :param retry_delay: 重试延迟(秒)
    :return: 所有课程的作业列表字典，键为课程名称，值为作业列表
    """
    print("\n正在获取课程列表...")
    courses = manager.get_course_list()

    if not courses:
        print("未找到课程")
        return {}

    print(f"找到 {len(courses)} 门课程，开始获取作业列表...")

    # 创建结果字典和线程安全的队列
    all_homeworks = {}
    result_queue = Queue()

    # 创建一个线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务到线程池
        futures = []
        for course in courses:
            future = executor.submit(
                process_course_homework,
                manager,
                course,
                result_queue,
                retry_count,
                retry_delay,
            )
            futures.append(future)

        # 显示进度
        total_courses = len(courses)
        completed = 0
        print(f"\n开始并发获取作业列表，最大线程数: {max_workers}")

        # 等待所有任务完成
        for future in concurrent.futures.as_completed(futures):
            completed += 1
            try:
                # 获取任务结果（如果有异常会在这里抛出）
                future.result()
                print(f"进度: {completed}/{total_courses} 门课程处理完成")
            except Exception as e:
                print(f"处理课程时出错: {e}")

    # 从队列中获取所有结果
    while not result_queue.empty():
        course_name, homeworks = result_queue.get()
        if homeworks:  # 只添加有作业的课程
            all_homeworks[course_name] = homeworks

    return all_homeworks


def process_course_homework(
    manager, course, result_queue, retry_count=3, retry_delay=5
):
    """
    处理单个课程的作业列表，支持重试
    :param manager: ChaoxingHomeworkManager 实例
    :param course: 课程信息字典
    :param result_queue: 结果队列
    :param retry_count: 重试次数
    :param retry_delay: 重试延迟(秒)
    """
    course_name = course["course_name"]
    teacher_name = course["teacher_name"]
    course_id = course["course_id"]
    clazz_id = course["clazz_id"]
    person_id = course["person_id"]

    # 简化输出，只显示课程名称
    print(f"正在处理: {course_name}")

    # 检查缓存中是否已有此课程的enc参数
    cache_key = f"{course_id}_{clazz_id}_{person_id}"
    enc_info = None

    with ENC_CACHE_LOCK:
        if cache_key in ENC_CACHE:
            enc_info = ENC_CACHE[cache_key]

    # 如果没有缓存的enc参数，尝试获取
    if not enc_info:
        # 获取课程enc参数
        enc_info = get_course_enc(
            manager.session,
            course_id,
            clazz_id,
            person_id,
        )

        # 如果成功获取到enc参数，缓存它
        if enc_info:
            with ENC_CACHE_LOCK:
                ENC_CACHE[cache_key] = enc_info

    # 如果成功获取到enc参数，将其添加到manager对象中
    if enc_info:
        # 设置manager对象的属性
        manager.course_enc = enc_info.get("enc")
        manager.stuenc = enc_info.get("stuenc")

    # 随机User-Agent列表
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
    ]

    # 为每次请求设置随机User-Agent
    random_user_agent = random.choice(user_agents)
    if "base_headers" in manager.__dict__ and manager.base_headers:
        manager.base_headers["User-Agent"] = random_user_agent

    # 尝试获取作业列表，支持重试
    homeworks = None
    backoff_factor = 1.0  # 初始退避因子

    for attempt in range(retry_count):
        try:
            # 获取作业列表
            homeworks = manager.get_homework_list(course_id, clazz_id, person_id)

            # 检查是否遇到反爬验证
            if (
                hasattr(manager.session, "history")
                and manager.session.history
                and "antispiderShowVerify" in manager.session.history[-1].url
            ):
                # 计算指数退避延迟时间
                current_delay = retry_delay * backoff_factor + random.uniform(1, 3)
                print(f"遇到反爬验证，等待 {current_delay:.2f} 秒后重试...")
                time.sleep(current_delay)
                backoff_factor *= 1.5  # 增加退避因子

                # 更换User-Agent
                random_user_agent = random.choice(user_agents)
                if "base_headers" in manager.__dict__ and manager.base_headers:
                    manager.base_headers["User-Agent"] = random_user_agent

                continue

            # 如果成功获取作业列表或者确认没有作业，跳出重试循环
            break
        except Exception as e:
            print(f"获取作业列表时出错: {e}")
            if attempt < retry_count - 1:
                # 计算指数退避延迟时间
                current_delay = retry_delay * backoff_factor + random.uniform(1, 3)
                print(f"等待 {current_delay:.2f} 秒后重试...")
                time.sleep(current_delay)
                backoff_factor *= 1.5  # 增加退避因子

                # 更换User-Agent
                random_user_agent = random.choice(user_agents)
                if "base_headers" in manager.__dict__ and manager.base_headers:
                    manager.base_headers["User-Agent"] = random_user_agent
            else:
                print(f"重试 {retry_count} 次后仍然失败")

    # 将结果添加到队列
    if homeworks:
        # 过滤掉已完成的作业
        if not manager.include_finished:
            unfinished_homeworks = [hw for hw in homeworks if hw["status"] != "已完成"]
            filtered_count = len(homeworks) - len(unfinished_homeworks)

            if filtered_count > 0:
                print(
                    f"{course_name}: 过滤 {filtered_count} 个已完成作业，剩余 {len(unfinished_homeworks)} 个"
                )

            if unfinished_homeworks:
                result_queue.put(
                    (f"{course_name} ({teacher_name})", unfinished_homeworks)
                )
            else:
                print(f"{course_name}: 所有作业均已完成")
                result_queue.put((f"{course_name} ({teacher_name})", []))
        else:
            # 不过滤已完成的作业
            print(f"{course_name}: 找到 {len(homeworks)} 个作业")
            result_queue.put((f"{course_name} ({teacher_name})", homeworks))
    else:
        print(f"{course_name}: 未找到作业")
        result_queue.put((f"{course_name} ({teacher_name})", []))

    # 随机延迟，避免请求过于频繁
    time.sleep(random.uniform(1.5, 4.0))


def save_all_homeworks_to_file(all_homeworks, filename="all_homeworks.json"):
    """
    将所有作业信息保存到文件
    :param all_homeworks: 所有作业信息的字典
    :param filename: 保存的文件名
    """
    # 创建一个可以序列化的字典
    serializable_data = {}

    for course_name, homeworks in all_homeworks.items():
        serializable_data[course_name] = []
        for homework in homeworks:
            # 复制作业信息，确保可以序列化
            hw_info = {
                "title": homework.get("title", ""),
                "status": homework.get("status", ""),
                "link": homework.get("link", ""),
                "work_id": homework.get("work_id", ""),
                "answer_id": homework.get("answer_id", ""),
                "course_id": homework.get("course_id", ""),
                "clazz_id": homework.get("clazz_id", ""),
                "cpi": homework.get("cpi", ""),
            }
            serializable_data[course_name].append(hw_info)

    # 保存到文件
    try:
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)
        print(f"\n所有作业信息已保存到文件: {filename}")
        return True
    except Exception as e:
        print(f"\n保存作业信息到文件时出错: {e}")
        return False


def print_all_homeworks(all_homeworks):
    """
    打印所有作业信息
    :param all_homeworks: 所有作业信息的字典
    """
    if not all_homeworks:
        print("没有找到作业")
        return

    print("\n===== 所有课程的作业列表 =====")
    total_homeworks = 0

    for course_name, homeworks in all_homeworks.items():
        if homeworks:  # 只显示有作业的课程
            print(f"\n课程: {course_name}")
            print("-" * 50)

            for i, homework in enumerate(homeworks, 1):
                title = homework.get("title", "未知作业")
                status = homework.get("status", "未知状态")
                link = homework.get("link", "")
                work_id = homework.get("work_id", "")
                answer_id = homework.get("answer_id", "")
                course_id = homework.get("course_id", "")
                clazz_id = homework.get("clazz_id", "")
                cpi = homework.get("cpi", "")

                # 从链接中提取enc参数
                enc = ""
                if link:
                    enc_match = re.search(r"enc=([^&]+)", link)
                    if enc_match:
                        enc = enc_match.group(1)

                print(f"{i}. {title} - {status}")
                if link:
                    print(f"   链接: {link}")
                    # 输出作业参数格式
                    if course_id and clazz_id and cpi and work_id and answer_id and enc:
                        homework_params = (
                            f"{course_id}|{clazz_id}|{cpi}|{work_id}|{answer_id}|{enc}"
                        )
                        print(f"   作业参数: {homework_params}")

                total_homeworks += 1

    print("\n" + "=" * 50)
    print(f"共找到 {len(all_homeworks)} 门课程，{total_homeworks} 个作业")
    print("=" * 50)


def main():
    """
    主函数，用于批量填充所有题目的答案
    """
    # 直接输入账号密码登录
    print("=== 超星学习通作业批量填充系统 ===")
    username = input("请输入账号(手机号/学号): ")
    password = input("请输入密码: ")

    # 使用登录函数进行登录
    session = login(username, password)

    if not session:
        print("登录失败，程序退出")
        return

    # 创建作业管理器实例，使用空的加密账号密码，但传入已登录的session
    manager = ChaoxingHomeworkManager("", "")
    manager.session = session
    manager.cookies = session.cookies
    print("登录成功")

    print("等待2秒，确保登录状态生效...")
    time.sleep(2)

    # 提供功能选择
    print("\n请选择功能:")
    print("1. 获取所有课程的作业列表")
    print("2. 按照原来的方式操作（选择课程和作业）")

    choice = input("请输入选项编号 (默认1): ") or "1"

    if choice == "1":
        # 获取所有课程的作业列表
        all_homeworks = get_all_homework_lists(manager)

        if not all_homeworks:
            print("未获取到任何作业信息，程序退出")
            return

        # 打印所有作业信息
        print_all_homeworks(all_homeworks)

        # 保存到文件
        save_choice = input("\n是否将所有作业信息保存到文件? (y/n, 默认y): ") or "y"
        if save_choice.lower() == "y":
            filename = (
                input("请输入保存的文件名 (默认 all_homeworks.json): ")
                or "all_homeworks.json"
            )
            save_all_homeworks_to_file(all_homeworks, filename)

        print("\n程序执行完毕")
        return

    # 以下是原来的代码逻辑
    # 获取课程列表
    print("\n获取课程列表...")
    courses = manager.get_course_list()

    if not courses:
        print("未找到课程")
        return

    print(f"找到 {len(courses)} 门课程:")
    for i, course in enumerate(courses):
        print(f"{i+1}. {course['course_name']} - {course['teacher_name']}")
        print(
            f"   课程ID: {course['course_id']}, 班级ID: {course['clazz_id']}, 个人ID: {course['person_id']}"
        )

    # 选择课程
    try:
        course_index = int(input("\n请选择要查看作业的课程编号: ")) - 1
        if course_index < 0 or course_index >= len(courses):
            print("无效的课程编号")
            return
    except ValueError:
        print("请输入有效的数字")
        return

    selected_course = courses[course_index]
    print(f"\n已选择课程: {selected_course['course_name']}")
    print(f"课程ID: {selected_course['course_id']}")
    print(f"班级ID: {selected_course['clazz_id']}")
    print(f"个人ID: {selected_course['person_id']}")

    # 获取课程enc参数
    course_enc_info = get_course_enc(
        manager.session,
        selected_course["course_id"],
        selected_course["clazz_id"],
        selected_course["person_id"],
    )

    # 如果成功获取到enc参数，将其添加到manager对象中
    if course_enc_info:
        print(f"将使用获取到的enc参数: {course_enc_info}")
        # 设置manager对象的属性
        manager.course_enc = course_enc_info.get("enc")
        manager.stuenc = course_enc_info.get("stuenc")
    else:
        print("警告: 无法获取必要的enc参数，将尝试在获取作业列表时重新获取")

    # 获取作业列表
    print("\n获取作业列表...")
    print(f"正在获取课程 '{selected_course['course_name']}' 的作业列表")
    print(
        f"课程ID: {selected_course['course_id']}, 班级ID: {selected_course['clazz_id']}, 个人ID: {selected_course['person_id']}"
    )

    homeworks = manager.get_homework_list(
        selected_course["course_id"],
        selected_course["clazz_id"],
        selected_course["person_id"],
    )

    if not homeworks:
        print("未找到作业")
        return

    print(f"\n找到 {len(homeworks)} 个作业:")
    for i, homework in enumerate(homeworks):
        # 显示作业ID和标题
        print(
            f"{i+1}. 作业ID: {homework['work_id']} - {homework['title']} - {homework['status']}"
        )
        print(
            f"   课程ID: {homework['course_id']}, 班级ID: {homework['clazz_id']}, 个人ID: {homework['cpi']}"
        )
        print(f"   作业URL: {homework['link']}")
        # 从链接中提取enc参数
        if homework["link"]:
            enc_match = re.search(r"enc=([^&]+)", homework["link"])
            if enc_match:
                print(f"   作业enc参数: {enc_match.group(1)}")

    # 选择作业方式改为输入作业ID
    work_id = input("\n请输入要批量填充答案的作业ID: ")

    # 查找对应作业ID的作业
    selected_homework = None
    for homework in homeworks:
        if homework["work_id"] == work_id:
            selected_homework = homework
            break

    if not selected_homework:
        print(f"未找到ID为 {work_id} 的作业")
        return

    print(f"\n已选择作业: {selected_homework['title']}")
    print(f"作业ID: {selected_homework['work_id']}")
    print(f"课程ID: {selected_homework['course_id']}")
    print(f"班级ID: {selected_homework['clazz_id']}")
    print(f"个人ID: {selected_homework['cpi']}")

    # 获取作业详情
    print("\n获取作业详情...")
    homework_html = get_homework_detail(manager.session, selected_homework["link"])

    # 提取表单数据和题目信息
    form_data, question_ids, question_contents = extract_form_data_and_questions(
        homework_html
    )

    print(f"\n找到 {len(question_ids)} 个题目")
    for i, qid in enumerate(question_ids):
        print(f"{i+1}. {question_contents[qid]}")

    # 确认是否使用AI回答所有题目
    confirm = input("\n是否使用AI自动回答所有题目？(y/n): ")
    if confirm.lower() != "y":
        print("操作已取消")
        return

    # 使用AI获取每个题目的答案
    print("\n正在使用AI回答题目...")
    answers = {}
    for i, qid in enumerate(question_ids):
        question_title = question_contents[qid]
        print(f"\n正在处理题目 {i+1}/{len(question_ids)}: {question_title}")

        # 调用AI接口获取答案
        ai_answer = get_ai_answer(question_title)
        answers[qid] = ai_answer

        print(
            f"AI回答: {ai_answer[:100]}..."
            if len(ai_answer) > 100
            else f"AI回答: {ai_answer}"
        )

        # 添加延迟，避免频繁请求
        if i < len(question_ids) - 1:
            print("等待2秒...")
            time.sleep(2)

    # 自动提交答案，不再询问用户
    print(
        f"\n已获取 {len(answers)}/{len(question_ids)} 个题目的AI答案，正在自动保存..."
    )

    # 提交答案
    result = submit_homework_answers(
        manager.session, form_data, question_ids, answers, selected_homework["link"]
    )

    # 解析结果
    try:
        result_json = json.loads(result)
        if result_json.get("status"):
            print("\n答案保存成功！")
            print(f"响应信息: {result_json.get('msg', '')}")
        else:
            print(f"\n答案保存失败: {result_json.get('msg', '未知错误')}")
    except Exception as e:
        print(f"\n答案保存结果解析失败: {e}")
        print(f"原始响应: {result}")


def get_all_homeworks_with_credentials(
    username,
    password,
    max_workers=5,
    retry_count=3,
    retry_delay=5,
    include_finished=False,
    include_ended_courses=False,
):
    """
    使用账号密码获取所有作业列表
    :param username: 超星学习通账号
    :param password: 超星学习通密码
    :param max_workers: 最大线程数
    :param retry_count: 重试次数
    :param retry_delay: 重试延迟(秒)
    :param include_finished: 是否包含已完成的作业
    :param include_ended_courses: 是否包含已结束的课程
    :return: 所有作业列表字典
    """
    # 创建ChaoxingHomeworkManager实例
    manager = ChaoxingHomeworkManager(username, password)

    # 登录
    login_success = manager.login()
    if not login_success:
        print("登录失败，无法获取作业列表")
        return {}

    # 设置过滤选项
    manager.include_finished = include_finished
    manager.include_ended_courses = include_ended_courses

    # 获取所有作业列表
    return get_all_homework_lists(
        manager,
        max_workers=max_workers,
        retry_count=retry_count,
        retry_delay=retry_delay,
    )


if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) > 2:
        # 从命令行参数获取账号密码
        username = sys.argv[1]
        password = sys.argv[2]

        # 获取所有作业列表
        all_homeworks = get_all_homeworks_with_credentials(username, password)

        if all_homeworks:
            # 打印所有作业信息
            print_all_homeworks(all_homeworks)

            # 保存到文件
            output_file = "all_homeworks.json"
            if len(sys.argv) > 3:
                output_file = sys.argv[3]

            save_all_homeworks_to_file(all_homeworks, output_file)
        else:
            print("未获取到任何作业信息")
    else:
        # 如果没有命令行参数，则运行交互式界面
        main()
