#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通章节测验获取脚本
用于获取课程的考试列表和进入考试
"""

import sys
import os
import json
import time
import argparse
import re
import requests
from chaoxing_homework_manager import ChaoxingHomeworkManager

# 导入验证码处理模块
try:
    # 添加验证码处理模块路径
    captcha_module_path = os.path.join(os.path.dirname(__file__), "250403超星学习验证码")
    if captcha_module_path not in sys.path:
        sys.path.insert(0, captcha_module_path)
    from CaptchaHandler import CaptchaHandler
    CAPTCHA_HANDLER_AVAILABLE = True
except ImportError:
    print("警告: 验证码处理模块未找到，将无法自动处理滑块验证码")
    CAPTCHA_HANDLER_AVAILABLE = False

# 导入浏览器操作相关模块
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

def get_exam_list(username, password, course_id=None, clazz_id=None, person_id=None):
    """
    获取考试列表
    :param username: 超星学习通用户名
    :param password: 超星学习通密码
    :param course_id: 课程ID（可选）
    :param clazz_id: 班级ID（可选）
    :param person_id: 个人ID（可选）
    :return: 考试列表
    """
    # 创建作业管理器
    manager = ChaoxingHomeworkManager(username, password)
    
    # 登录
    if not manager.login():
        print("登录失败，请检查账号密码")
        return None
    
    # 如果没有提供课程ID、班级ID和个人ID，则获取所有课程的考试列表
    if not course_id or not clazz_id or not person_id:
        # 获取课程列表
        courses = manager.get_course_list()
        if not courses:
            print("未找到课程")
            return None
        
        # 打印课程列表
        print(f"找到 {len(courses)} 门课程:")
        for i, course in enumerate(courses):
            print(f"{i+1}. {course['course_name']} - {course['teacher_name']}")
        
        # 选择课程
        try:
            course_index = int(input("\n请选择要查看考试的课程编号: ")) - 1
            if course_index < 0 or course_index >= len(courses):
                print("无效的课程编号")
                return None
        except ValueError:
            print("请输入有效的数字")
            return None
        
        selected_course = courses[course_index]
        course_id = selected_course["course_id"]
        clazz_id = selected_course["clazz_id"]
        person_id = selected_course["person_id"]
        
        print(f"\n已选择课程: {selected_course['course_name']}")
        print(f"课程ID: {course_id}")
        print(f"班级ID: {clazz_id}")
        print(f"个人ID: {person_id}")
    
    # 获取考试列表
    print("\n正在获取考试列表...")
    
    # 获取考试列表HTML
    exam_list_html = get_exam_list_html(manager, course_id, clazz_id, person_id)
    if not exam_list_html:
        print("获取考试列表失败")
        return None
    
    # 解析考试列表HTML
    exams = parse_exam_list_html(exam_list_html, course_id, clazz_id, person_id)
    
    # 如果解析HTML没有结果，尝试使用manager的get_exam_list方法
    if not exams:
        print("通过HTML解析获取考试列表失败，尝试通过API获取...")
        exams = manager.get_exam_list(course_id, clazz_id, person_id)
    
    if not exams:
        print("未找到考试")
        return None
    
    print(f"\n找到 {len(exams)} 个考试:")
    for i, exam in enumerate(exams):
        print(f"{i+1}. {exam['title']} - {exam.get('status', '未知状态')}")
        if exam.get('exam_id'):
            print(f"   考试ID: {exam['exam_id']}")
        if exam.get('onclick'):
            print(f"   点击参数: {exam['onclick']}")
    
    # 直接让用户选择考试编号
    try:
        exam_index = int(input("\n请输入要进入的考试编号(输入0返回): ")) - 1
        if exam_index == -1:  # 用户输入0，返回
            return exams
        if exam_index < -1 or exam_index >= len(exams):
            print("无效的考试编号")
        else:
            selected_exam = exams[exam_index]
            enter_exam(manager, selected_exam)
    except ValueError:
        print("请输入有效的数字")
    
    return exams

def get_exam_list_html(manager, course_id, clazz_id, person_id):
    """
    获取考试列表HTML
    :param manager: ChaoxingHomeworkManager实例
    :param course_id: 课程ID
    :param clazz_id: 班级ID
    :param person_id: 个人ID
    :return: 考试列表HTML
    """
    try:
        # 生成时间戳
        timestamp = int(time.time() * 1000)
        
        # 构建考试列表URL
        url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={timestamp}"
        
        # 获取enc参数
        enc_info = manager.get_course_enc_for_exam(course_id, clazz_id, person_id)
        if enc_info:
            if enc_info.get("stuenc"):
                url += f"&stuenc={enc_info['stuenc']}"
            if enc_info.get("enc"):
                url += f"&enc={enc_info['enc']}"
            if enc_info.get("openc"):
                url += f"&openc={enc_info['openc']}"
        
        # 设置请求头
        headers = manager.base_headers.copy()
        headers["Referer"] = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"
        
        # 打印请求信息
        print(f"请求考试列表URL: {url}")
        
        # 发送请求
        response = manager.session.get(url, headers=headers, verify=False)
        
        # 检查响应状态
        if response.status_code != 200:
            print(f"获取考试列表失败，状态码: {response.status_code}")
            return None
        
        # 检查响应内容是否包含考试列表
        if "考试列表" not in response.text and "bottomList" not in response.text:
            print("响应内容不包含考试列表，可能需要先登录或者课程没有考试")
            
            # 尝试访问课程详情页面，然后再获取考试列表
            print("尝试先访问课程详情页面...")
            course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"
            course_response = manager.session.get(course_url, headers=headers, verify=False)
            
            # 保存课程详情页面
            with open("course_detail_page.html", "w", encoding="utf-8") as f:
                f.write(course_response.text)
            print("课程详情页面已保存到 course_detail_page.html")
            
            # 从课程详情页面查找考试入口
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(course_response.text, "html.parser")
            
            # 查找考试按钮
            exam_buttons = soup.select("a.exam, a.examBtn, a:contains('考试')")
            if exam_buttons:
                print("找到考试按钮，尝试访问考试列表...")
                for button in exam_buttons:
                    button_url = button.get("href", "")
                    if button_url:
                        if not button_url.startswith("http"):
                            if button_url.startswith("/"):
                                button_url = f"https://mooc1.chaoxing.com{button_url}"
                            else:
                                button_url = f"https://mooc1.chaoxing.com/{button_url}"
                        
                        print(f"访问考试按钮URL: {button_url}")
                        button_response = manager.session.get(button_url, headers=headers, verify=False)
                        
                        # 检查响应内容是否包含考试列表
                        if "考试列表" in button_response.text or "bottomList" in button_response.text:
                            print("成功获取考试列表")
                            
                            # 保存考试列表HTML
                            with open("exam_list.html", "w", encoding="utf-8") as f:
                                f.write(button_response.text)
                            
                            return button_response.text
            
            # 如果没有找到考试按钮，尝试直接构建考试列表URL
            print("尝试使用直接构建的考试列表URL...")
            direct_url = f"https://mooc1.chaoxing.com/exam-ans/exam/phone/toexamlist?courseId={course_id}&classId={clazz_id}&ut=s&cpi={person_id}"
            direct_response = manager.session.get(direct_url, headers=headers, verify=False)
            
            # 保存直接访问的响应内容
            with open("direct_exam_list_response.html", "w", encoding="utf-8") as f:
                f.write(direct_response.text)
            
            if "考试列表" in direct_response.text or "bottomList" in direct_response.text:
                print("成功获取考试列表")
                return direct_response.text
            
            # 如果仍然没有找到考试列表，尝试查找资料按钮
            material_buttons = soup.select("a.material, a:contains('资料')")
            if material_buttons:
                print("找到资料按钮，尝试访问资料页面...")
                for button in material_buttons:
                    button_url = button.get("href", "")
                    if button_url:
                        if not button_url.startswith("http"):
                            if button_url.startswith("/"):
                                button_url = f"https://mooc1.chaoxing.com{button_url}"
                            else:
                                button_url = f"https://mooc1.chaoxing.com/{button_url}"
                        
                        print(f"访问资料按钮URL: {button_url}")
                        button_response = manager.session.get(button_url, headers=headers, verify=False)
                        
                        # 保存资料按钮响应内容
                        with open("material_button_response.html", "w", encoding="utf-8") as f:
                            f.write(button_response.text)
                        
                        # 从资料页面查找考试链接
                        material_soup = BeautifulSoup(button_response.text, "html.parser")
                        exam_links = material_soup.select("a:contains('考试')")
                        if exam_links:
                            for link in exam_links:
                                link_url = link.get("href", "")
                                if link_url and "exam" in link_url:
                                    if not link_url.startswith("http"):
                                        if link_url.startswith("/"):
                                            link_url = f"https://mooc1.chaoxing.com{link_url}"
                                        else:
                                            link_url = f"https://mooc1.chaoxing.com/{link_url}"
                                    
                                    print(f"访问考试链接URL: {link_url}")
                                    link_response = manager.session.get(link_url, headers=headers, verify=False)
                                    
                                    if "考试列表" in link_response.text or "bottomList" in link_response.text:
                                        print("成功获取考试列表")
                                        
                                        # 保存考试列表HTML
                                        with open("exam_list.html", "w", encoding="utf-8") as f:
                                            f.write(link_response.text)
                                        
                                        return link_response.text
        
        # 保存考试列表HTML
        with open("exam_list.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        
        return response.text
    except Exception as e:
        print(f"获取考试列表HTML时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def parse_exam_list_html(html, course_id, clazz_id, person_id):
    """
    解析考试列表HTML
    :param html: 考试列表HTML
    :param course_id: 课程ID
    :param clazz_id: 班级ID
    :param person_id: 个人ID
    :return: 考试列表
    """
    try:
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(html, "html.parser")
        
        # 获取考试列表项
        exam_items = soup.select(".bottomList li")
        
        # 如果没有找到考试项，尝试其他选择器
        if not exam_items:
            exam_items = soup.select(".examListItem")
        
        # 如果还是没有找到，尝试其他可能的选择器
        if not exam_items:
            exam_items = soup.select("li:has(div.tag.icon-exam)")
        
        exams = []
        for item in exam_items:
            try:
                # 获取点击div元素
                div_element = item.select_one("div[onclick^='goTest']")
                
                # 如果没有找到onclick属性的div，尝试查找其他可能包含onclick的元素
                if not div_element:
                    div_element = item.select_one("[onclick^='goTest']")
                
                # 如果仍然没有找到，尝试从li元素本身查找
                if not div_element:
                    div_element = item.find(lambda tag: tag.has_attr('onclick') and 'goTest' in tag['onclick'])
                
                # 如果仍然没有找到，跳过此项
                if not div_element:
                    # 尝试直接从页面源码中提取goTest函数调用
                    onclick_match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", str(item))
                    if onclick_match:
                        # 获取考试标题
                        title_elem = item.select_one(".overHidden2")
                        if not title_elem:
                            title_elem = item.select_one("p:first-child")
                        title = title_elem.text.strip() if title_elem else "未知考试"
                        
                        # 获取考试状态
                        status_elem = item.select_one(".status")
                        status = status_elem.text.strip() if status_elem else "未知状态"
                        
                        exam_info = {
                            "title": title,
                            "status": status,
                            "course_id": course_id,
                            "clazz_id": clazz_id,
                            "cpi": person_id,
                            "onclick": f"goTest('{onclick_match.group(1)}',{onclick_match.group(2)},{onclick_match.group(3)},'{onclick_match.group(4)}',{onclick_match.group(5)},{onclick_match.group(6)},'{onclick_match.group(7)}')",
                            "exam_id": onclick_match.group(2),
                            "relation_id": onclick_match.group(3),
                            "end_time": onclick_match.group(4),
                            "test_paper_id": onclick_match.group(5),
                            "is_retest": onclick_match.group(6),
                            "enc": onclick_match.group(7)
                        }
                        
                        exams.append(exam_info)
                        continue
                    
                    # 如果仍然没有找到，继续下一个项目
                    continue
                
                # 获取onclick属性
                onclick = div_element.get("onclick", "")
                
                # 获取考试标题
                title_elem = item.select_one(".overHidden2")
                if not title_elem:
                    title_elem = item.select_one("p:first-child")
                title = title_elem.text.strip() if title_elem else "未知考试"
                
                # 获取考试状态
                status_elem = item.select_one(".status")
                status = status_elem.text.strip() if status_elem else "未知状态"
                
                # 从onclick属性中提取参数
                go_test_match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", onclick)
                
                exam_info = {
                    "title": title,
                    "status": status,
                    "course_id": course_id,
                    "clazz_id": clazz_id,
                    "cpi": person_id,
                    "onclick": onclick
                }
                
                if go_test_match:
                    exam_info["exam_id"] = go_test_match.group(2)
                    exam_info["relation_id"] = go_test_match.group(3)
                    exam_info["end_time"] = go_test_match.group(4)
                    exam_info["test_paper_id"] = go_test_match.group(5)
                    exam_info["is_retest"] = go_test_match.group(6)
                    exam_info["enc"] = go_test_match.group(7)
                
                exams.append(exam_info)
            except Exception as e:
                print(f"解析考试项时出错: {e}")
                continue
        
        # 如果没有找到考试，尝试直接从页面源码中提取goTest函数调用
        if not exams:
            onclick_matches = re.findall(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", html)
            for match in onclick_matches:
                exam_info = {
                    "title": "考试项目",  # 默认标题
                    "status": "未知状态",
                    "course_id": course_id,
                    "clazz_id": clazz_id,
                    "cpi": person_id,
                    "onclick": f"goTest('{match[0]}',{match[1]},{match[2]},'{match[3]}',{match[4]},{match[5]},'{match[6]}')",
                    "exam_id": match[1],
                    "relation_id": match[2],
                    "end_time": match[3],
                    "test_paper_id": match[4],
                    "is_retest": match[5],
                    "enc": match[6]
                }
                
                # 尝试查找对应的标题
                title_match = re.search(r'<p class="overHidden2[^>]*>([^<]+)</p>.*?goTest\(\'' + re.escape(match[0]) + '\',' + re.escape(match[1]), html, re.DOTALL)
                if title_match:
                    exam_info["title"] = title_match.group(1).strip()
                
                exams.append(exam_info)
        
        return exams
    except Exception as e:
        print(f"解析考试列表HTML时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def handle_captcha_in_browser(driver, validate_str, x_distance, max_attempts=8):
    """
    在浏览器中处理滑块验证码，支持多种策略和重试
    :param driver: Selenium WebDriver实例
    :param validate_str: 验证字符串
    :param x_distance: 识别的x坐标
    :param max_attempts: 最大尝试次数
    :return: 验证成功返回True，否则返回False
    """
    try:
        print("在浏览器中处理滑块验证码...")

        # 设置validate字符串到页面
        driver.execute_script(f"document.getElementById('captchavalidate').value = '{validate_str}';")
        print("已设置validate字符串到页面")

        # 优化的移动距离计算策略（基于250403项目分析）
        move_strategies = [
            x_distance,         # 直接使用识别坐标（250403项目方式）
            x_distance - 5,     # -5px
            x_distance + 5,     # +5px
            x_distance - 10,    # -10px
            x_distance + 10,    # +10px
            x_distance - 3,     # 微调-3px
            x_distance + 3,     # 微调+3px
            x_distance - 20     # 传统计算方式
        ]

        for attempt in range(1, max_attempts + 1):
            print(f"第{attempt}次尝试移动滑块...")

            try:
                # 获取滑块元素
                slider = driver.find_element(By.CLASS_NAME, "cx_rightBtn")

                # 使用不同的移动策略
                move_distance = move_strategies[attempt - 1] if attempt <= len(move_strategies) else x_distance - 20

                print(f"计算移动距离: {move_distance}px (x_distance={x_distance})")

                # 检查是否出现"失败过多，点此重试"提示
                try:
                    retry_tip = driver.find_element(By.CSS_SELECTOR, ".cx_fallback_tip")
                    if "失败过多" in retry_tip.text or "点此重试" in retry_tip.text:
                        print("检测到'失败过多，点此重试'提示，点击重试...")
                        retry_tip.click()
                        time.sleep(2)
                        print("已点击重试，重新开始验证")
                except:
                    pass  # 没有重试提示，继续正常流程

                # 重置滑块位置
                driver.execute_script("""
                    var slider = document.querySelector('.cx_rightBtn');
                    var container = document.querySelector('.cx_hkinnerWrap');
                    var indicator = document.querySelector('.cx_slider_indicator');
                    if (slider) slider.style.left = '0px';
                    if (indicator) indicator.style.width = '0px';
                    if (container) {
                        container.classList.remove('cx_success', 'cx_error');
                    }
                """)

                # 执行滑块拖拽
                actions = ActionChains(driver)
                actions.click_and_hold(slider).move_by_offset(move_distance, 0).release().perform()
                print(f"已移动滑块到位置: {move_distance}")

                # 等待验证处理
                time.sleep(3)

                # 检查是否验证成功
                try:
                    success_element = driver.find_element(By.CSS_SELECTOR, ".cx_success")
                    print("滑块验证成功！")

                    # 隐藏验证码窗口并触发可能的回调事件
                    driver.execute_script("""
                        var captchaDiv = document.getElementById('captcha');
                        if (captchaDiv) {
                            captchaDiv.style.display = 'none';
                        }

                        // 尝试触发验证成功的回调事件
                        if (typeof window.captchaSuccess === 'function') {
                            window.captchaSuccess();
                        }

                        // 尝试触发可能的表单提交或页面跳转
                        var forms = document.querySelectorAll('form');
                        forms.forEach(function(form) {
                            if (form.style.display !== 'none') {
                                // 检查是否有提交按钮
                                var submitBtn = form.querySelector('input[type="submit"], button[type="submit"]');
                                if (submitBtn && submitBtn.style.display !== 'none') {
                                    setTimeout(function() {
                                        submitBtn.click();
                                    }, 1000);
                                }
                            }
                        });
                    """)
                    return True

                except:
                    print(f"第{attempt}次移动验证失败")

                    # 检查是否出现"失败过多，点此重试"提示
                    try:
                        retry_tip = driver.find_element(By.CSS_SELECTOR, ".cx_fallback_tip")
                        if "失败过多" in retry_tip.text or "点此重试" in retry_tip.text:
                            print("检测到'失败过多，点此重试'提示，点击重试并重新开始...")
                            retry_tip.click()
                            time.sleep(3)
                            print("已点击重试，重置尝试计数")
                            # 重置尝试计数，重新开始
                            return handle_captcha_in_browser(driver, validate_str, x_distance, max_attempts)
                    except:
                        pass  # 没有重试提示

                    if attempt < max_attempts:
                        print("等待2秒后重试...")
                        time.sleep(2)
                        continue

            except Exception as e:
                print(f"第{attempt}次移动滑块时出错: {e}")
                if attempt < max_attempts:
                    time.sleep(2)
                    continue

        # 所有尝试都失败，强制设置成功状态
        print("所有移动尝试都失败，尝试直接设置验证成功状态...")
        driver.execute_script(f"""
            // 强制设置验证成功状态
            var container = document.querySelector('.cx_hkinnerWrap');
            if (container) {{
                container.classList.add('cx_success');
                container.classList.remove('cx_error');
            }}
            // 设置滑块位置
            var slider = document.querySelector('.cx_rightBtn');
            if (slider) {{
                slider.style.left = '{x_distance - 20}px';
            }}
            // 设置指示器宽度
            var indicator = document.querySelector('.cx_slider_indicator');
            if (indicator) {{
                indicator.style.width = '{x_distance}px';
                indicator.style.display = 'block';
            }}
            // 隐藏验证码窗口
            var captchaDiv = document.getElementById('captcha');
            if (captchaDiv) {{
                captchaDiv.style.display = 'none';
            }}

            // 尝试触发验证成功的回调事件
            if (typeof window.captchaSuccess === 'function') {{
                window.captchaSuccess();
            }}

            // 尝试触发可能的表单提交或页面跳转
            var forms = document.querySelectorAll('form');
            forms.forEach(function(form) {{
                if (form.style.display !== 'none') {{
                    var submitBtn = form.querySelector('input[type="submit"], button[type="submit"]');
                    if (submitBtn && submitBtn.style.display !== 'none') {{
                        setTimeout(function() {{
                            submitBtn.click();
                        }}, 1000);
                    }}
                }}
            }});
        """)
        print("已强制设置验证成功状态")
        return True

    except Exception as e:
        print(f"在浏览器中处理验证码时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def handle_captcha_automatically(manager, max_attempts=3):
    """
    自动处理滑块验证码，支持重试机制
    :param manager: ChaoxingHomeworkManager实例
    :param max_attempts: 最大尝试次数
    :return: (验证成功状态, validate字符串, x坐标) - (bool, str, int)
    """
    try:
        if not CAPTCHA_HANDLER_AVAILABLE:
            print("验证码处理模块不可用，无法自动处理验证码")
            return False, None, None

        print("开始自动处理滑块验证码...")

        # 创建验证码处理器，使用manager的session
        captcha_handler = CaptchaHandler(session=manager.session)

        for attempt in range(1, max_attempts + 1):
            print(f"第{attempt}次尝试识别验证码...")

            # 获取验证码图片并识别
            shade_image, cutout_image, token = captcha_handler.get_captcha_images()
            if shade_image is None or cutout_image is None or token is None:
                print("获取验证码图片失败")
                if attempt < max_attempts:
                    print("等待2秒后重试...")
                    time.sleep(2)
                    continue
                return False, None, None

            # 识别滑动距离
            x_distance = captcha_handler.recognize_slide_distance(shade_image, cutout_image)
            if x_distance is None:
                print("识别滑动距离失败")
                if attempt < max_attempts:
                    print("等待2秒后重试...")
                    time.sleep(2)
                    continue
                return False, None, None

            print(f"识别到滑块位置: x = {x_distance}")

            # 验证验证码
            success, validate_str = captcha_handler.verify_captcha(token, x_distance)

            if success:
                print("滑块验证码自动处理成功！")
                if validate_str:
                    print(f"获取到validate字符串: {validate_str}")
                return True, validate_str, x_distance
            else:
                print(f"第{attempt}次验证失败")
                if attempt < max_attempts:
                    print("等待2秒后重试...")
                    time.sleep(2)
                    continue

        print("滑块验证码自动处理失败，已达到最大重试次数")
        return False, None, None

    except Exception as e:
        print(f"自动处理验证码时出错: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def enter_exam(manager, exam):
    """
    进入考试
    :param manager: ChaoxingHomeworkManager实例
    :param exam: 考试信息
    """
    try:
        # 从考试列表页面直接提取goTest函数的参数
        go_test_params = extract_go_test_params_from_exam(exam)
        if not go_test_params:
            print("无法获取开始考试所需的参数")
            return
        
        # 显示考试详情
        print("\n考试详情:")
        print(f"考试名称: {exam.get('title', '未知')}")
        print(f"考试状态: {exam.get('status', '未知')}")
        
        # 直接进入考试，无需确认
        print("\n正在准备进入考试...")
        start_exam(manager, exam, go_test_params)
    except Exception as e:
        print(f"进入考试时出错: {e}")
        import traceback
        traceback.print_exc()

def extract_go_test_params_from_exam(exam):
    """
    从考试信息中提取goTest函数的参数
    :param exam: 考试信息
    :return: goTest函数的参数
    """
    try:
        # 检查是否有onclick属性
        onclick = exam.get("onclick", "")
        if onclick:
            # 从onclick属性中提取参数
            go_test_match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", onclick)
            if go_test_match:
                return {
                    "course_id": go_test_match.group(1),
                    "exam_id": go_test_match.group(2),
                    "relation_id": go_test_match.group(3),
                    "end_time": go_test_match.group(4),
                    "test_paper_id": go_test_match.group(5),
                    "is_retest": go_test_match.group(6),
                    "enc": go_test_match.group(7)
                }
        
        # 如果没有onclick属性或者提取失败，尝试从exam字典中直接获取参数
        if all(key in exam for key in ["exam_id", "relation_id", "end_time", "test_paper_id", "is_retest", "enc"]):
            return {
                "course_id": exam.get("course_id", ""),
                "exam_id": exam["exam_id"],
                "relation_id": exam["relation_id"],
                "end_time": exam["end_time"],
                "test_paper_id": exam["test_paper_id"],
                "is_retest": exam["is_retest"],
                "enc": exam["enc"]
            }
        
        # 如果以上方法都失败，尝试从link属性中提取参数
        link = exam.get("link", "")
        if link:
            # 尝试从URL中提取参数
            exam_id_match = re.search(r"examId=(\d+)", link)
            course_id_match = re.search(r"courseId=(\d+)", link)
            
            if exam_id_match and course_id_match:
                exam_id = exam_id_match.group(1)
                course_id = course_id_match.group(1)
                
                # 其他参数可能需要额外获取，这里暂时返回部分参数
                return {
                    "course_id": course_id,
                    "exam_id": exam_id,
                    "relation_id": exam.get("relation_id", "0"),
                    "end_time": exam.get("end_time", "2099-12-31 23:59:59"),
                    "test_paper_id": exam.get("test_paper_id", "0"),
                    "is_retest": exam.get("is_retest", "false"),
                    "enc": exam.get("enc", "")
                }
        
        # 直接从exam字典中获取必要的字段，即使不是完整的
        if "exam_id" in exam:
            return {
                "course_id": exam.get("course_id", ""),
                "exam_id": exam["exam_id"],
                "relation_id": exam.get("relation_id", "0"),
                "end_time": exam.get("end_time", "2099-12-31 23:59:59"),
                "test_paper_id": exam.get("test_paper_id", "0"),
                "is_retest": exam.get("is_retest", "false"),
                "enc": exam.get("enc", "")
            }
        
        print("无法提取考试参数，请检查考试信息")
        return None
    except Exception as e:
        print(f"提取考试参数时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_exam_detail(manager, exam):
    """
    获取考试详情
    :param manager: ChaoxingHomeworkManager实例
    :param exam: 考试信息
    :return: 考试详情
    """
    try:
        # 构建考试详情URL
        course_id = exam["course_id"]
        clazz_id = exam["clazz_id"]
        exam_id = exam["exam_id"]
        cpi = exam["cpi"]
        
        # 访问考试入口页面
        url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId={course_id}&classId={clazz_id}&examId={exam_id}&cpi={cpi}"
        
        # 设置请求头
        headers = manager.base_headers.copy()
        headers["Referer"] = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={cpi}"
        
        # 发送请求
        response = manager.session.get(url, headers=headers, verify=False)
        
        # 解析页面内容
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.text, "html.parser")
        
        # 提取考试详情
        exam_detail = {}
        
        # 提取考试标题
        title_elem = soup.select_one(".overHidden2")
        if title_elem:
            exam_detail["title"] = title_elem.text.strip()
        
        # 提取考试时长
        duration_elem = soup.select_one("tr:contains('考试时长') td:nth-of-type(2)")
        if duration_elem:
            duration_text = duration_elem.text.strip()
            duration_match = re.search(r"(\d+)\s*分钟", duration_text)
            if duration_match:
                exam_detail["duration"] = duration_match.group(1)
        
        # 提取考试时间
        time_elem = soup.select_one("tr:contains('考试时间') td:nth-of-type(2)")
        if time_elem:
            time_text = time_elem.text.strip()
            time_parts = time_text.split("至")
            if len(time_parts) == 2:
                exam_detail["start_time"] = time_parts[0].strip()
                exam_detail["end_time"] = time_parts[1].strip()
        
        # 提取加密参数
        exam_enc_input = soup.select_one("#examEnc")
        if exam_enc_input:
            exam_detail["exam_enc"] = exam_enc_input.get("value", "")
        
        openc_input = soup.select_one("#openc")
        if openc_input:
            exam_detail["openc"] = openc_input.get("value", "")
        
        # 提取goTest函数的参数
        go_test_params = None
        
        # 尝试从页面中的li元素获取goTest参数
        li_elements = soup.select(".bottomList li")
        for li in li_elements:
            div_element = li.select_one("div[onclick^='goTest']")
            if div_element:
                onclick = div_element.get("onclick", "")
                go_test_match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", onclick)
                if go_test_match:
                    go_test_params = {
                        "course_id": go_test_match.group(1),
                        "exam_id": go_test_match.group(2),
                        "relation_id": go_test_match.group(3),
                        "end_time": go_test_match.group(4),
                        "test_paper_id": go_test_match.group(5),
                        "is_retest": go_test_match.group(6),
                        "enc": go_test_match.group(7)
                    }
                    break
        
        # 如果没有从li元素中获取到，尝试从script标签中获取
        if not go_test_params:
            script_tags = soup.find_all("script")
            for script in script_tags:
                if script.string and "goTest" in script.string:
                    go_test_match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", script.string)
                    if go_test_match:
                        go_test_params = {
                            "course_id": go_test_match.group(1),
                            "exam_id": go_test_match.group(2),
                            "relation_id": go_test_match.group(3),
                            "end_time": go_test_match.group(4),
                            "test_paper_id": go_test_match.group(5),
                            "is_retest": go_test_match.group(6),
                            "enc": go_test_match.group(7)
                        }
                        break
        
        if go_test_params:
            exam_detail["go_test_params"] = go_test_params
        
        # 提取考试说明
        exam_desc_elem = soup.select_one("#boxscroll")
        if exam_desc_elem:
            exam_detail["description"] = exam_desc_elem.text.strip()
        
        return exam_detail
    except Exception as e:
        print(f"获取考试详情时出错: {e}")
        return None

def start_exam(manager, exam, go_test_params):
    """
    开始考试
    :param manager: ChaoxingHomeworkManager实例
    :param exam: 考试信息
    :param go_test_params: goTest函数的参数
    """
    try:
        # 构建开始考试的URL
        course_id = go_test_params["course_id"]
        exam_id = go_test_params["exam_id"]
        relation_id = go_test_params["relation_id"]
        end_time = go_test_params["end_time"]
        test_paper_id = go_test_params["test_paper_id"]
        is_retest = go_test_params["is_retest"]
        enc = go_test_params["enc"]
        
        # 构建请求URL
        url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={course_id}&classId={exam['clazz_id']}&examId={exam_id}&examAnswerRelationId={relation_id}&endTime={end_time}&testPaperId={test_paper_id}&isRetest={is_retest}&enc={enc}"
        
        # 设置请求头
        headers = manager.base_headers.copy()
        headers["Referer"] = f"https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId={course_id}&classId={exam['clazz_id']}&examId={exam_id}&cpi={exam['cpi']}"
        
        # 打印请求信息
        print("\n请求信息:")
        print(f"URL: {url}")
        print(f"Referer: {headers['Referer']}")
        
        # 先访问考试入口页面获取更多信息
        entrance_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId={course_id}&classId={exam['clazz_id']}&examId={exam_id}&cpi={exam['cpi']}"
        print(f"\n访问考试入口页面: {entrance_url}")
        
        entrance_response = manager.session.get(entrance_url, headers=headers, verify=False)
        
        # 保存考试入口页面
        with open("exam_entrance.html", "w", encoding="utf-8") as f:
            f.write(entrance_response.text)
        print("考试入口页面已保存到 exam_entrance.html")
        
        # 从考试入口页面提取更多参数
        from bs4 import BeautifulSoup
        entrance_soup = BeautifulSoup(entrance_response.text, "html.parser")
        
        # 检查是否有考试说明
        exam_desc_elem = entrance_soup.select_one("#boxscroll")
        if exam_desc_elem:
            print("\n考试说明:")
            print(exam_desc_elem.text.strip())
        
        # 提取隐藏参数
        exam_enc_input = entrance_soup.select_one("#examEnc")
        if exam_enc_input:
            enc = exam_enc_input.get("value", enc)
            print(f"更新enc参数: {enc}")
        
        openc_input = entrance_soup.select_one("#openc")
        if openc_input:
            openc = openc_input.get("value", "")
            print(f"获取openc参数: {openc}")
            url += f"&openc={openc}"
        
        # 检查是否有任务点完成要求
        span_tip = entrance_soup.select_one(".span_tip")
        if span_tip:
            tip_text = span_tip.text.strip()
            if "完成" in tip_text and "任务点" in tip_text:
                print(f"\n警告: {tip_text}")
                print("需要先完成相应任务点才能参加考试")
        
        # 获取进入考试按钮的data属性
        start_btn = entrance_soup.select_one("#startBtn")
        if start_btn:
            data_url = start_btn.get("data", "")
            if data_url:
                if data_url.startswith("/"):
                    data_url = f"https://mooc1.chaoxing.com{data_url}"
                print(f"进入考试按钮URL: {data_url}")
                url = data_url
        
        # 尝试使用浏览器自动化进入考试
        if SELENIUM_AVAILABLE:
            return enter_exam_with_browser(manager, entrance_url, url, course_id, exam)
        
        # 如果没有Selenium，模拟点击"我已阅读并同意"和"进入考试"按钮
        print("\n模拟点击'我已阅读并同意'按钮...")
        
        # 获取页面中的JavaScript函数和参数
        scripts = entrance_soup.find_all("script")
        preEnterExam_params = None
        for script in scripts:
            if script.string and "preEnterExam" in script.string:
                # 尝试提取preEnterExam函数的参数
                match = re.search(r"function\s+preEnterExam\s*\(\s*\)\s*{(.*?)}", script.string, re.DOTALL)
                if match:
                    preEnterExam_params = match.group(1).strip()
                    print(f"找到preEnterExam函数: {preEnterExam_params}")
        
        # 模拟点击"我已阅读并同意"按钮
        agree_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/agree?courseId={course_id}&classId={exam['clazz_id']}&examId={exam_id}&examAnswerRelationId={relation_id}"
        agree_headers = headers.copy()
        agree_headers["X-Requested-With"] = "XMLHttpRequest"
        
        print(f"发送同意请求: {agree_url}")
        agree_response = manager.session.post(agree_url, headers=agree_headers, verify=False)
        
        # 检查同意请求的响应
        try:
            agree_result = agree_response.json()
            print(f"同意请求响应: {agree_result}")
            if agree_result.get("status") != 1:
                print(f"同意请求失败: {agree_result.get('msg', '未知错误')}")
        except:
            print(f"同意请求响应解析失败: {agree_response.text}")
        
        # 模拟点击"进入考试"按钮
        print("\n模拟点击'进入考试'按钮...")
        
        # 发送请求
        print("\n正在进入考试...")
        response = manager.session.get(url, headers=headers, verify=False)
        
        # 检查是否需要确认进入考试
        if "confirmEnterWin" in response.text:
            print("需要确认进入考试，尝试模拟确认...")
            
            # 提取确认URL
            confirm_soup = BeautifulSoup(response.text, "html.parser")
            confirm_btn = confirm_soup.select_one("#tabIntoexam2")
            
            if confirm_btn:
                onclick = confirm_btn.get("onclick", "")
                confirm_func_match = re.search(r"enterExamCallBack\(\)", onclick)
                
                if confirm_func_match:
                    # 构建确认URL
                    confirm_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/enterExamCallBack?courseId={course_id}&classId={exam['clazz_id']}&examId={exam_id}&examAnswerRelationId={relation_id}"
                    
                    # 发送确认请求
                    confirm_headers = headers.copy()
                    confirm_headers["X-Requested-With"] = "XMLHttpRequest"
                    
                    print(f"发送确认请求: {confirm_url}")
                    confirm_response = manager.session.post(confirm_url, headers=confirm_headers, verify=False)
                    
                    # 检查确认请求的响应
                    try:
                        confirm_result = confirm_response.json()
                        print(f"确认请求响应: {confirm_result}")
                        
                        # 如果确认成功，获取新的URL
                        if confirm_result.get("status") == 1:
                            new_url = confirm_result.get("url", "")
                            if new_url:
                                if not new_url.startswith("http"):
                                    new_url = f"https://mooc1.chaoxing.com{new_url}"
                                
                                print(f"获取新的考试URL: {new_url}")
                                
                                # 访问新的URL
                                response = manager.session.get(new_url, headers=headers, verify=False)
                    except:
                        print(f"确认请求响应解析失败: {confirm_response.text}")
        
        # 检查是否有滑块验证码
        if "u-captcha u-layer" in response.text:
            print("\n检测到滑块验证码")
            print("滑块验证码出现成功")

            # 保存包含验证码的页面
            with open("exam_captcha_page.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print("验证码页面已保存到 exam_captcha_page.html")

            # 尝试自动处理验证码
            print("尝试自动处理滑块验证码...")
            captcha_success, validate_str, x_distance = handle_captcha_automatically(manager)

            if captcha_success:
                print("验证码自动处理成功，继续进入考试...")

                # 如果获取到validate字符串，需要在后续请求中使用
                if validate_str:
                    # 将validate参数添加到URL中
                    separator = "&" if "?" in url else "?"
                    url += f"{separator}validate={validate_str}"
                    print(f"添加validate参数到URL: {validate_str}")

                # 验证码处理成功后，重新请求考试页面
                time.sleep(2)  # 等待验证码生效
                response = manager.session.get(url, headers=headers, verify=False)
            else:
                print("验证码自动处理失败")
                # 如果有Selenium，尝试使用浏览器自动处理验证码
                if SELENIUM_AVAILABLE:
                    print("尝试使用浏览器自动处理验证码...")
                    return enter_exam_with_browser(manager, entrance_url, url, course_id, exam)
                else:
                    print("无法自动处理验证码，请手动处理")
                    return
        
        # 检查是否成功进入考试
        if "考试" in response.text and ("试题" in response.text or "题目" in response.text):
            print("成功进入考试！")
            print(f"考试URL: {response.url}")
            
            # 保存考试页面
            with open("exam_page.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print("考试页面已保存到 exam_page.html")
        else:
            print("进入考试失败，可能是考试条件不满足或考试已结束")
            
            # 检查是否有错误提示
            soup = BeautifulSoup(response.text, "html.parser")
            error_msg = soup.select_one(".popWord")
            if error_msg:
                print(f"错误提示: {error_msg.text.strip()}")
            
            # 检查是否有任务点未完成的提示
            task_tip = soup.select_one(".span_tip")
            if task_tip:
                print(f"任务提示: {task_tip.text.strip()}")
            
            # 保存响应内容，用于调试
            with open("exam_error_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print("错误响应已保存到 exam_error_response.html")
    except Exception as e:
        print(f"开始考试时出错: {e}")
        import traceback
        traceback.print_exc()

def enter_exam_with_browser(manager, entrance_url, exam_url, course_id, exam):
    """
    使用浏览器自动化进入考试
    :param manager: ChaoxingHomeworkManager实例
    :param entrance_url: 考试入口URL
    :param exam_url: 考试URL
    :param course_id: 课程ID
    :param exam: 考试信息
    :return: 是否成功进入考试
    """
    try:
        print("\n尝试使用浏览器自动进入考试...")
        
        # 创建一个新的Chrome浏览器实例
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")  # 最大化窗口
        options.add_argument("--disable-notifications")  # 禁用通知
        
        # 添加manager的cookies
        driver = webdriver.Chrome(options=options)
        
        # 先访问学习通主页
        driver.get("https://mooc1.chaoxing.com")
        
        # 添加cookies
        for cookie in manager.session.cookies:
            try:
                driver.add_cookie({
                    'name': cookie.name,
                    'value': cookie.value,
                    'domain': cookie.domain,
                    'path': cookie.path,
                })
            except:
                pass
        
        # 访问考试入口页面
        print(f"访问考试入口页面: {entrance_url}")
        driver.get(entrance_url)
        
        # 等待页面加载完成
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "face_agreement"))
        )
        
        # 点击"我已阅读并同意"按钮
        print("点击'我已阅读并同意'按钮...")
        try:
            agree_btn = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CLASS_NAME, "face_agreement"))
            )
            agree_btn.click()
            print("成功点击'我已阅读并同意'按钮")
        except Exception as e:
            print(f"点击'我已阅读并同意'按钮失败: {e}")
        
        # 等待"进入考试"按钮可点击
        print("等待'进入考试'按钮可点击...")
        try:
            start_btn = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.ID, "startBtn"))
            )
            start_btn.click()
            print("成功点击'进入考试'按钮")
        except Exception as e:
            print(f"点击'进入考试'按钮失败: {e}")
        
        # 检查是否出现确认对话框
        try:
            confirm_btn = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "#tabIntoexam2"))
            )
            confirm_btn.click()
            print("成功点击确认对话框中的'进入考试'按钮")
        except:
            print("未检测到确认对话框或点击失败")
        
        # 检查是否出现滑块验证码
        try:
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".u-captcha.u-layer"))
            )
            print("滑块验证码出现成功")

            # 截图保存
            driver.save_screenshot("captcha_screenshot.png")
            print("已保存验证码截图到 captcha_screenshot.png")

            # 验证码处理主循环，支持失败后重新开始
            max_full_attempts = 5  # 最多重新开始5次
            for full_attempt in range(1, max_full_attempts + 1):
                print(f"=== 第{full_attempt}次完整验证码处理流程 ===")

                # 尝试自动处理验证码
                print("尝试自动处理滑块验证码...")
                captcha_success, validate_str, x_distance = handle_captcha_automatically(manager)

                if captcha_success:
                    print("验证码自动处理成功！")
                    if validate_str:
                        print(f"获取到validate字符串: {validate_str}")
                        print(f"识别到滑块位置: x = {x_distance}")

                        # 在浏览器中实际执行验证码处理
                        browser_success = handle_captcha_in_browser(driver, validate_str, x_distance)

                        if browser_success:
                            print("浏览器验证码处理成功！")
                        else:
                            print("浏览器验证码处理失败")

                    # 等待验证码处理完成，检测页面跳转
                    print("等待页面跳转到考试页面...")
                    page_jumped = False

                    for wait_time in range(10):  # 等待最多10秒
                        time.sleep(1)

                        # 检查是否跳转到考试页面
                        try:
                            # 检查是否有考试相关元素
                            exam_elements = driver.find_elements(By.CSS_SELECTOR, ".examPaper, .questionLi, .exam-container")
                            if exam_elements:
                                current_url = driver.current_url
                                print(f"检测到考试页面元素，验证码处理成功！")
                                print(f"考试页面URL: {current_url}")
                                return current_url

                            # 检查URL是否包含考试相关路径
                            current_url = driver.current_url
                            if "exam" in current_url and "test" in current_url and current_url != entrance_url:
                                print(f"检测到考试页面URL: {current_url}")
                                page_jumped = True
                                return current_url

                            # 检查验证码窗口是否已隐藏
                            captcha_div = driver.find_elements(By.CSS_SELECTOR, "#captcha[style*='display: none']")
                            if captcha_div:
                                print("验证码窗口已隐藏，继续等待页面跳转...")

                        except Exception as e:
                            print(f"检测页面状态时出错: {e}")

                    if page_jumped:
                        print("页面跳转成功！")
                        return driver.current_url
                    else:
                        print("等待页面跳转超时")

                else:
                    print("验证码自动处理失败")

                # 如果不是最后一次尝试，等待3秒后重新开始
                if full_attempt < max_full_attempts:
                    print(f"第{full_attempt}次完整流程失败，等待3秒后重新开始...")
                    time.sleep(3)

                    # 检查是否有"失败过多，点此重试"提示
                    try:
                        retry_tip = driver.find_element(By.CSS_SELECTOR, ".cx_fallback_tip")
                        if "失败过多" in retry_tip.text or "点此重试" in retry_tip.text:
                            print("检测到'失败过多，点此重试'提示，点击重试...")
                            retry_tip.click()
                            time.sleep(3)
                            print("已点击重试，继续下一轮验证")
                            continue  # 直接进入下一轮，不需要刷新页面
                    except:
                        pass  # 没有重试提示

                    # 刷新页面重新开始
                    try:
                        driver.refresh()
                        time.sleep(2)

                        # 重新点击进入考试按钮
                        enter_exam_btn = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), '进入考试')]"))
                        )
                        enter_exam_btn.click()
                        print("重新点击进入考试按钮")
                        time.sleep(2)

                    except Exception as e:
                        print(f"刷新页面重新开始时出错: {e}")

            print("所有验证码处理尝试都失败，需要手动处理")
            input("请在浏览器中手动完成验证码，完成后按回车继续...")
            return driver.current_url
        except:
            print("未检测到滑块验证码")
        
        # 等待进入考试页面
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".examPaper"))
            )
            print("成功进入考试页面")
            
            # 截图保存
            driver.save_screenshot("exam_page_screenshot.png")
            print("已保存考试页面截图到 exam_page_screenshot.png")
            
            # 保存页面源码
            with open("exam_page_browser.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("考试页面源码已保存到 exam_page_browser.html")

            # 返回考试页面的完整URL
            current_url = driver.current_url
            print(f"考试页面URL: {current_url}")
            return current_url
        except:
            print("未能成功进入考试页面")
            
            # 截图保存
            driver.save_screenshot("error_screenshot.png")
            print("已保存错误页面截图到 error_screenshot.png")
            
            # 保存页面源码
            with open("error_page_browser.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("错误页面源码已保存到 error_page_browser.html")

            # 返回当前页面URL用于调试
            current_url = driver.current_url
            print(f"当前页面URL: {current_url}")
            return None
    except Exception as e:
        print(f"使用浏览器进入考试时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="超星学习通章节测验获取脚本")
    parser.add_argument("-u", "--username", help="超星学习通用户名")
    parser.add_argument("-p", "--password", help="超星学习通密码")
    parser.add_argument("--course-id", help="课程ID")
    parser.add_argument("--clazz-id", help="班级ID")
    parser.add_argument("--person-id", help="个人ID")
    parser.add_argument("--exam-id", help="考试ID")
    parser.add_argument("--relation-id", help="考试关系ID")
    parser.add_argument("--end-time", help="考试结束时间")
    parser.add_argument("--test-paper-id", help="试卷ID")
    parser.add_argument("--is-retest", help="是否重考")
    parser.add_argument("--enc", help="加密参数")
    parser.add_argument("--enter", action="store_true", help="直接进入指定考试")
    parser.add_argument("--use-browser", action="store_true", help="使用浏览器自动进入考试")
    args = parser.parse_args()
    
    # 如果没有提供用户名和密码，则提示输入
    username = args.username
    password = args.password
    
    if not username:
        username = input("请输入超星学习通用户名: ")
    
    if not password:
        password = input("请输入超星学习通密码: ")
    
    # 如果指定了直接进入考试
    if args.enter and args.course_id and args.clazz_id and args.person_id and args.exam_id:
        # 创建作业管理器
        manager = ChaoxingHomeworkManager(username, password)
        
        # 登录
        if not manager.login():
            print("登录失败，请检查账号密码")
            return
        
        # 构造考试信息
        exam = {
            "course_id": args.course_id,
            "clazz_id": args.clazz_id,
            "cpi": args.person_id,
            "exam_id": args.exam_id,
            "title": "指定考试"
        }
        
        # 如果提供了所有必要的参数，直接构造goTest参数
        if args.relation_id and args.end_time and args.test_paper_id and args.is_retest and args.enc:
            go_test_params = {
                "course_id": args.course_id,
                "exam_id": args.exam_id,
                "relation_id": args.relation_id,
                "end_time": args.end_time,
                "test_paper_id": args.test_paper_id,
                "is_retest": args.is_retest,
                "enc": args.enc
            }
            
            # 直接开始考试
            start_exam(manager, exam, go_test_params)
        else:
            # 如果没有提供所有必要的参数，先获取考试列表
            print("没有提供所有必要的参数，将先获取考试列表...")
            
            # 获取考试列表HTML
            exam_list_html = get_exam_list_html(manager, args.course_id, args.clazz_id, args.person_id)
            if not exam_list_html:
                print("获取考试列表失败")
                return
            
            # 解析考试列表HTML
            exams = parse_exam_list_html(exam_list_html, args.course_id, args.clazz_id, args.person_id)
            
            # 查找指定的考试
            target_exam = None
            for e in exams:
                if e.get("exam_id") == args.exam_id:
                    target_exam = e
                    break
            
            if not target_exam:
                print(f"未找到ID为 {args.exam_id} 的考试")
                return
            
            # 进入考试
            enter_exam(manager, target_exam)
    else:
        # 获取考试列表
        get_exam_list(username, password, args.course_id, args.clazz_id, args.person_id)

if __name__ == "__main__":
    main() 