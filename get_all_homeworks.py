#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通作业列表获取工具
用于获取所有课程的作业列表并保存到文件
"""

import sys
import os
import argparse
import time
import random
import re  # Added for regex extraction
from batch_fill_homework import (
    get_all_homeworks_with_credentials,
    print_all_homeworks,
    save_all_homeworks_to_file,
)


def main():
    """
    主函数，处理命令行参数并执行相应操作
    """
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="超星学习通作业列表获取工具")

    # 添加命令行参数
    parser.add_argument("-u", "--username", help="超星学习通账号")
    parser.add_argument("-p", "--password", help="超星学习通密码")
    parser.add_argument(
        "-o",
        "--output",
        default="all_homeworks.json",
        help="输出文件名 (默认: all_homeworks.json)",
    )
    parser.add_argument(
        "-s", "--silent", action="store_true", help="静默模式，不显示详细信息"
    )
    parser.add_argument(
        "-q",
        "--quiet",
        action="store_true",
        help="安静模式，只显示最终作业列表，不显示进度信息",
    )
    parser.add_argument(
        "-t", "--threads", type=int, default=3, help="并发线程数 (默认: 3)"
    )
    parser.add_argument(
        "-r", "--retry", type=int, default=5, help="遇到错误时的重试次数 (默认: 5)"
    )
    parser.add_argument(
        "-d", "--delay", type=int, default=10, help="基础重试延迟秒数 (默认: 10)"
    )
    parser.add_argument(
        "--min-interval",
        type=float,
        default=2.0,
        help="请求最小间隔时间(秒) (默认: 2.0)",
    )
    parser.add_argument(
        "--max-interval",
        type=float,
        default=5.0,
        help="请求最大间隔时间(秒) (默认: 5.0)",
    )
    parser.add_argument(
        "--smart-mode", action="store_true", help="智能模式，自动调整请求参数以避免反爬"
    )
    parser.add_argument(
        "--include-finished", action="store_true", help="包含已完成的作业"
    )
    parser.add_argument(
        "--include-ended-courses", action="store_true", help="包含已结束的课程"
    )
    parser.add_argument(
        "--only-unfinished", action="store_true", help="只获取未完成的作业（默认行为）"
    )
    parser.add_argument(
        "--params-only", action="store_true", help="只输出作业参数，不保存JSON文件"
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 如果没有提供账号密码，则交互式获取
    username = args.username
    password = args.password

    if not username:
        username = input("请输入超星学习通账号: ")

    if not password:
        import getpass

        password = getpass.getpass("请输入超星学习通密码: ")

    # 显示运行参数，只有在非静默模式且非安静模式下才显示
    if not args.silent and not args.quiet:
        print("\n===== 超星学习通作业列表获取工具 =====")
        print(f"并发线程数: {args.threads}")
        print(f"重试次数: {args.retry}")
        print(f"基础重试延迟: {args.delay}秒")
        print(f"请求间隔: {args.min_interval}-{args.max_interval}秒")
        print(f"智能模式: {'启用' if args.smart_mode else '禁用'}")
        print(f"包含已完成作业: {'是' if args.include_finished else '否'}")
        print(f"包含已结束课程: {'是' if args.include_ended_courses else '否'}")
        print("=====================================\n")

    # 如果启用智能模式，根据线程数自动调整参数
    if args.smart_mode and not args.quiet:
        if args.threads > 3:
            # 线程数较多时，增加请求间隔和延迟
            args.min_interval = max(args.min_interval, 3.0)
            args.max_interval = max(args.max_interval, 7.0)
            args.delay = max(args.delay, 15)
            if not args.silent:
                print("[智能模式] 线程数较多，已自动增加请求间隔和延迟时间")

        # 随机等待一段时间再开始，避免多个实例同时启动
        if args.threads > 1:
            start_delay = random.uniform(1, 3)
            if not args.silent:
                print(f"[智能模式] 随机等待 {start_delay:.2f} 秒后开始...")
            time.sleep(start_delay)

    start_time = time.time()

    # 在非安静模式下显示进度信息
    if not args.quiet:
        print("正在获取作业列表，请稍候...")

    try:
        # 获取所有作业列表
        all_homeworks = get_all_homeworks_with_credentials(
            username,
            password,
            max_workers=args.threads,
            retry_count=args.retry,
            retry_delay=args.delay,
            include_finished=args.include_finished,
            include_ended_courses=args.include_ended_courses,
        )

        if all_homeworks:
            # 如果只需要输出参数
            if args.params_only:
                print("\n===== 作业参数列表 =====")
                for course_name, homeworks in all_homeworks.items():
                    for homework in homeworks:
                        title = homework.get("title", "未知作业")
                        link = homework.get("link", "")
                        work_id = homework.get("work_id", "")
                        answer_id = homework.get("answer_id", "")
                        course_id = homework.get("course_id", "")
                        clazz_id = homework.get("clazz_id", "")
                        cpi = homework.get("cpi", "")

                        # 从链接中提取enc参数
                        enc = ""
                        if link:
                            enc_match = re.search(r"enc=([^&]+)", link)
                            if enc_match:
                                enc = enc_match.group(1)

                        # 输出作业参数格式
                        if (
                            course_id
                            and clazz_id
                            and cpi
                            and work_id
                            and answer_id
                            and enc
                        ):
                            homework_params = f"{course_id}|{clazz_id}|{cpi}|{work_id}|{answer_id}|{enc}"
                            print(f"{title}: {homework_params}")
            else:
                # 打印所有作业信息
                if not args.silent:
                    print_all_homeworks(all_homeworks)

                # 保存到文件
                save_all_homeworks_to_file(all_homeworks, args.output)
                if not args.quiet:
                    print(f"作业信息已保存到文件: {args.output}")

            # 统计信息
            total_courses = len(all_homeworks)
            total_homeworks = sum(
                len(homeworks) for homeworks in all_homeworks.values()
            )
            elapsed_time = time.time() - start_time

            if not args.quiet:
                print(f"\n执行完成，用时: {elapsed_time:.2f} 秒")
                print(f"共获取到 {total_courses} 门课程，{total_homeworks} 个作业")
        else:
            print("未获取到任何作业信息")
            return 1
    except KeyboardInterrupt:
        print("\n用户中断操作，程序退出")
        return 1
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
