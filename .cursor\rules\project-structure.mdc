---
description:
globs:
alwaysApply: false
---
# 超星学习通项目结构规范

## 目录结构

项目应遵循以下目录结构：

```
crawl4ai/
│
├── core/                     # 核心功能模块
│   ├── __init__.py
│   ├── auth.py               # 认证相关功能
│   ├── course.py             # 课程管理功能
│   ├── homework.py           # 作业管理功能
│   └── utils.py              # 工具函数
│
├── api/                      # API接口定义
│   ├── __init__.py
│   └── chaoxing_api.py       # 超星API接口定义
│
├── models/                   # 数据模型
│   ├── __init__.py
│   ├── course.py             # 课程模型
│   └── homework.py           # 作业模型
│
├── config/                   # 配置文件
│   ├── __init__.py
│   └── settings.py           # 全局设置
│
├── scripts/                  # 脚本工具
│   ├── batch_fill_homework.py  # 批量填充作业
│   └── debug_tools.py        # 调试工具
│
├── tests/                    # 测试代码
│   ├── __init__.py
│   └── test_auth.py          # 认证测试
│
├── docs/                     # 文档
│   └── api_reference.md      # API参考文档
│
├── .cursor/                  # Cursor编辑器配置
│   └── rules/                # 代码规范规则
│
├── requirements.txt          # 项目依赖
├── README.md                 # 项目说明
└── main.py                   # 主入口文件
```

## 模块职责

### core 模块

核心功能模块，包含系统的主要业务逻辑：

- **auth.py**: 负责用户认证、会话管理
- **course.py**: 处理课程相关操作
- **homework.py**: 处理作业相关操作
- **utils.py**: 提供通用工具函数

### api 模块

定义与外部系统交互的API接口：

- **chaoxing_api.py**: 封装超星学习通API的调用

### models 模块

定义数据模型和数据结构：

- **course.py**: 课程相关的数据模型
- **homework.py**: 作业相关的数据模型

### config 模块

管理系统配置：

- **settings.py**: 全局设置，如API地址、超时设置等

### scripts 模块

独立的脚本工具：

- **batch_fill_homework.py**: 批量填充作业的脚本
- **debug_tools.py**: 调试工具脚本

## 代码组织原则

1. **关注点分离**: 将不同功能放在不同模块中
2. **单一职责**: 每个模块、类和函数应该只有一个职责
3. **依赖注入**: 通过参数传递依赖，而不是在函数内部创建
4. **配置与代码分离**: 将配置信息从代码中分离出来
5. **测试驱动**: 编写测试来验证代码功能
