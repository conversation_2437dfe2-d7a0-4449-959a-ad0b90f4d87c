# 超星学习通登录加密机制说明

## 加密原理

超星学习通的登录接口使用AES加密对用户名和密码进行加密，然后将加密后的数据发送到服务器进行验证。这种方式可以防止明文传输用户凭据，提高账号安全性。

## 加密实现细节

### 1. 加密算法

使用的是AES（Advanced Encryption Standard，高级加密标准）算法，具体模式为CBC（Cipher Block Chaining，密码块链接）模式。

### 2. 加密密钥

加密使用固定密钥：`u2oh6Vu^HWe4_AES`，该密钥同时作为AES加密的初始化向量(IV)。

### 3. 加密流程

1. 将明文（用户名/密码）转换为UTF-8编码的字节
2. 对字节进行PKCS7填充，确保数据长度是16字节的倍数
3. 使用AES-CBC模式加密数据
4. 将加密后的数据进行Base64编码，得到最终的密文

### 4. 代码实现

核心加密类`AESCipher`的实现如下：

```python
class AESCipher:
    def __init__(self):
        self.key = str(gc.AESKey).encode("utf8")
        self.iv = str(gc.AESKey).encode("utf8")

    def encrypt(self, plaintext: str):
        ciphertext = b""
        cbc = pyaes.AESModeOfOperationCBC(self.key, self.iv)
        plaintext = plaintext.encode("utf-8")
        blocks = split_to_data_blocks(pkcs7_padding(plaintext))
        for b in blocks:
            ciphertext = ciphertext + cbc.encrypt(b)
        base64_text = base64.b64encode(ciphertext).decode("utf8")
        return base64_text
```

## 登录流程

1. 创建一个`AESCipher`实例
2. 分别加密用户名和密码
3. 构造登录请求数据，包含加密后的用户名和密码
4. 发送POST请求到登录接口
5. 服务器验证成功后，保存返回的cookies用于后续请求

```python
def login(self):
    _session = requests.session()
    _session.verify = False
    _url = "https://passport2.chaoxing.com/fanyalogin"
    _data = {
        "fid": "-1",
        "uname": self.cipher.encrypt(self.account.username),
        "password": self.cipher.encrypt(self.account.password),
        "refer": "https%3A%2F%2Fi.chaoxing.com",
        "t": True,
        "forbidotherlogin": 0,
        "validate": "",
        "doubleFactorLogin": 0,
        "independentId": 0,
    }
    resp = _session.post(_url, headers=gc.HEADERS, data=_data)
    if resp and resp.json()["status"] == True:
        save_cookies(_session)
        return {"status": True, "msg": "登录成功"}
    else:
        return {"status": False, "msg": str(resp.json()["msg2"])}
```

## Cookie持久化

登录成功后，系统会保存会话cookies到本地文件，后续请求可以直接使用这些cookies进行身份验证，无需重新登录。

```python
def save_cookies(_session):
    with open(gc.COOKIES_PATH, "wb") as f:
        pickle.dump(_session.cookies, f)

def use_cookies():
    if os.path.exists(gc.COOKIES_PATH):
        with open(gc.COOKIES_PATH, "rb") as f:
            _cookies = pickle.load(f)
        return _cookies
```

## 如何在其他项目中使用

要在其他项目中使用此登录机制，需要复制以下文件：

1. `api/cipher.py` - 包含AES加密实现
2. `api/config.py` - 包含AES密钥和其他配置
3. `api/cookies.py` - 包含cookie保存和读取功能

同时需要安装以下依赖：
- `pyaes` - 用于AES加密
- `requests` - 用于HTTP请求

### 最简实现示例

```python
import base64
import pyaes
import requests
import pickle
import os.path

# AES密钥
AES_KEY = "u2oh6Vu^HWe4_AES"
COOKIES_PATH = "cookies.txt"

# PKCS7填充
def pkcs7_padding(s, block_size=16):
    bs = block_size
    return s + (bs - len(s) % bs) * chr(bs - len(s) % bs).encode()

# 分割数据块
def split_to_data_blocks(byte_str, block_size=16):
    length = len(byte_str)
    j, y = divmod(length, block_size)
    blocks = []
    shenyu = j * block_size
    for i in range(j):
        start = i * block_size
        end = (i + 1) * block_size
        blocks.append(byte_str[start:end])
    stext = byte_str[shenyu:]
    if stext:
        blocks.append(stext)
    return blocks

# AES加密类
class AESCipher:
    def __init__(self):
        self.key = AES_KEY.encode("utf8")
        self.iv = AES_KEY.encode("utf8")

    def encrypt(self, plaintext: str):
        ciphertext = b""
        cbc = pyaes.AESModeOfOperationCBC(self.key, self.iv)
        plaintext = plaintext.encode("utf-8")
        blocks = split_to_data_blocks(pkcs7_padding(plaintext))
        for b in blocks:
            ciphertext = ciphertext + cbc.encrypt(b)
        base64_text = base64.b64encode(ciphertext).decode("utf8")
        return base64_text

# 保存cookies
def save_cookies(_session):
    with open(COOKIES_PATH, "wb") as f:
        pickle.dump(_session.cookies, f)

# 使用cookies
def use_cookies():
    if os.path.exists(COOKIES_PATH):
        with open(COOKIES_PATH, "rb") as f:
            _cookies = pickle.load(f)
        return _cookies
    return None

# 登录函数
def login(username, password):
    _session = requests.session()
    _session.verify = False
    _url = "https://passport2.chaoxing.com/fanyalogin"
    
    cipher = AESCipher()
    _data = {
        "fid": "-1",
        "uname": cipher.encrypt(username),
        "password": cipher.encrypt(password),
        "refer": "https%3A%2F%2Fi.chaoxing.com",
        "t": True,
        "forbidotherlogin": 0,
        "validate": "",
        "doubleFactorLogin": 0,
        "independentId": 0,
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }
    
    resp = _session.post(_url, headers=headers, data=_data)
    if resp and resp.json()["status"] == True:
        save_cookies(_session)
        return {"status": True, "msg": "登录成功", "session": _session}
    else:
        return {"status": False, "msg": str(resp.json()["msg2"])}

# 使用示例
if __name__ == "__main__":
    # 尝试使用已保存的cookies
    cookies = use_cookies()
    if cookies:
        session = requests.session()
        session.cookies.update(cookies)
        # 使用session进行后续请求...
    else:
        # 重新登录
        result = login("你的手机号", "你的密码")
        if result["status"]:
            session = result["session"]
            # 使用session进行后续请求...
        else:
            print(f"登录失败: {result['msg']}")
```

## 注意事项

1. 超星可能会更改加密密钥或登录接口，需要及时更新
2. 此实现依赖于第三方库`pyaes`，请确保安装
3. 登录成功后保存的cookies有一定的有效期，过期后需要重新登录
4. 请合法使用此代码，仅用于学习和研究目的 