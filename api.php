<?php
include('confing/common.php');
$ckxz = $DB->get_row("select settings,api_ck,api_xd,api_proportion from qingka_wangke_config");
$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;
@header('Content-Type: application/json; charset=UTF-8');
if ($conf['settings'] != 1) {
	exit('{"code":-1,"msg":"API功能已关闭，请联系管理员！"}');
} else {
	switch ($act) {
		case 'verify': //用户登录验证
			$uid = trim(strip_tags(daddslashes($_POST['uid'])));
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			
			if ($uid == '' || $key == '') {
				exit(json_encode(['status' => 'error', 'message' => '用户ID或密钥不能为空']));
			}
			
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if (!$row) {
				// 用户不存在
				wlog(0, "登录验证", "用户{$uid}不存在", 0);
				exit(json_encode(['status' => 'error', 'message' => '用户ID不存在']));
			} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "登录验证", "密钥错误", 0);
				exit(json_encode(['status' => 'error', 'message' => '密钥错误']));
			} else {
				// 验证成功，记录日志
				wlog($uid, "登录验证", "登录成功，欢迎{$row['name']}", 0);
				exit(json_encode([
					'status' => 'success',
					'message' => '登录成功',
					'user' => [
						'uid' => $uid,
						'name' => $row['name']
					]
				]));
			}
			break;
		case 'getmoney'://查询当前余额
			$uid = trim(strip_tags(daddslashes($_POST['uid'])));
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			if ($uid == '' || $key == '') {
				exit('{"code":0,"msg":"所有项目不能为空"}');
			}
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				$result = array(
					'code' => 1,
					'msg' => '查询成功',
					'money' => $row['money']
				);
				exit(json_encode($result));
			}
			break;
		// 版本更新后台
		case 'getupdateinfo':
			$uid = trim(strip_tags(addslashes($_POST['uid'])));
			$key = trim(strip_tags(addslashes($_POST['key'])));

			if ($uid == '' || $key == '') {
				exit('{"code":0,"msg":"小本生意，请根据要求来可以么"}');
			}

			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");

			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				$new = isset($conf['banbennr']) ? $conf['banbennr'] : '默认内容';
				$version = isset($conf['banben']) ? $conf['banben'] : '当前版本';
				$url = isset($conf['banbenurl']) ? $conf['banbenurl'] : '安装包地址';


				$latestVersion = [
					"code" => "1",
					"version" => $version,
					"url" => "$url",
					"new" => $new
				];

				$result = array(
					'code' => 1,
					'msg' => '查询成功',
					'data' => $latestVersion
				);
				exit(json_encode($result));
			}
			break;
		// 版本更新
		case 'updateversion':
			$uid = trim(strip_tags(addslashes($_POST['uid'])));
			$key = trim(strip_tags(addslashes($_POST['key'])));
			$newVersion = trim(strip_tags(addslashes($_POST['newVersion'])));

			if ($uid == '' || $key == '' || $newVersion == '') {
				exit('{"code":0,"msg":"参数错误，请根据要求来可以么"}');
			}

			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");

			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				// 查询当前的k值
				$configRow = $DB->get_row("SELECT k FROM qingka_wangke_config WHERE v='banben'");

				if (!$configRow) {
					exit('{"code":-3,"msg":"配置信息未找到"}');
				}

				// 更新数据库中的版本信息
				$updateResult = $DB->query("UPDATE `qingka_wangke_config` SET k='{$newVersion}' WHERE v='banben'");

				if ($updateResult) {
					$result = array("code" => 1, "msg" => "版本更新成功");
				} else {
					$result = array("code" => -3, "msg" => "版本更新失败");
				}

				exit(json_encode($result));
			}
			break;
		case 'verify_key': // 验证key是否有效
			$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			
			if (empty($key)) {
				exit(json_encode(array("code" => 0, "msg" => "请提供要验证的Key")));
			}
			
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if ($row) {
				// 记录验证成功日志
				wlog($row['uid'], "Key验证", "Key验证成功，欢迎{$row['name']}", 0);
				exit(json_encode(array("code" => 1, "msg" => "Key验证成功")));
			} else {
				// 记录验证失败日志
				wlog(0, "Key验证", "Key验证失败：{$key}", 0);
				exit(json_encode(array("code" => 0, "msg" => "Key不存在，请前往蜜雪激活")));
			}
			break;
		case 'query': // 题库查询
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			$question = trim(strip_tags(daddslashes($_POST['question'])));
			
			// 调试日志
			error_log("Debug - 接收到的参数:");
			error_log("key: " . $key);
			error_log("question: " . $question);
			
			// 验证参数
			if ($key == '' || $question == '') {
				error_log("Debug - 参数验证失败");
				exit('{"code":0,"msg":"所有项目不能为空"}');
			}

			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 清理问题文本，移除特殊字符以提高匹配率
			$cleaned_question = $question;
			$cleaned_question = preg_replace('/\s+/', ' ', $cleaned_question); // 合并多个空格为一个
			$cleaned_question = trim($cleaned_question); // 移除首尾空格
			$cleaned_question = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ','), '', $cleaned_question); // 移除各种括号和标点符号
			
			error_log("Debug - 清理后的问题: " . $cleaned_question);
			
			// 查询题库 - 使用多种查询方式
			$original_question = $DB->escape($question); // 原始问题（SQL注入防护）
			$cleaned_question = $DB->escape($cleaned_question); // 清理后的问题（SQL注入防护）
			
			// 1. 先尝试完全匹配原始问题
			$sql = "SELECT * FROM question WHERE question = '{$original_question}' LIMIT 1";
			error_log("Debug - SQL精确查询(原始): " . $sql);
			$q = $DB->get_row($sql);
			
			// 2. 如果未匹配，尝试完全匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - SQL精确查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 3. 如果仍未匹配，尝试题库表中的问题去除特殊字符后与清理后的问题进行比较
			if (!$q) {
				$sql = "SELECT * FROM question WHERE REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, '(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', '') = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - SQL特殊字符对比查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 4. 如果仍未匹配，尝试模糊匹配原始问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$original_question}%' LIMIT 1";
				error_log("Debug - SQL模糊查询(原始): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 5. 如果仍未匹配，尝试模糊匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_question}%' LIMIT 1";
				error_log("Debug - SQL模糊查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 6. 如果仍未匹配，尝试将清理后问题作为题库的子串或反向查询
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE 
				        question LIKE '%{$cleaned_question}%' OR 
				        '{$cleaned_question}' LIKE CONCAT('%', REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, '(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), '%') 
				        LIMIT 1";
				error_log("Debug - SQL双向子串查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 7. 尝试关键词匹配 - 提取较长的词语作为关键词进行搜索
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				// 将问题拆分为关键词
				$keywords = preg_split('/\s+/', $cleaned_question);
				$long_keywords = array();
				
				// 筛选长度大于3的关键词
				foreach ($keywords as $keyword) {
					if (mb_strlen($keyword, 'UTF-8') > 3) {
						$long_keywords[] = $DB->escape($keyword);
					}
				}
				
				// 如果没有大于3的关键词，就尝试使用所有的关键词
				if (empty($long_keywords) && !empty($keywords)) {
					foreach ($keywords as $keyword) {
						if (mb_strlen($keyword, 'UTF-8') > 1) {
							$long_keywords[] = $DB->escape($keyword);
						}
					}
				}
				
				// 如果有足够的关键词，使用它们进行查询
				if (!empty($long_keywords)) {
					$keyword_conditions = array();
					foreach ($long_keywords as $keyword) {
						$keyword_conditions[] = "question LIKE '%{$keyword}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $keyword_conditions) . " LIMIT 1";
					error_log("Debug - SQL关键词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			error_log("Debug - 查询结果: " . print_r($q, true));
			
			// 计算相似度并记录搜题历史
			$time = date('Y-m-d H:i:s');
			if ($q) {
				// 计算相似度
				$accuracy = 0;
				similar_text($question, $q['question'], $accuracy);
				$accuracy = round($accuracy, 2);
				
				// 记录搜题历史（找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '{$q['answer']}', '{$accuracy}', '{$time}')");
				
				// 检查用户积分是否足够
				if($row['money'] < 1) {
					exit('{"code":-3,"msg":"积分不足，无法进行搜索，请充值"}');
				}
				
				// 扣除用户积分（只在查询成功时扣除）
				$DB->query("UPDATE qingka_wangke_user SET money=money-1 WHERE uid='$uid'");
				// 记录积分扣除日志
				wlog($uid, "积分消费", "API题库搜索消费1积分", -1);
				
				$result = array(
					"code" => 1,
					"data" => array(
						"question" => $q['question'],
						"answer" => $q['answer'],
						"times" => 999
					),
					"message" => "请求成功"
				);
				// 记录操作日志
				wlog($uid, "蜜雪程序查询", "查询题目：{$question} - 查询成功", 0);
					} else {
				// 记录搜题历史（未找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '0', '未找到匹配题目', '未找到答案', '0', '{$time}')");
				
				// 未找到答案
				$result = array(
					"code" => 0,
					"data" => array(
						"question" => "未找到答案！",
						"answer" => "很抱歉, 题目搜索不到。",
						"times" => 999
					),
					"message" => "验证成功，但是没有找到答案"
				);
				// 记录未找到答案的日志
				wlog($uid, "蜜雪程序查询", "查询题目：{$question} - 未找到答案", 0);
			}
			
						exit(json_encode($result));
			break;
			
		case 'mcx': // 柠檬题库兼容接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取POST请求体中的JSON数据
			$json_data = file_get_contents('php://input');
			$post_data = json_decode($json_data, true);
			
			// 从POST JSON或GET参数中获取问题
			$question = '';
			$options = '';
			$type = '6'; // 默认类型
			$uid_param = ''; // 用户传入的uid参数（仅记录用）
			
			if ($post_data) {
				// 从JSON POST中获取数据
				$question = isset($post_data['question']) ? trim(strip_tags(daddslashes($post_data['question']))) : '';
				$options = isset($post_data['options']) ? trim(strip_tags(daddslashes($post_data['options']))) : '';
				$type = isset($post_data['type']) ? trim(strip_tags(daddslashes($post_data['type']))) : '6';
				$uid_param = isset($post_data['uid']) ? trim(strip_tags(daddslashes($post_data['uid']))) : '';
					} else {
				// 从常规POST或GET中获取数据
				$question = isset($_POST['question']) ? trim(strip_tags(daddslashes($_POST['question']))) : (isset($_GET['question']) ? trim(strip_tags(daddslashes($_GET['question']))) : '');
				$options = isset($_POST['options']) ? trim(strip_tags(daddslashes($_POST['options']))) : (isset($_GET['options']) ? trim(strip_tags(daddslashes($_GET['options']))) : '');
				$type = isset($_POST['type']) ? trim(strip_tags(daddslashes($_POST['type']))) : (isset($_GET['type']) ? trim(strip_tags(daddslashes($_GET['type']))) : '6');
				$uid_param = isset($_POST['uid']) ? trim(strip_tags(daddslashes($_POST['uid']))) : (isset($_GET['uid']) ? trim(strip_tags(daddslashes($_GET['uid']))) : '');
			}
			
			// 调试日志
			error_log("Debug - MCX接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("question: " . $question);
			error_log("options: " . $options);
			error_log("type: " . $type);
			error_log("uid_param: " . $uid_param);
			
			// 验证参数
			if ($key == '' || $question == '') {
				error_log("Debug - MCX参数验证失败");
				exit(json_encode(array("code" => 1001, "msg" => "参数不完整", "data" => array("answer" => ""))));
			}
			
			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				exit(json_encode(array("code" => 1001, "msg" => "密钥错误", "data" => array("answer" => ""))));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 清理问题文本，移除特殊字符以提高匹配率
			$cleaned_question = $question;
			$cleaned_question = preg_replace('/\s+/', ' ', $cleaned_question); // 合并多个空格为一个
			$cleaned_question = trim($cleaned_question); // 移除首尾空格
			$cleaned_question = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ','), '', $cleaned_question); // 移除各种括号和标点符号
			
			error_log("Debug - MCX 清理后的问题: " . $cleaned_question);
			
			// 查询题库 - 使用多种查询方式
			$original_question = $DB->escape($question); // 原始问题（SQL注入防护）
			$cleaned_question = $DB->escape($cleaned_question); // 清理后的问题（SQL注入防护）
			
			// 1. 先尝试完全匹配原始问题
			$sql = "SELECT * FROM question WHERE question = '{$original_question}' LIMIT 1";
			error_log("Debug - MCX SQL精确查询(原始): " . $sql);
			$q = $DB->get_row($sql);
			
			// 2. 如果未匹配，尝试完全匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - MCX SQL精确查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 3. 如果仍未匹配，尝试题库表中的问题去除特殊字符后与清理后的问题进行比较
			if (!$q) {
				$sql = "SELECT * FROM question WHERE REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, '(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', '') = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - MCX SQL特殊字符对比查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 4. 如果仍未匹配，尝试模糊匹配原始问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$original_question}%' LIMIT 1";
				error_log("Debug - MCX SQL模糊查询(原始): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 5. 如果仍未匹配，尝试模糊匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_question}%' LIMIT 1";
				error_log("Debug - MCX SQL模糊查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 6. 如果仍未匹配，尝试将清理后问题作为题库的子串或反向查询
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE 
				        question LIKE '%{$cleaned_question}%' OR 
				        '{$cleaned_question}' LIKE CONCAT('%', REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, '(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), '%') 
				        LIMIT 1";
				error_log("Debug - MCX SQL双向子串查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 7. 尝试关键词匹配 - 提取较长的词语作为关键词进行搜索
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				// 将问题拆分为关键词
				$keywords = preg_split('/\s+/', $cleaned_question);
				$long_keywords = array();
				
				// 筛选长度大于3的关键词
				foreach ($keywords as $keyword) {
					if (mb_strlen($keyword, 'UTF-8') > 3) {
						$long_keywords[] = $DB->escape($keyword);
					}
				}
				
				// 如果没有大于3的关键词，就尝试使用所有的关键词
				if (empty($long_keywords) && !empty($keywords)) {
					foreach ($keywords as $keyword) {
						if (mb_strlen($keyword, 'UTF-8') > 1) {
							$long_keywords[] = $DB->escape($keyword);
						}
					}
				}
				
				// 如果有足够的关键词，使用它们进行查询
				if (!empty($long_keywords)) {
					$keyword_conditions = array();
					foreach ($long_keywords as $keyword) {
						$keyword_conditions[] = "question LIKE '%{$keyword}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $keyword_conditions) . " LIMIT 1";
					error_log("Debug - MCX SQL关键词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			error_log("Debug - MCX查询结果: " . print_r($q, true));
			
			// 计算相似度并记录搜题历史
			$time = date('Y-m-d H:i:s');
			if ($q) {
				// 计算相似度
				$accuracy = 0;
				similar_text($question, $q['question'], $accuracy);
				$accuracy = round($accuracy, 2);
				
				// 记录搜题历史（找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '{$q['answer']}', '{$accuracy}', '{$time}')");
				
				// 检查用户积分是否足够
				if($row['money'] < 1) {
					exit(json_encode(array("code" => 1001, "msg" => "积分不足，无法进行搜索，请充值", "data" => array("answer" => ""))));
				}
				
				// 扣除用户积分（只在查询成功时扣除）
				$DB->query("UPDATE qingka_wangke_user SET money=money-1 WHERE uid='$uid'");
				// 记录积分扣除日志
				wlog($uid, "积分消费", "OCS搜索消费1积分", -1);
				
				// 按照柠檬题库的格式返回结果
				$result = array(
					"code" => 1000,
					"msg" => "success",
					"data" => array(
						"answer" => $q['answer']
					)
				);
				// 记录操作日志
				wlog($uid, "OCS查询", "查询题目：{$question} - 查询成功", 0);
				} else {
				// 记录搜题历史（未找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '0', '未找到匹配题目', '未找到答案', '0', '{$time}')");
				
				// 未找到答案，按照柠檬题库的格式返回结果
				$result = array(
					"code" => 1001,
					"msg" => "未找到答案",
					"data" => array(
						"answer" => ""
					)
				);
				// 记录未找到答案的日志
				wlog($uid, "OCS查询", "查询题目：{$question} - 未找到答案", 0);
			}
			
			exit(json_encode($result));
			break;

		case 'xxt': // 油猴脚本专用接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取问题和题目类型
			$question = isset($_POST['question']) ? trim(strip_tags(daddslashes($_POST['question']))) : (isset($_GET['question']) ? trim(strip_tags(daddslashes($_GET['question']))) : '');
			$type = isset($_POST['type']) ? trim(strip_tags(daddslashes($_POST['type']))) : (isset($_GET['type']) ? trim(strip_tags(daddslashes($_GET['type']))) : '0');
			$options = isset($_POST['options']) ? trim(strip_tags(daddslashes($_POST['options']))) : (isset($_GET['options']) ? trim(strip_tags(daddslashes($_GET['options']))) : '');
			
			// 调试日志
			error_log("Debug - XXT接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("question: " . $question);
			error_log("type: " . $type);
			error_log("options: " . $options);
			
			// 验证参数
			if ($question == '') {
				error_log("Debug - XXT参数验证失败");
				exit(json_encode(array("code" => 0, "msg" => "题目不能为空", "data" => array("answer" => ""))));
			}
			
			// 用户ID，如果有key则验证，没有则使用默认值
			$uid = 0;
			$need_pay = false;
			
			// 如果提供了key，则验证key
			if (!empty($key)) {
				$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
				if ($row) {
					$uid = $row['uid'];
					// 检查用户积分是否足够
					if($row['money'] < 1) {
						exit(json_encode(array("code" => 0, "msg" => "积分不足，无法进行搜索，请充值", "data" => array("answer" => ""))));
					}
					$need_pay = true;
				} else {
					// key错误，返回错误信息并停止查询
					error_log("Debug - XXT key验证失败");
					exit(json_encode(array("code" => 0, "msg" => "Key验证失败，请检查您的Key是否正确", "data" => array("answer" => ""))));
				}
			} else {
				// 未提供key，返回错误信息
				error_log("Debug - XXT 未提供key");
				exit(json_encode(array("code" => 0, "msg" => "请提供有效的Key", "data" => array("answer" => ""))));
			}
			
			// 清理问题文本，移除特殊字符以提高匹配率
			$cleaned_question = $question;
			$cleaned_question = preg_replace('/\s+/', ' ', $cleaned_question); // 合并多个空格为一个
			$cleaned_question = trim($cleaned_question); // 移除首尾空格
			$cleaned_question = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ','), '', $cleaned_question); // 移除各种括号和标点符号
			
			// 如果问题过长，可能是包含了选项，尝试提取主要问题部分
			if (mb_strlen($cleaned_question, 'UTF-8') > 30) {
				// 尝试找到问题的结束标志（如问号）
				$pos = mb_strpos($cleaned_question, '?', 0, 'UTF-8');
				if ($pos !== false && $pos > 10) {
					$short_question = mb_substr($cleaned_question, 0, $pos + 1, 'UTF-8');
					error_log("Debug - XXT 提取主要问题: " . $short_question);
					// 如果提取的问题足够长，使用提取的问题
					if (mb_strlen($short_question, 'UTF-8') > 10) {
						$cleaned_question = $short_question;
					}
				}
				
				// 也尝试查找中文问号
				$pos = mb_strpos($cleaned_question, '？', 0, 'UTF-8');
				if ($pos !== false && $pos > 10) {
					$short_question = mb_substr($cleaned_question, 0, $pos + 1, 'UTF-8');
					error_log("Debug - XXT 提取主要问题(中文问号): " . $short_question);
					// 如果提取的问题足够长，使用提取的问题
					if (mb_strlen($short_question, 'UTF-8') > 10) {
						$cleaned_question = $short_question;
					}
				}
			}
			
			error_log("Debug - XXT 清理后的问题: " . $cleaned_question);
			
			// 查询题库 - 使用更全面的查询策略
			$original_question = $DB->escape($question); // 原始问题（SQL注入防护）
			$cleaned_question = $DB->escape($cleaned_question); // 清理后的问题（SQL注入防护）
			
			// 1. 先尝试完全匹配原始问题
			$sql = "SELECT * FROM question WHERE question = '{$original_question}' LIMIT 1";
			error_log("Debug - XXT SQL精确查询(原始): " . $sql);
			$q = $DB->get_row($sql);
			
			// 2. 如果未匹配，尝试完全匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - XXT SQL精确查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 3. 如果仍未匹配，尝试题库表中的问题去除特殊字符后与清理后的问题进行比较
			if (!$q) {
				$sql = "SELECT * FROM question WHERE REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, '(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', '') = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - XXT SQL特殊字符对比查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 4. 如果仍未匹配，尝试模糊匹配原始问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$original_question}%' LIMIT 1";
				error_log("Debug - XXT SQL模糊查询(原始): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 5. 如果仍未匹配，尝试模糊匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_question}%' LIMIT 1";
				error_log("Debug - XXT SQL模糊查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 6. 如果仍未匹配，尝试双向子串查询（题库中的题目是查询问题的子串，或者查询问题是题库中题目的子串）
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE 
				        question LIKE '%{$cleaned_question}%' OR 
				        '{$cleaned_question}' LIKE CONCAT('%', REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, '(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), '%') 
				        LIMIT 1";
				error_log("Debug - XXT SQL双向子串查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 7. 如果仍未匹配，尝试关键词匹配 - 提取较长的词语作为关键词进行搜索
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				// 将问题拆分为关键词
				$keywords = preg_split('/\s+/', $cleaned_question);
				$long_keywords = array();
				
				// 筛选长度大于3的关键词
				foreach ($keywords as $keyword) {
					if (mb_strlen($keyword, 'UTF-8') > 3) {
						$long_keywords[] = $DB->escape($keyword);
					}
				}
				
				// 如果没有大于3的关键词，就尝试使用所有的关键词
				if (empty($long_keywords) && !empty($keywords)) {
					foreach ($keywords as $keyword) {
						if (mb_strlen($keyword, 'UTF-8') > 1) {
							$long_keywords[] = $DB->escape($keyword);
						}
					}
				}
				
				// 如果有足够的关键词，使用它们进行查询
				if (!empty($long_keywords)) {
					$keyword_conditions = array();
					foreach ($long_keywords as $keyword) {
						$keyword_conditions[] = "question LIKE '%{$keyword}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $keyword_conditions) . " LIMIT 1";
					error_log("Debug - XXT SQL关键词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			error_log("Debug - XXT查询结果: " . print_r($q, true));
			
			// 计算相似度并记录搜题历史
			$time = date('Y-m-d H:i:s');
			if ($q) {
				// 计算相似度
				$accuracy = 0;
				similar_text($question, $q['question'], $accuracy);
				$accuracy = round($accuracy, 2);
				
				// 放宽相似度要求，降低最小匹配长度
				$min_match_length = 3; // 原先是5，现在改为3
				$max_common_length = 0;
				
				// 查找最长公共子串（仅用于记录，不再作为拒绝的依据）
				for ($i = 0; $i < mb_strlen($question, 'UTF-8'); $i++) {
					for ($j = 0; $j < mb_strlen($q['question'], 'UTF-8'); $j++) {
						$k = 0;
						while (
							($i + $k) < mb_strlen($question, 'UTF-8') && 
							($j + $k) < mb_strlen($q['question'], 'UTF-8') && 
							mb_substr($question, $i + $k, 1, 'UTF-8') === mb_substr($q['question'], $j + $k, 1, 'UTF-8')
						) {
							$k++;
						}
						
						if ($k > $max_common_length) {
							$max_common_length = $k;
							$common_substring = mb_substr($question, $i, $k, 'UTF-8');
						}
					}
				}
				
				error_log("Debug - XXT 最长公共子串: " . (isset($common_substring) ? $common_substring : "无") . " (长度: " . $max_common_length . ")");
				
				// 如果有用户ID，记录搜题历史
				if ($uid > 0) {
					// 记录搜题历史（找到答案）
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '{$q['answer']}', '{$accuracy}', '{$time}')");
					
					// 如果需要扣费，扣除积分并记录日志
					if ($need_pay) {
						$DB->query("UPDATE qingka_wangke_user SET money=money-1 WHERE uid='$uid'");
						wlog($uid, "蜜雪脚本查询", "查询题目：{$question} - 查询成功，消费1积分", -1);
					}
				}
				
				// 返回结果，兼容油猴脚本格式
				$result = array(
					"code" => 200,
					"msg" => "success",
					"data" => array(
						"question" => $q['question'],
						"answer" => $q['answer'],
						"times" => 999
					)
				);
				} else {
				// 如果有用户ID，记录搜题历史
				if ($uid > 0) {
					// 记录搜题历史（未找到答案）
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '0', '未找到匹配题目', '未找到答案', '0', '{$time}')");
					
					// 记录未找到答案的日志（不扣除积分）
					wlog($uid, "蜜雪脚本查询", "查询题目：{$question} - 未找到答案", 0);
				}
				
				// 未找到答案，返回错误信息
				$result = array(
					"code" => 0,
					"msg" => "未找到答案，请重新描述问题或联系客服添加题库",
					"data" => array(
						"question" => "未找到答案！",
						"answer" => "",
						"times" => 999
					)
				);
			}
			
				exit(json_encode($result));
			break;
			
		case 'ai': // AI 接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取POST请求体中的JSON数据
			$json_data = file_get_contents('php://input');
			$post_data = json_decode($json_data, true);
			
			// 从POST JSON或GET/POST参数中获取问题和模型
			$question = '';
			$model = 'net-gpt-3.5-turbo-16k'; // 默认模型
			
			if ($post_data) {
				// 从JSON获取模型
				if (isset($post_data['model'])) {
					$model = trim(strip_tags(daddslashes($post_data['model'])));
				}
				
				// 从JSON获取问题
				if (isset($post_data['messages']) && is_array($post_data['messages'])) {
					foreach ($post_data['messages'] as $message) {
						if (isset($message['role']) && $message['role'] === 'user') {
							$question = trim(strip_tags(daddslashes($message['content'])));
							break;
						}
					}
				}
			}
			
			// 调试日志
			error_log("Debug - AI接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("model: " . $model);
			error_log("question: " . $question);
			
			// 验证参数
			if ($question == '') {
				error_log("Debug - AI参数验证失败");
				exit(json_encode(array("code" => 1001, "msg" => "问题不能为空", "data" => array("answer" => ""))));
			}
			
			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				exit(json_encode(array("code" => 1001, "msg" => "密钥错误", "data" => array("answer" => ""))));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 设置不同模型的积分消费
			$model_cost = array(
				'gpt-3.5-turbo-16k' => 1,
				'deepseek-chat' => 2,
				'gpt-4o-mini' => 2,
				'gpt-4' => 10,
				'glm-4-flash' => 2
			);
			
			// 根据模型确定需要的积分
			$required_points = isset($model_cost[$model]) ? $model_cost[$model] : 1;
			
			// 检查用户积分是否足够
			if($row['money'] < $required_points) {
				exit(json_encode(array("code" => 1001, "msg" => "积分不足，无法进行搜索，请充值。当前模型 {$model} 需要 {$required_points} 积分", "data" => array("answer" => ""))));
			}
			
			// 根据客户端传递的模型标识符映射到实际API使用的模型名称
			$model_mapping = array(
				'gpt-3.5-turbo-16k' => 'gpt-3.5-turbo-16k',
				'deepseek-chat' => 'deepseek-chat',
				'gpt-4o-mini' => 'gpt-4o-mini',
				'gpt-4' => 'gpt-4',
				'glm-4-flash' => 'glm-4-flash'
			);
			
			// 获取实际使用的模型名称，如果映射中没有则使用默认模型
			$api_model = isset($model_mapping[$model]) ? $model_mapping[$model] : 'gpt-3.5-turbo-16k';
			
			// 记录映射情况
			error_log("模型映射: 客户端模型 {$model} 映射到 API模型 {$api_model}");
			
			// 调用外部AI API
			$ai_data = array(
				"model" => $api_model,
				"messages" => array(
					array(
						"role" => "system",
						"search" => "true",
						"content" => "你是一个专业的答题助手。请根据题目类型给出准确的答案。回答要求：1. 选择题：只返回答案加：选项字母，如 'A' 或 'ABC' 答案：A,并介绍为什么选这个选项 2.判断题：只返回 '正确' 或 '错误' 答案：正确，并介绍为什么选这个答案 3. 填空题：直接返回答案：答案，并介绍为什么选这个答案 4. 其他题型：返回简洁的答案加上为什么选这个答案"
					),
					array(
						"role" => "user",
						"content" => $question
					)
				)
			);
			
			// 记录请求日志
			wlog($uid, "AI助手", "准备请求模型: {$api_model}, 问题: {$question}", 0);
			
			// 记录实际发送的请求体，用于调试
			error_log("AI API请求数据: " . json_encode($ai_data, JSON_UNESCAPED_UNICODE));
			
			$ch = curl_init('https://api.ephone.ai/v1/chat/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ai_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Accept: application/json',
				'Authorization: sk-YQSpuaSrMqVxGviMB07zYwOAkm90ZK48mffK852a2r8iEPgi',
				'search: true',
				'Content-Type: application/json'
			));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 禁用 SSL 证书验证
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 禁用主机名验证
			
			// 执行请求前记录
			error_log("准备执行 cURL 请求到 https://api.ephone.ai/v1/chat/completions");
			
			$response = curl_exec($ch);
			$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			$curl_error = curl_error($ch);
			$curl_errno = curl_errno($ch);
			curl_close($ch);
			
			// 记录API请求结果
			error_log("AI API响应: HTTP码 " . $http_code . ", cURL错误码: " . $curl_errno . ", cURL错误: " . $curl_error);
			if ($response) {
				error_log("API响应内容: " . substr($response, 0, 1000));
				} else {
				error_log("API没有返回响应内容");
			}
			
			if ($http_code == 200) {
				$ai_result = json_decode($response, true);
				if (isset($ai_result['choices'][0]['message']['content'])) {
					// 扣除用户积分
					$DB->query("UPDATE qingka_wangke_user SET money=money-{$required_points} WHERE uid='$uid'");
					// 记录积分扣除日志
					wlog($uid, "AI助手", "AI问答消费{$required_points}积分，使用模型：{$model}", -$required_points);
					
					// 记录成功的AI请求
					$time = date('Y-m-d H:i:s');
					$answer = $ai_result['choices'][0]['message']['content'];
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '0', 'AI问答', '{$answer}', '100', '{$time}')");
					
					// 返回结果，格式与 xxt 接口一致
					exit(json_encode(array(
						"code" => 200,
						"msg" => "success",
						"data" => array(
							"answer" => $answer,
							"question" => $question,
							"times" => 999
						)
					)));
				}
			}
			
			// 记录失败的请求
			wlog($uid, "AI助手", "AI问答请求失败: {$question}, HTTP码: {$http_code}, cURL错误: {$curl_error}", 0);
			exit(json_encode(array(
				"code" => 1001,
				"msg" => "AI服务请求失败，请稍后重试",
				"data" => array(
					"answer" => "",
					"question" => $question,
					"times" => 0
				)
			)));
			break;

		case 'autoai': // 自动AI答题接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取POST请求体中的JSON数据
			$json_data = file_get_contents('php://input');
			$post_data = json_decode($json_data, true);
			
			// 从POST JSON或GET/POST参数中获取问题和模型
			$question = '';
			$model = 'gpt-3.5-turbo-16k'; // 默认模型
			
			if ($post_data) {
				// 从JSON获取模型
				if (isset($post_data['model'])) {
					$model = trim(strip_tags(daddslashes($post_data['model'])));
				}
				
				// 从JSON获取问题
				if (isset($post_data['messages']) && is_array($post_data['messages'])) {
					foreach ($post_data['messages'] as $message) {
						if (isset($message['role']) && $message['role'] === 'user') {
							$question = trim(strip_tags(daddslashes($message['content'])));
			break;
						}
					}
				}
			}
			
			// 调试日志
			error_log("Debug - AutoAI接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("model: " . $model);
			error_log("question: " . $question);
			
			// 验证参数
			if ($question == '') {
				error_log("Debug - AutoAI参数验证失败");
				exit(json_encode(array("code" => 1001, "msg" => "问题不能为空", "data" => array("answer" => ""))));
			}
			
			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				exit(json_encode(array("code" => 1001, "msg" => "密钥错误", "data" => array("answer" => ""))));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 设置不同模型的积分消费
			$model_cost = array(
				'gpt-3.5-turbo-16k' => 1,
				'deepseek-chat' => 2,
				'gpt-4o-mini' => 2,
				'gpt-4' => 10,
				'glm-4-flash' => 2
			);
			
			// 根据模型确定需要的积分
			$required_points = isset($model_cost[$model]) ? $model_cost[$model] : 1;
			
			// 检查用户积分是否足够
			if($row['money'] < $required_points) {
				exit(json_encode(array("code" => 1001, "msg" => "积分不足，无法进行搜索，请充值。当前模型 {$model} 需要 {$required_points} 积分", "data" => array("answer" => ""))));
			}
			
			// 根据客户端传递的模型标识符映射到实际API使用的模型名称
			$model_mapping = array(
				'gpt-3.5-turbo-16k' => 'gpt-3.5-turbo-16k',
				'deepseek-chat' => 'deepseek-chat',
				'gpt-4o-mini' => 'gpt-4o-mini',
				'gpt-4' => 'gpt-4',
				'glm-4-flash' => 'glm-4-flash'
			);
			
			// 获取实际使用的模型名称，如果映射中没有则使用默认模型
			$api_model = isset($model_mapping[$model]) ? $model_mapping[$model] : 'gpt-3.5-turbo-16k';
			
			// 记录映射情况
			error_log("AutoAI模型映射: 客户端模型 {$model} 映射到 API模型 {$api_model}");
			
			// 获取系统提示词，如果发送请求中包含则使用请求中的
			$system_prompt = "你是一个专业的答题助手。请根据题目类型给出准确的答案。回答要求：1. 选择题：只返回选项字母，如 'A' 或 'ABC'。2. 判断题：只返回 '正确' 或 '错误'。3. 填空题：直接返回填空内容。4. 其他题型：返回简洁的答案。";
			
			if ($post_data && isset($post_data['messages']) && is_array($post_data['messages'])) {
				foreach ($post_data['messages'] as $message) {
					if (isset($message['role']) && $message['role'] === 'system') {
						$system_prompt = trim(strip_tags(daddslashes($message['content'])));
						break;
					}
				}
			}
			
			// 调用外部AI API
			$ai_data = array(
				"model" => $api_model,
				"messages" => array(
					array(
						"role" => "system",
						"search" => "true",
						"content" => $system_prompt
					),
					array(
						"role" => "user",
						"content" => $question
					)
				),
				"temperature" => 0.2 // 降低随机性，使答案更加确定
			);
			
			// 记录请求日志
			wlog($uid, "自动AI答题", "准备请求模型: {$api_model}, 问题: {$question}", 0);
			
			// 记录实际发送的请求体，用于调试
			error_log("AutoAI API请求数据: " . json_encode($ai_data, JSON_UNESCAPED_UNICODE));
			
			$ch = curl_init('https://api.ephone.ai/v1/chat/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ai_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Accept: application/json',
				'Authorization: sk-YQSpuaSrMqVxGviMB07zYwOAkm90ZK48mffK852a2r8iEPgi',
				'search: true',
				'Content-Type: application/json'
			));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 20); // 设置超时时间为20秒，自动化场景需要快速响应
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 禁用 SSL 证书验证
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 禁用主机名验证
			
			// 执行请求前记录
			error_log("准备执行 AutoAI cURL 请求到 https://api.ephone.ai/v1/chat/completions");
			
			$response = curl_exec($ch);
			$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			$curl_error = curl_error($ch);
			$curl_errno = curl_errno($ch);
			curl_close($ch);
			
			// 记录API请求结果
			error_log("AutoAI API响应: HTTP码 " . $http_code . ", cURL错误码: " . $curl_errno . ", cURL错误: " . $curl_error);
			if ($response) {
				error_log("AutoAI API响应内容: " . substr($response, 0, 1000));
			} else {
				error_log("AutoAI API没有返回响应内容");
			}
			
			if ($http_code == 200) {
				$ai_result = json_decode($response, true);
				if (isset($ai_result['choices'][0]['message']['content'])) {
					// 扣除用户积分
					$DB->query("UPDATE qingka_wangke_user SET money=money-{$required_points} WHERE uid='$uid'");
					// 记录积分扣除日志
					wlog($uid, "自动AI答题", "自动AI答题消费{$required_points}积分，使用模型：{$model}", -$required_points);
					
					// 记录成功的AI请求
					$time = date('Y-m-d H:i:s');
					$answer = $ai_result['choices'][0]['message']['content'];
					
					// 处理答案，移除多余的解释
					$answer_cleaned = $answer;
					if (strpos($answer, '答案：') !== false) {
						$answer_parts = explode('答案：', $answer);
						if (count($answer_parts) > 1) {
							// 获取"答案："后面的内容，可能还需要进一步处理
							$answer_cleaned = trim($answer_parts[1]);
							
							// 如果后面还有解释，只保留第一段
							$explanation_parts = preg_split('/[\r\n]+/', $answer_cleaned, 2);
							if (count($explanation_parts) > 0) {
								$answer_cleaned = trim($explanation_parts[0]);
							}
						}
					}
					
					// 记录搜题历史
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '0', '自动AI答题', '{$answer}', '100', '{$time}')");
					
					// 返回结果，格式与 xxt 接口一致
					exit(json_encode(array(
						"code" => 200,
						"msg" => "success",
						"data" => array(
							"answer" => $answer, // 返回完整答案，包括解释
							"question" => $question,
							"times" => 999
						)
					)));
				}
			}
			
			// 记录失败的请求
			wlog($uid, "自动AI答题", "自动AI答题请求失败: {$question}, HTTP码: {$http_code}, cURL错误: {$curl_error}", 0);
			exit(json_encode(array(
				"code" => 1001,
				"msg" => "AI服务请求失败，请稍后重试",
				"data" => array(
					"answer" => "",
					"question" => $question,
					"times" => 0
				)
			)));
			break;

		case 'get_orders': // 根据账号查询订单
			$uid = trim(strip_tags(daddslashes($_POST['uid'])));
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			$username = trim(strip_tags(daddslashes($_POST['username'])));
				
				// 验证参数
			if ($uid == '' || $key == '') {
				exit(json_encode(['status' => 'error', 'message' => '用户ID或密钥不能为空']));
				}
				
			// 验证用户
				$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if (!$row) {
				// 用户不存在
				wlog(0, "订单查询", "用户{$uid}不存在", 0);
				exit(json_encode(['status' => 'error', 'message' => '用户ID不存在']));
				} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "订单查询", "密钥错误", 0);
				exit(json_encode(['status' => 'error', 'message' => '密钥错误']));
			}
			
			// 构建查询条件
			$where = "dockstatus='99'  AND status='待处理' AND cid='99999'";
			
			// 如果提供了username，则按username查询
			if (!empty($username)) {
				$username = $DB->escape($username); // 防SQL注入
				$where .= " AND user='$username'";
			}
			
			// 查询订单数据
			$orders = $DB->query("SELECT * FROM qingka_wangke_order WHERE $where ORDER BY addtime ASC LIMIT 2");
			$result = array();
			
			// 处理查询结果
			while ($order = $DB->fetch($orders)) {
				$result[] = array(
					'oid' => $order['oid'],
					'user' => $order['user'],
					'pass' => $order['pass'],
					'school' => $order['school'],
					'kcname' => $order['kcname'],
					'kcid' => $order['kcid'],  // 添加课程ID字段
					'status' => $order['status'],
					'addtime' => $order['addtime'],
					'dockstatus' => $order['dockstatus'],
					'process' => $order['process'],
					'remarks' => $order['remarks']
				);
			}
			
			// 如果找到了订单记录，将其状态更新为"进行中"
			if (count($result) > 0) {
				// 收集查询到的订单ID
				$order_ids = array();
				foreach ($result as $order) {
					$order_ids[] = $order['oid'];
				}
				
				// 只更新查询到的这些订单的状态
				if (!empty($order_ids)) {
					$ids_str = implode(',', $order_ids);
					$DB->query("UPDATE qingka_wangke_order SET status='进行中' WHERE oid IN ({$ids_str})");
					
					// 修改返回结果中的状态值
					foreach ($result as &$order) {
						$order['status'] = '进行中';
					}
					unset($order); // 解除引用
					
					// 记录状态更新日志
					wlog($uid, "订单状态更新", "将查询到的".count($result)."条订单状态更新为'进行中'", 0);
				}
			}
			
			// 记录日志
			$count = count($result);
			wlog($uid, "订单查询", "查询dockstatus=99的订单，找到{$count}条记录", 0);
			
			// 返回结果
			exit(json_encode([
				'status' => 'success',
				'message' => "查询成功，共找到{$count}条记录",
				'data' => $result
			]));
			break;
	
		// 新增update_progress接口
		case 'update_progress':
			// 验证必要参数
			$uid = isset($_POST['uid']) ? intval($_POST['uid']) : 0;
			$key = isset($_POST['key']) ? trim($_POST['key']) : '';
			$username = isset($_POST['username']) ? trim($_POST['username']) : '';
			$password = isset($_POST['password']) ? trim($_POST['password']) : '';
			$kcid = isset($_POST['kcid']) ? trim($_POST['kcid']) : '';
			$progress_data = isset($_POST['progress_data']) ? trim($_POST['progress_data']) : '';
			$progress_percent = isset($_POST['progress_percent']) ? intval($_POST['progress_percent']) : 0;
			
			// 参数验证
			if ($uid <= 0 || empty($key)) {
				$result = [
					'status' => 'error',
					'message' => '用户ID或密钥不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			if (empty($username) || empty($password)) {
				$result = [
					'status' => 'error',
					'message' => '账号和密码不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			if (empty($progress_data)) {
				$result = [
					'status' => 'error',
					'message' => '进度数据不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 验证用户身份（使用与其他接口相同的验证逻辑）
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			
			if (!$row) {
				// 用户不存在
				wlog(0, "进度更新", "用户{$uid}不存在", 0);
				$result = [
					'status' => 'error',
					'message' => '用户ID不存在'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "进度更新", "密钥错误", 0);
				$result = [
					'status' => 'error',
					'message' => '密钥错误'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 构建查询条件
			$username = $DB->escape($username); // 防SQL注入
			$password = $DB->escape($password);
			
			// 构建查询SQL
			$query = "SELECT * FROM `qingka_wangke_order` WHERE `user` = '{$username}' AND `pass` = '{$password}'AND `kcid` = '{$kcid}'AND `cid` = '99999'";
			
			// 如果提供了课程ID，则添加课程ID条件
			if (!empty($kcid)) {
				$kcid = $DB->escape($kcid);
				$query .= " AND `kcid` LIKE '%{$kcid}%'";
			}
			
			$query .= " ORDER BY `oid` DESC LIMIT 1";
			
			$result = $DB->query($query);
			$order_data = $DB->fetch($result);
			
			if (!$order_data) {
				$result = [
					'status' => 'error',
					'message' => '未找到匹配的订单'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			$order_id = $order_data['oid'];
			
			// 安全处理进度数据
			$progress_data = $DB->escape($progress_data);
			
			// 更新订单remarks和process字段
			$update_query = "UPDATE `qingka_wangke_order` SET 
							 `remarks` = '{$progress_data}',
							 `process` = '{$progress_percent}%'
							 WHERE `oid` = {$order_id}";
			
			$update_result = $DB->query($update_query);
			
			if ($update_result) {
				// 记录进度更新日志
				wlog($uid, "进度更新", "订单ID: {$order_id}, 账号: {$username}, 进度: {$progress_percent}%", 0);
				$result = [
					'status' => 'success',
					'message' => '进度数据更新成功',
					'oid' => $order_id
				];
				} else {
				wlog($uid, "进度更新", "更新失败 - 订单ID: {$order_id}, 账号: {$username}", 0);
				$result = [
					'status' => 'error',
					'message' => '进度数据更新失败: ' . $DB->error()
				];
			}
			
			echo json_encode($result, JSON_UNESCAPED_UNICODE);
				break;
				
		// 新增complete_task接口 - 用于在所有课程学习任务完成时更新订单状态
		case 'complete_task':
			// 验证必要参数
			$uid = isset($_POST['uid']) ? intval($_POST['uid']) : 0;
			$key = isset($_POST['key']) ? trim($_POST['key']) : '';
			$username = isset($_POST['username']) ? trim($_POST['username']) : '';
			$password = isset($_POST['password']) ? trim($_POST['password']) : '';
			$kcid = isset($_POST['kcid']) ? trim($_POST['kcid']) : '';
			
			// 参数验证
			if ($uid <= 0 || empty($key)) {
				$result = [
					'status' => 'error',
					'message' => '用户ID或密钥不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			if (empty($username) || empty($password)) {
				$result = [
					'status' => 'error',
					'message' => '账号和密码不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 验证用户身份
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			
			if (!$row) {
				// 用户不存在
				wlog(0, "任务完成", "用户{$uid}不存在", 0);
				$result = [
					'status' => 'error',
					'message' => '用户ID不存在'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "任务完成", "密钥错误", 0);
				$result = [
					'status' => 'error',
					'message' => '密钥错误'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 记录API调用日志
			wlog($uid, "任务完成API", "接收任务完成请求 - 账号: {$username}", 0);
			
			// 构建查询条件
			$username = $DB->escape($username); // 防SQL注入
			$password = $DB->escape($password);
			
			// 构建查询SQL
			$query = "SELECT * FROM `qingka_wangke_order` WHERE `user` = '{$username}' AND `pass` = '{$password}'";
			
			// 如果提供了课程ID，则添加课程ID条件
			if (!empty($kcid)) {
				$kcid = $DB->escape($kcid);
				$query .= " AND `kcid` LIKE '%{$kcid}%'";
			}
			
			$query .= " ORDER BY `oid` DESC LIMIT 1";
			
			$result = $DB->query($query);
			$order_data = $DB->fetch($result);
			
			if (!$order_data) {
				wlog($uid, "任务完成", "未找到匹配的订单 - 账号: {$username}", 0);
				$result = [
					'status' => 'error',
					'message' => '未找到匹配的订单'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			$order_id = $order_data['oid'];
			$kcname = $order_data['kcname'];
			
			// 更新订单状态为已完成，并设置进度为100%
			$update_query = "UPDATE `qingka_wangke_order` SET 
							 `status` = '已完成',
							 `process` = '100%',
							 `remarks` = '所有课程学习任务已完成'
							 WHERE `oid` = {$order_id}";
			
			$update_result = $DB->query($update_query);
			
			if ($update_result) {
				// 记录完成任务日志
				wlog($uid, "任务完成", "订单ID: {$order_id}, 账号: {$username}, 课程: {$kcname}, 状态已更新为'已完成'", 0);
				// 记录系统通知日志
				wlog($uid, "系统通知", "你的订单 [{$order_id}] {$kcname} 已完成", 0);
				$result = [
					'status' => 'success',
					'message' => '任务已标记为完成',
					'oid' => $order_id
				];
			} else {
				wlog($uid, "任务完成", "更新失败 - 订单ID: {$order_id}, 账号: {$username}, 错误: " . $DB->error(), 0);
				$result = [
					'status' => 'error',
					'message' => '任务完成状态更新失败: ' . $DB->error()
				];
			}
			
			echo json_encode($result, JSON_UNESCAPED_UNICODE);
			break;


		
		    case 'aimodel': // AI大模型专用接口
			// 获取认证信息 - 改进验证流程，参考autoai接口
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取请求参数
			$question = isset($_POST['question']) ? trim($_POST['question']) : '';
			$model = isset($_POST['model']) ? trim($_POST['model']) : 'gpt-3.5-turbo-16k'; // 默认模型
			$prompt = isset($_POST['prompt']) ? trim($_POST['prompt']) : '';
			
			// 验证参数
			if(empty($question)){
				wlog(0, "AI大模型", "请求验证失败: 缺少question参数", 0);
				exit(json_encode(['code'=>-1, 'msg'=>'请提供问题内容']));
			}
			
			// 严格验证key - 检查用户表
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				wlog(0, "AI大模型", "请求验证失败: 密钥错误: " . substr($key, 0, 10) . "...", 0);
				exit(json_encode(['code'=>-1, 'msg'=>'密钥错误，请提供有效的key']));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 设置不同模型的积分消费
			$model_cost = array(
				'gpt-3.5-turbo-16k' => 1,
				'deepseek-chat' => 2,
				'gpt-4o-mini' => 2,
				'gpt-4' => 10,
				'glm-4-flash' => 2
			);
			
			// 根据模型确定需要的积分
			$required_points = isset($model_cost[$model]) ? $model_cost[$model] : 1;
			
			// 检查用户积分是否足够
			if($row['money'] < $required_points) {
				wlog($uid, "AI大模型", "请求验证失败: 积分不足。当前模型 {$model} 需要 {$required_points} 积分", 0);
				exit(json_encode(['code'=>-1, 'msg'=>"积分不足，无法进行查询，请充值。当前模型 {$model} 需要 {$required_points} 积分"]));
			}
			
			
			// 确保模型是支持的模型
			$supported_models = array('gpt-3.5-turbo','gpt-3.5-turbo-16k', 'deepseek-chat', 'gpt-4o-mini', 'gpt-4', 'glm-4-flash');
			if(!in_array($model, $supported_models)){
				wlog($uid, "AI大模型", "警告: 不支持的模型 " . $model . "，使用默认模型替代", 0);
				$model = 'gpt-3.5-turbo-16k'; // 如果不支持则使用默认模型
			}
			
			// 调用AI接口
			try {
				// 准备请求数据
				$default_system_prompt = "你是一个知识渊博、乐于助人的AI助手。请根据用户的问题提供准确、详细且有建设性的回答。";
				$system_prompt = !empty($prompt) ? $prompt : $default_system_prompt;
				
				// 模型映射
				$model_mapping = array(
					'gpt-3.5-turbo-16k' => 'gpt-3.5-turbo-16k',
					'deepseek-chat' => 'deepseek-chat',
					'gpt-4o-mini' => 'gpt-4o-mini',
					'gpt-4' => 'gpt-4',
					'glm-4-flash' => 'glm-4-flash'
				);
				$api_model = isset($model_mapping[$model]) ? $model_mapping[$model] : 'gpt-3.5-turbo-16k';
				
				// 构建请求数据
				$ai_data = array(
					"model" => $api_model,
					"messages" => array(
						array(
							"role" => "system",
							"content" => $system_prompt
						),
						array(
							"role" => "user",
							"content" => $question
						)
					),
					"temperature" => 0.7
				);
				
				// 发送API请求
				$ch = curl_init('https://api.ephone.ai/v1/chat/completions');
				curl_setopt($ch, CURLOPT_POST, 1);
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ai_data));
				curl_setopt($ch, CURLOPT_HTTPHEADER, array(
					'Accept: application/json',
					'Authorization: Bearer sk-YQSpuaSrMqVxGviMB07zYwOAkm90ZK48mffK852a2r8iEPgi',
					'Content-Type: application/json'
				));
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($ch, CURLOPT_TIMEOUT, 15); // 超时时间15秒
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
				
				$response = curl_exec($ch);
				$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
				$curl_error = curl_error($ch);
				curl_close($ch);
				
				// 处理响应
				if ($http_code == 200 && $response) {
					$ai_result = json_decode($response, true);
					if (isset($ai_result['choices'][0]['message']['content'])) {
						$answer = $ai_result['choices'][0]['message']['content'];
						
						// 扣除用户积分
						$DB->query("UPDATE qingka_wangke_user SET money=money-{$required_points} WHERE uid='$uid'");
						
						// 记录积分扣除日志
						wlog($uid, "AI大模型", "请求成功，消费{$required_points}积分，使用模型：{$model}", -$required_points);
						
						// 记录搜题历史
						$time = date('Y-m-d H:i:s');
						$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
								   VALUES ('{$uid}', '{$question}', '0', 'AI大模型', '{$answer}', '100', '{$time}')");
						
						// 记录成功日志，包含题目和答案摘要
						$answer_summary = substr($answer, 0, 50) . (strlen($answer) > 50 ? "..." : "");
						$question_summary = substr($question, 0, 50) . (strlen($question) > 50 ? "..." : "");
						wlog($uid, "AI大模型", "请求成功 - 模型: {$model} 题目: {$question} - 答案: {$answer_summary}", 0);
						
						exit(json_encode(['code'=>1, 'answer'=>$answer, 'msg'=>'success']));
					} else {
						wlog($uid, "AI大模型", "响应格式不正确，找不到有效内容", 0);
					}
				} else {
					wlog($uid, "AI大模型", "请求失败: HTTP码=" . $http_code . ", 错误=" . $curl_error, 0);
				}
				
				// 请求失败，返回模拟回复
				$simulated_reply = "由于API连接问题，无法获取AI回答。请稍后再试或联系管理员。当前请求的模型: " . $model;
				wlog($uid, "AI大模型", "返回模拟回复", 0);
				exit(json_encode(['code'=>-1, 'answer'=>$simulated_reply, 'msg'=>'请求失败，无法获取AI回答']));
				
			} catch (Exception $e) {
				wlog($uid, "AI大模型", "请求异常: " . $e->getMessage(), 0);
				exit(json_encode(['code'=>-1, 'msg'=>'AI接口请求异常: ' . $e->getMessage()]));
			}
			break;
	}
}


?>