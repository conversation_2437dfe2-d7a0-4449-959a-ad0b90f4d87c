#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通考试入口列表API服务
提供API接口，接收用户账号密码课程名并返回考试入口url列表
基于chaoxing_api_server.py和获取考试入口URL.py创建
"""

import os
import re
import json
import base64
import pyaes
import requests
import time
import random
import concurrent.futures
from flask import Flask, request, jsonify
from concurrent.futures import ThreadPoolExecutor
from queue import Queue
import argparse
from bs4 import BeautifulSoup

# 创建Flask应用
app = Flask(__name__)

# 全局配置
config = {
    "max_workers": 20,  # 默认课程线程池大小
    "max_page_workers": 10,  # 默认页面线程池大小
    "timeout": 15,  # 默认超时时间(秒)
    "max_connections": 100,  # 最大并发连接数
}

# 创建全局线程池，用于处理多账号请求
global_executor = ThreadPoolExecutor(max_workers=config["max_connections"])

# 创建全局连接池配置
adapter = requests.adapters.HTTPAdapter(
    pool_connections=config["max_connections"],
    pool_maxsize=config["max_connections"],
    max_retries=3,
    pool_block=False,
)

# AES密钥
AES_KEY = "u2oh6Vu^HWe4_AES"

# 全局缓存
course_enc_cache = {}


# PKCS7填充
def pkcs7_padding(s, block_size=16):
    bs = block_size
    return s + (bs - len(s) % bs) * chr(bs - len(s) % bs).encode()


# 分割数据块
def split_to_data_blocks(byte_str, block_size=16):
    length = len(byte_str)
    j, y = divmod(length, block_size)
    blocks = []
    shenyu = j * block_size
    for i in range(j):
        start = i * block_size
        end = (i + 1) * block_size
        blocks.append(byte_str[start:end])
    stext = byte_str[shenyu:]
    if stext:
        blocks.append(stext)
    return blocks


# AES加密类
class AESCipher:
    def __init__(self):
        self.key = AES_KEY.encode("utf8")
        self.iv = AES_KEY.encode("utf8")

    def encrypt(self, plaintext: str):
        ciphertext = b""
        cbc = pyaes.AESModeOfOperationCBC(self.key, self.iv)
        plaintext = plaintext.encode("utf-8")
        blocks = split_to_data_blocks(pkcs7_padding(plaintext))
        for b in blocks:
            ciphertext = ciphertext + cbc.encrypt(b)
        base64_text = base64.b64encode(ciphertext).decode("utf8")
        return base64_text


# 超星学习通考试管理器类
class ChaoxingExamManager:
    def __init__(self, username, password):
        """
        初始化超星学习通考试管理器
        :param username: 加密后的用户名
        :param password: 加密后的密码
        """
        self.username = username
        self.password = password
        self.session = requests.Session()
        # 禁用SSL验证，解决证书验证失败问题
        self.session.verify = False
        # 设置连接池参数，提高并发性能
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        # 设置默认超时时间
        self.session.timeout = config["timeout"]
        self.cookies = None
        self.base_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }
        # 初始化course_enc属性为None
        self.course_enc = None
        # 初始化stuenc属性为None
        self.stuenc = None
        # 初始化openc属性为None
        self.openc = None

    def login(self):
        """
        登录超星学习通
        :return: 登录是否成功
        """
        try:
            url = "https://passport2.chaoxing.com/fanyalogin"

            payload = {
                "fid": "-1",
                "uname": self.username,
                "password": self.password,
                "refer": "https%3A%2F%2Fi.chaoxing.com",
                "t": "true",
                "forbidotherlogin": "0",
                "validate": "",
                "doubleFactorLogin": "0",
                "independentId": "0",
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Pragma": "no-cache",
                "Cache-Control": "no-cache",
                "sec-ch-ua-platform": '"Windows"',
                "X-Requested-With": "XMLHttpRequest",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "Origin": "https://passport2.chaoxing.com",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://passport2.chaoxing.com/login?fid=&newversion=true&refer=https%3A%2F%2Fi.chaoxing.com",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            }

            # 添加超时设置
            response = self.session.post(url, data=payload, headers=headers, timeout=15)

            try:
                result = json.loads(response.text)
                if result.get("status") and result.get("status") is True:
                    self.cookies = self.session.cookies
                    return True
                else:
                    error_msg = result.get("msg", "未知错误")
                    return False
            except json.JSONDecodeError:
                # 检查是否是HTML响应，可能是登录页面或验证码页面
                if "<html" in response.text.lower():
                    if "验证码" in response.text or "captcha" in response.text.lower():
                        return False
                    elif "登录" in response.text or "login" in response.text.lower():
                        return False
                return False
        except requests.exceptions.Timeout:
            return False
        except requests.exceptions.ConnectionError:
            return False
        except Exception:
            return False

    def get_course_list(self):
        """
        获取课程列表
        :return: 课程列表
        """
        try:
            import urllib3

            # 禁用SSL警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            url = "https://mooc2-ans.chaoxing.com/visit/courselistdata"

            payload = {
                "courseType": "1",  # 1表示学生课程
                "courseFolderId": "0",
                "query": "",
                "pageHeader": "",
                "single": "0",
                "superstarClass": "0",
            }

            headers = self.base_headers.copy()
            headers["Referer"] = "https://mooc2-ans.chaoxing.com/visit/interaction"
            headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"

            # 随机选择一个User-Agent
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
            ]
            headers["User-Agent"] = random.choice(user_agents)

            # 添加超时设置
            response = self.session.post(
                url, data=payload, headers=headers, verify=False, timeout=15
            )

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 查找课程列表
            course_items = soup.select(".learnCourse")

            # 如果没有找到课程，尝试其他选择器
            if not course_items:
                course_items = soup.select(".course-list .course")

            # 如果还是没有找到课程，尝试第三种选择器
            if not course_items:
                course_items = soup.select(".course-info-item")

            courses = []
            for item in course_items:
                try:
                    # 获取课程ID和班级ID
                    course_id_input = item.select_one(".courseId")
                    clazz_id_input = item.select_one(".clazzId")
                    person_id_input = item.select_one(".curPersonId")

                    if course_id_input and clazz_id_input and person_id_input:
                        course_id = course_id_input.get("value")
                        clazz_id = clazz_id_input.get("value")
                        person_id = person_id_input.get("value")

                        # 获取课程名称
                        course_name_span = item.select_one(".course-name")
                        course_name = (
                            course_name_span.text.strip()
                            if course_name_span
                            else "未知课程"
                        )

                        # 获取教师名称
                        teacher_name_p = item.select_one(".line2.color3")
                        teacher_name = (
                            teacher_name_p.text.strip()
                            if teacher_name_p
                            else "未知教师"
                        )

                        courses.append(
                            {
                                "course_name": course_name,
                                "teacher_name": teacher_name,
                                "course_id": course_id,
                                "clazz_id": clazz_id,
                                "person_id": person_id,
                            }
                        )
                except Exception:
                    pass

            return courses
        except requests.exceptions.Timeout:
            return []
        except requests.exceptions.ConnectionError:
            return []
        except Exception:
            return []

    def get_course_enc_for_exam(self, course_id, clazz_id, person_id):
        """
        通过访问课程详情页面，模拟点击考试按钮，获取考试列表的enc参数
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: enc参数字典，包含stuenc、enc和openc参数
        """
        try:
            # 构建课程详情页链接
            course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"

            headers = self.base_headers.copy()

            # 访问课程详情页，可能会重定向
            response = self.session.get(
                course_url, headers=headers, verify=False, allow_redirects=True
            )

            # 解析页面内容，获取考试按钮的相关信息
            soup = BeautifulSoup(response.text, "html.parser")

            # 首先尝试从页面中获取enc和stuenc参数
            enc = None
            stuenc = None
            openc = None

            # 从隐藏输入框中获取
            enc_input = soup.select_one("#examEnc")
            if enc_input and enc_input.get("value"):
                enc = enc_input.get("value")

            # 尝试获取openc参数
            openc_input = soup.select_one("#openc")
            if openc_input and openc_input.get("value"):
                openc = openc_input.get("value")

            # 尝试获取stuenc参数（可能不存在）
            stuenc_input = soup.select_one("#stuenc")
            if stuenc_input and stuenc_input.get("value"):
                stuenc = stuenc_input.get("value")

            # 如果没有找到stuenc参数，可以尝试使用userId作为stuenc参数
            if not stuenc:
                user_id_input = soup.select_one("#userId")
                if user_id_input and user_id_input.get("value"):
                    user_id = user_id_input.get("value")

            # 如果已经获取到了enc参数，但没有stuenc参数，可以尝试使用enc作为stuenc
            if enc and not stuenc:
                stuenc = enc

            # 如果已经获取到了参数，可以直接返回
            if enc:  # 只要有enc参数就可以返回，stuenc可以为空
                return {"stuenc": stuenc or enc, "enc": enc, "openc": openc}

            # 如果没有直接获取到参数，继续尝试通过点击考试按钮获取
            # 获取所有导航按钮，用于后续分析
            nav_buttons = soup.select(".nav-content ul li")

            # 找到考试按钮(dataname='ks')
            exam_button_li = None
            for btn in nav_buttons:
                if btn.get("dataname") == "ks":
                    exam_button_li = btn
                    break

            if exam_button_li:
                # 获取考试按钮的链接
                exam_button = exam_button_li.select_one("a")
                if exam_button:
                    # 优先使用data-url属性
                    exam_url = exam_button.get("data-url")
                    if not exam_url:
                        exam_url = exam_button.get("href")

                    if exam_url:
                        # 如果是相对路径，转换为绝对路径
                        if exam_url.startswith("/"):
                            exam_url = f"https://mooc1.chaoxing.com{exam_url}"

                        # 如果是javascript:void(0)这样的链接，需要特殊处理
                        if exam_url.startswith("javascript:"):
                            # 尝试构建考试列表URL
                            exam_url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}"

                        # 访问考试按钮链接
                        exam_response = self.session.get(exam_url, headers=headers, verify=False)

                        # 从响应URL中提取参数
                        stuenc_match = re.search(r"stuenc=([^&]+)", exam_response.url)
                        enc_match = re.search(r"enc=([^&]+)", exam_response.url)
                        openc_match = re.search(r"openc=([^&]+)", exam_response.url)

                        if stuenc_match:
                            stuenc = stuenc_match.group(1)

                        if enc_match:
                            enc = enc_match.group(1)

                        if openc_match:
                            openc = openc_match.group(1)

                        if stuenc and enc:
                            return {"stuenc": stuenc, "enc": enc, "openc": openc}

            return None
        except Exception as e:
            return None

    def get_exam_list_html(self, course_id, clazz_id, person_id):
        """
        获取考试列表HTML
        """
        try:
            # 生成时间戳
            timestamp = int(time.time() * 1000)

            # 构建考试列表URL
            url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={timestamp}"

            # 获取enc参数
            enc_info = self.get_course_enc_for_exam(course_id, clazz_id, person_id)
            if enc_info:
                if enc_info.get("stuenc"):
                    url += f"&stuenc={enc_info['stuenc']}"
                if enc_info.get("enc"):
                    url += f"&enc={enc_info['enc']}"
                if enc_info.get("openc"):
                    url += f"&openc={enc_info['openc']}"

            # 设置请求头
            headers = self.base_headers.copy()
            headers["Referer"] = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"

            # 发送请求
            response = self.session.get(url, headers=headers, verify=False)

            if response.status_code != 200:
                return None

            return response.text

        except Exception as e:
            return None

    def parse_exam_list_html(self, html, course_id, clazz_id, person_id):
        """
        解析考试列表HTML
        """
        try:
            soup = BeautifulSoup(html, "html.parser")

            # 获取考试列表项
            exam_items = soup.select(".bottomList li")

            if not exam_items:
                exam_items = soup.select(".examListItem")

            if not exam_items:
                exam_items = soup.select("li:has(div.tag.icon-exam)")

            exams = []
            for item in exam_items:
                try:
                    # 获取点击div元素
                    div_element = item.select_one("div[onclick^='goTest']")

                    if not div_element:
                        div_element = item.select_one("[onclick^='goTest']")

                    if not div_element:
                        div_element = item.find(lambda tag: tag.has_attr('onclick') and 'goTest' in tag['onclick'])

                    if not div_element:
                        # 尝试直接从页面源码中提取goTest函数调用
                        onclick_match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", str(item))
                        if onclick_match:
                            # 获取考试标题
                            title_elem = item.select_one(".overHidden2")
                            if not title_elem:
                                title_elem = item.select_one("p:first-child")
                            title = title_elem.text.strip() if title_elem else "未知考试"

                            # 获取考试状态
                            status_elem = item.select_one(".status")
                            status = status_elem.text.strip() if status_elem else "未知状态"

                            exam_info = {
                                "title": title,
                                "status": status,
                                "course_id": course_id,
                                "clazz_id": clazz_id,
                                "cpi": person_id,
                                "exam_id": onclick_match.group(2),
                                "relation_id": onclick_match.group(3),
                                "enc": onclick_match.group(4),
                                "test_paper_id": onclick_match.group(5),
                                "is_retest": onclick_match.group(6),
                                "end_time": onclick_match.group(7)
                            }
                            exams.append(exam_info)
                            continue

                    if div_element:
                        onclick = div_element.get("onclick", "")

                        # 解析onclick属性中的goTest函数调用
                        match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", onclick)
                        if match:
                            # 获取考试标题
                            title_elem = item.select_one(".overHidden2")
                            if not title_elem:
                                title_elem = item.select_one("p:first-child")
                            title = title_elem.text.strip() if title_elem else "未知考试"

                            # 获取考试状态
                            status_elem = item.select_one(".status")
                            status = status_elem.text.strip() if status_elem else "未知状态"

                            exam_info = {
                                "title": title,
                                "status": status,
                                "course_id": course_id,
                                "clazz_id": clazz_id,
                                "cpi": person_id,
                                "exam_id": match.group(2),
                                "relation_id": match.group(3),
                                "enc": match.group(4),
                                "test_paper_id": match.group(5),
                                "is_retest": match.group(6),
                                "end_time": match.group(7)
                            }
                            exams.append(exam_info)

                except Exception as e:
                    continue

            return exams

        except Exception as e:
            return []

    def generate_exam_params(self, exam_info):
        """
        生成考试参数字符串，格式：courseId|classId|examId|cpi
        """
        try:
            # 构建考试参数字符串，修正参数顺序为：courseId|classId|examId|cpi
            params = f"{exam_info['course_id']}|{exam_info['clazz_id']}|{exam_info['exam_id']}|{exam_info['cpi']}"

            return params

        except Exception as e:
            return None

    def get_exam_list_by_course_name(self, course_name):
        """
        根据课程名获取考试列表
        """
        try:
            # 获取课程列表
            courses = self.get_course_list()
            if not courses:
                return None, "未找到课程"

            # 查找匹配的课程
            matched_course = None
            for course in courses:
                if course_name in course["course_name"] or course["course_name"] in course_name:
                    matched_course = course
                    break

            if not matched_course:
                return None, f"未找到课程名包含'{course_name}'的课程"

            course_id = matched_course["course_id"]
            clazz_id = matched_course["clazz_id"]
            person_id = matched_course["person_id"]

            # 获取考试列表HTML
            exam_list_html = self.get_exam_list_html(course_id, clazz_id, person_id)
            if not exam_list_html:
                return None, "获取考试列表失败"

            # 解析考试列表HTML
            exams = self.parse_exam_list_html(exam_list_html, course_id, clazz_id, person_id)

            if not exams:
                return None, "未找到考试"

            # 为每个考试生成参数
            for exam in exams:
                # 生成考试参数字符串
                params = self.generate_exam_params(exam)
                exam['params'] = params

            return exams, None

        except Exception as e:
            return None, f"获取考试列表时出错: {str(e)}"


# 获取考试列表的主函数
def get_exam_urls_by_course_name(username, password, course_name):
    """
    根据课程名获取考试入口URL列表
    """
    # 加密用户名和密码
    cipher = AESCipher()
    encrypted_username = cipher.encrypt(username)
    encrypted_password = cipher.encrypt(password)

    # 创建考试管理器
    manager = ChaoxingExamManager(encrypted_username, encrypted_password)

    # 登录
    if not manager.login():
        return {"error": "登录失败，请检查账号密码"}

    # 获取考试列表
    exams, error = manager.get_exam_list_by_course_name(course_name)

    if error:
        return {"error": error}

    if not exams:
        return {"error": "未找到考试"}

    # 格式化返回结果，与chaoxing_api_server.py保持一致
    result = []
    for exam in exams:
        exam_info = {
            "title": exam.get("title", "未知考试"),
            "status": exam.get("status", "未知状态"),
            "params": exam.get("params", "")
        }
        result.append(exam_info)

    return {"exams": result, "total": len(result)}


# API路由：获取考试入口URL列表
@app.route("/get_kaoshi", methods=["POST"])
def api_get_exam_urls():
    """
    API接口：获取考试入口URL列表
    """
    # 获取请求参数
    data = request.json
    if not data:
        return jsonify({"error": "请求参数错误，需要JSON格式数据"}), 400

    # 获取必要参数
    username = data.get("username")
    password = data.get("password")
    course_name = data.get("course_name")

    # 验证参数
    if not username or not password or not course_name:
        return jsonify({"error": "账号、密码和课程名不能为空"}), 400

    try:
        # 设置全局超时
        socket_timeout = data.get("timeout", config["timeout"])
        import socket
        socket.setdefaulttimeout(socket_timeout)

        # 使用全局线程池处理请求
        future = global_executor.submit(get_exam_urls_by_course_name, username, password, course_name)

        # 等待结果，设置超时时间
        try:
            result = future.result(timeout=60)  # 60秒超时
        except concurrent.futures.TimeoutError:
            return jsonify({"error": "请求超时，请稍后重试"}), 408

        # 处理结果
        if "error" in result:
            error_msg = result["error"]
            return jsonify({"error": error_msg}), 400

        # 返回成功结果，格式与chaoxing_api_server.py保持一致
        return jsonify({
            "success": True,
            "count": result["total"],
            "courses_count": 1,  # 按课程名查询，只有一个课程
            "homeworks": result["exams"]
        })

    except Exception as e:
        return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500


# 健康检查接口
@app.route("/health", methods=["GET"])
def health_check():
    """
    健康检查接口
    """
    return jsonify({
        "status": "healthy",
        "service": "chaoxing_exam_api_server",
        "version": "1.0.0"
    })


# 获取服务信息接口
@app.route("/info", methods=["GET"])
def get_info():
    """
    获取服务信息接口
    """
    return jsonify({
        "service_name": "超星学习通考试入口列表API服务",
        "description": "提供API接口，接收用户账号密码课程名并返回考试入口url列表",
        "version": "1.0.0",
        "endpoints": {
            "/get_kaoshi": {
                "method": "POST",
                "description": "获取考试入口URL列表",
                "parameters": {
                    "username": "学习通账号",
                    "password": "学习通密码",
                    "course_name": "课程名称"
                }
            },
            "/health": {
                "method": "GET",
                "description": "健康检查"
            },
            "/info": {
                "method": "GET",
                "description": "获取服务信息"
            }
        },
        "example_request": {
            "url": "http://127.0.0.1:5001/get_kaoshi",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json"
            },
            "body": {
                "username": "your_username",
                "password": "your_password",
                "course_name": "课程名称"
            }
        },
        "example_response": {
            "success": True,
            "count": 3,
            "courses_count": 1,
            "homeworks": [
                {
                    "title": "期中考试",
                    "status": "未开始",
                    "params": "124398558|7689933|253891757|404722142"
                }
            ]
        }
    })


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="超星学习通考试入口列表API服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务器地址")
    parser.add_argument("--port", type=int, default=5001, help="服务器端口")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")

    args = parser.parse_args()

    print(f"超星学习通考试入口列表API服务启动中...")
    print(f"服务地址: http://{args.host}:{args.port}")
    print(f"API接口: http://{args.host}:{args.port}/get_kaoshi")
    print(f"健康检查: http://{args.host}:{args.port}/health")
    print(f"服务信息: http://{args.host}:{args.port}/info")

    app.run(host=args.host, port=args.port, debug=args.debug, threaded=True)
