#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import re
import os
import sys
import argparse
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs


class ChaoxingHomeworkSubmitter:
    def __init__(self, username=None, password=None, custom_answers_file=None):
        """初始化超星作业提交器"""
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "sec-ch-ua-platform": '"Windows"',
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-mobile": "?0",
            "Origin": "https://passport2.chaoxing.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }
        self.username = username
        self.password = password
        self.is_logged_in = False
        self.homework_info = {}
        self.custom_answers = {}

        # 如果提供了自定义答案文件，加载它
        if custom_answers_file and os.path.exists(custom_answers_file):
            try:
                with open(custom_answers_file, "r", encoding="utf-8") as f:
                    self.custom_answers = json.load(f)
                print(f"已加载自定义答案文件: {custom_answers_file}")
            except Exception as e:
                print(f"加载自定义答案文件失败: {e}")

    def login(self, username=None, password=None):
        """登录超星学习通"""
        if username:
            self.username = username
        if password:
            self.password = password

        if not self.username or not self.password:
            print("请提供用户名和密码")
            return False

        # 这里使用的是加密后的用户名和密码，实际使用时需要替换为自己的
        login_url = "https://passport2.chaoxing.com/fanyalogin"
        login_data = {
            "fid": "-1",
            "uname": "D2bCfRqh3U9bDhfsvVUneg==",  # 加密后的用户名
            "password": "/H+E5YEkVpxVz37gYPB0xg==",  # 加密后的密码
            "refer": "https%3A%2F%2Fi.chaoxing.com",
            "t": "true",
            "forbidotherlogin": "0",
            "validate": "",
            "doubleFactorLogin": "0",
            "independentId": "0",
            "independentNameId": "0",
        }

        # 设置登录请求的headers
        login_headers = self.headers.copy()
        login_headers["Referer"] = (
            "https://passport2.chaoxing.com/login?fid=&newversion=true&refer=https%3A%2F%2Fi.chaoxing.com"
        )
        login_headers["Cookie"] = (
            'route=3a66e47c0ac92560e5c67cd5e1803201; retainlogin=1; fid=2790; JSESSIONID=769C715E1D24E7940CACF528529155DC; source=""'
        )

        try:
            response = self.session.post(
                login_url, data=login_data, headers=login_headers
            )
            if response.status_code == 200:
                result = response.json()
                if result.get("status") and result.get("url"):
                    self.is_logged_in = True
                    print("登录成功!")
                    return True
                else:
                    print(f"登录失败: {result.get('msg', '未知错误')}")
            else:
                print(f"登录请求失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"登录过程中出现异常: {e}")

        return False

    def parse_homework_page(self, url=None, html_content=None):
        """解析作业页面，提取题目信息"""
        if not url and not html_content:
            print("请提供作业URL或HTML内容")
            return False

        try:
            if url:
                # 设置请求headers
                homework_headers = self.headers.copy()
                homework_headers["Referer"] = (
                    "https://mooc1.chaoxing.com/mooc2/work/list?courseId=228836418&classId=64009643&cpi=282371395&ut=s&t=1751482770162&stuenc=ad49072db6e9f19fe87ae593548e14f1&enc=550114ba5f5bf0c61d3f650fe04350da"
                )
                homework_headers["Accept"] = (
                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
                )
                homework_headers["Upgrade-Insecure-Requests"] = "1"
                homework_headers["Cookie"] = (
                    'k8s=1751347563.493.12706.587279; route=1ab934bb3bbdaaef56ce3b0da45c52ed; fid=2790; writenote=yes; doubleSpeedValue=2; videojs_id=8260357; jrose=E06B43EA4531A327AA736AB3737BC051.mooc-p4-2387308155-jzjwp; source=""; _uid=221880762; _d=1751482845912; UID=221880762; vc3=a454agod0VF66B%2B8Oy75nUs8GS2N2ty8jUEWMi%2B7NpLERrpe7ydd2NCI4R1W5%2BwMSICNhL%2Fhvrepic6c7NaUZ0YCyybfNWrKh4Nbakg1H9kg%2FiVvJVk2TLz4dxyQxrfkmLemAvdJvvWhg7iKpeAJ2g5i67xt2YpKH8i3OHsyztE%3Da66dacb9740164f5f3d977ea0ff47b52; uf=da0883eb5260151ed5cd77328d409813eb3dd150f62496a8c8dcfef3f8d40f9980daa5ac6aa7d14f1db27cf8906ebabc81a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba0790730943c5c914713028f1ec42bf71b1188854805578cc7988116ae8984b028e33b0a5f2ba96c71a0d8eef657fc076d342747f5d0eac2fc7b9a467b233c21a4df7ff280fcb29d10d8a4c92b12beb4b44156174a8045981e87e69aadf3839ce560d457d8c24c6dfe7fafd565af53bf2; cx_p_token=cad34a0d2b89a3b7d5d904a98b0222c7; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyMjE4ODA3NjIiLCJsb2dpblRpbWUiOjE3NTE0ODI4NDU5MTMsImV4cCI6MTc1MjA4NzY0NX0.6J9y34l94EvL3GE7M7CzyB2OSK3tVv-0wBlJ_j78IDY; xxtenc=c4885b4f5d02c8246c0260b0959ca202; DSSTASH_LOG=C_38-UN_1612-US_221880762-T_1751482845914; spaceFid=2790; spaceRoleId=3; tl=1'
                )

                # 获取作业页面内容
                response = self.session.get(url, headers=homework_headers)
                if response.status_code != 200:
                    print(f"获取作业页面失败，状态码: {response.status_code}")
                    return False
                html_content = response.text

                # 保存作业页面内容到文件，方便调试
                with open("homework_page.html", "w", encoding="utf-8") as f:
                    f.write(html_content)
                print("已保存作业页面内容到 homework_page.html")

            # 解析HTML内容
            soup = BeautifulSoup(html_content, "html.parser")

            # 提取作业标题
            title = soup.select_one(".mark_title")
            if title:
                self.homework_info["title"] = title.text.strip()

            # 提取作业表单信息
            form = soup.select_one("#submitForm")
            if form:
                # 提取表单参数
                self.homework_info["form_action"] = form.get("action", "")
                self.homework_info["form_method"] = form.get("method", "post")

                # 提取隐藏字段
                hidden_fields = {}
                for input_tag in form.select('input[type="hidden"]'):
                    name = input_tag.get("name")
                    value = input_tag.get("value", "")
                    if name:
                        hidden_fields[name] = value
                self.homework_info["hidden_fields"] = hidden_fields

            # 提取题目信息
            questions = []
            for question_div in soup.select(".questionLi"):
                question_id = question_div.get("id", "").replace("question", "")
                question_type = (
                    question_div.select_one('input[id^="answertype"]').get("value")
                    if question_div.select_one('input[id^="answertype"]')
                    else ""
                )
                question_title = (
                    question_div.select_one(".mark_name").text.strip()
                    if question_div.select_one(".mark_name")
                    else ""
                )

                # 提取题目内容（去除题号和题型）
                if question_title:
                    # 移除题号和题型信息
                    question_content = re.sub(r"^\d+\.", "", question_title)
                    question_content = re.sub(
                        r"\(简答题\)", "", question_content
                    ).strip()
                else:
                    question_content = ""

                questions.append(
                    {
                        "id": question_id,
                        "type": question_type,
                        "title": question_content,
                    }
                )

            self.homework_info["questions"] = questions
            print(f"成功解析到 {len(questions)} 个题目")

            # 解析URL参数
            if url:
                parsed_url = urlparse(url)
                query_params = parse_qs(parsed_url.query)
                for key, value in query_params.items():
                    if key not in self.homework_info:
                        self.homework_info[key] = value[0] if value else ""

            return True
        except Exception as e:
            print(f"解析作业页面时出现异常: {e}")
            import traceback

            traceback.print_exc()
            return False

    def generate_answers(self):
        """生成答案"""
        if not self.homework_info or "questions" not in self.homework_info:
            print("请先解析作业页面")
            return {}

        answers = {}
        for question in self.homework_info["questions"]:
            question_id = question["id"]
            question_title = question["title"]

            # 检查是否有自定义答案
            if question_id in self.custom_answers:
                answers[question_id] = self.custom_answers[question_id]
                print(f"题目 {question_id} 使用自定义答案")
            elif question_title in self.custom_answers:
                answers[question_id] = self.custom_answers[question_title]
                print(f"题目 '{question_title}' 使用自定义答案")
            else:
                # 生成默认答案
                default_answer = self.generate_default_answer(question)
                answers[question_id] = default_answer
                print(f"题目 {question_id} 使用默认答案")

        return answers

    def generate_default_answer(self, question):
        """生成默认答案"""
        question_type = question["type"]
        question_title = question["title"]

        # 根据题目类型生成不同的默认答案
        if question_type == "4":  # 简答题
            # 这里可以根据题目内容生成更智能的答案
            # 简单示例：返回一个通用答案
            return "这是一道简答题，请根据实际情况填写答案。"
        else:
            # 其他题型的默认答案
            return "默认答案"

    def prepare_form_data(self, answers):
        """准备提交表单数据"""
        if not self.homework_info:
            print("请先解析作业页面")
            return {}

        # 基础表单数据
        form_data = {
            "courseId": self.homework_info.get("courseId", "228836418"),
            "classId": self.homework_info.get("classId", "64009643"),
            "knowledgeid": self.homework_info.get("hidden_fields", {}).get(
                "knowledgeId", "0"
            ),
            "cpi": self.homework_info.get("cpi", "282371395"),
            "workRelationId": self.homework_info.get("workId", "24875828"),
            "workAnswerId": self.homework_info.get("answerId", "52094560"),
            "jobid": self.homework_info.get("hidden_fields", {}).get("jobid", ""),
            "standardEnc": self.homework_info.get("hidden_fields", {}).get(
                "standardEnc", "eba6203955dc26a6781808c842f604b7"
            ),
            "enc_work": self.homework_info.get("hidden_fields", {}).get(
                "enc_work", "fdb8e5d170da272670346f3bf4cf7ad1"
            ),
            "totalQuestionNum": self.homework_info.get("hidden_fields", {}).get(
                "totalQuestionNum", "6eaca1bf84b6b650bc4b09d8f8805f1e"
            ),
            "pyFlag": "1",
            "answerwqbid": "",
            "mooc2": "1",
            "randomOptions": "false",
            "workTimesEnc": "",
        }

        # 添加题目ID列表
        question_ids = (
            ",".join([q["id"] for q in self.homework_info["questions"]]) + ","
        )
        form_data["answerwqbid"] = question_ids

        # 添加每个题目的答案
        for question in self.homework_info["questions"]:
            question_id = question["id"]
            question_type = question["type"]

            # 添加题目类型
            form_data[f"answertype{question_id}"] = question_type

            # 添加题目答案
            if question_id in answers:
                form_data[f"answer{question_id}"] = answers[question_id]
            else:
                form_data[f"answer{question_id}"] = ""

        return form_data

    def save_answers(self, answers=None):
        """保存答案到超星学习通"""
        if not answers and not self.homework_info:
            print("请先生成答案或解析作业页面")
            return False

        # 准备表单数据
        form_data = self.prepare_form_data(answers)

        # 保存表单数据到文件，方便调试
        with open("form_data.json", "w", encoding="utf-8") as f:
            json.dump(form_data, f, ensure_ascii=False, indent=2)
        print("已保存表单数据到 form_data.json")

        # 构建保存URL
        save_url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb?_classId=64009643&courseid=228836418&token=fdb8e5d170da272670346f3bf4cf7ad1&totalQuestionNum=6eaca1bf84b6b650bc4b09d8f8805f1e&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1"

        # 设置保存请求的headers
        save_headers = self.headers.copy()
        save_headers["Referer"] = (
            "https://mooc1.chaoxing.com/mooc-ans/mooc2/work/dowork?courseId=228836418&classId=64009643&cpi=282371395&workId=24875828&answerId=52094560&standardEnc=eba6203955dc26a6781808c842f604b7&enc=efc4c54fbc52fb7e756541b1331f72d2"
        )
        save_headers["Origin"] = "https://mooc1.chaoxing.com"
        save_headers["Cookie"] = (
            'k8s=1751347563.493.12706.587279; route=1ab934bb3bbdaaef56ce3b0da45c52ed; fid=2790; writenote=yes; doubleSpeedValue=2; videojs_id=8260357; jrose=E06B43EA4531A327AA736AB3737BC051.mooc-p4-2387308155-jzjwp; source=""; _uid=221880762; _d=1751482845912; UID=221880762; vc3=a454agod0VF66B%2B8Oy75nUs8GS2N2ty8jUEWMi%2B7NpLERrpe7ydd2NCI4R1W5%2BwMSICNhL%2Fhvrepic6c7NaUZ0YCyybfNWrKh4Nbakg1H9kg%2FiVvJVk2TLz4dxyQxrfkmLemAvdJvvWhg7iKpeAJ2g5i67xt2YpKH8i3OHsyztE%3Da66dacb9740164f5f3d977ea0ff47b52; uf=da0883eb5260151ed5cd77328d409813eb3dd150f62496a8c8dcfef3f8d40f9980daa5ac6aa7d14f1db27cf8906ebabc81a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba0790730943c5c914713028f1ec42bf71b1188854805578cc7988116ae8984b028e33b0a5f2ba96c71a0d8eef657fc076d342747f5d0eac2fc7b9a467b233c21a4df7ff280fcb29d10d8a4c92b12beb4b44156174a8045981e87e69aadf3839ce560d457d8c24c6dfe7fafd565af53bf2; cx_p_token=cad34a0d2b89a3b7d5d904a98b0222c7; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyMjE4ODA3NjIiLCJsb2dpblRpbWUiOjE3NTE0ODI4NDU5MTMsImV4cCI6MTc1MjA4NzY0NX0.6J9y34l94EvL3GE7M7CzyB2OSK3tVv-0wBlJ_j78IDY; xxtenc=c4885b4f5d02c8246c0260b0959ca202; DSSTASH_LOG=C_38-UN_1612-US_221880762-T_1751482845914; spaceFid=2790; spaceRoleId=3; tl=1'
        )

        try:
            response = self.session.post(save_url, data=form_data, headers=save_headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("status"):
                    print("答案保存成功!")
                    print(f"响应信息: {result.get('msg', '')}")
                    return True
                else:
                    print(f"答案保存失败: {result.get('msg', '未知错误')}")
            else:
                print(f"答案保存请求失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"保存答案过程中出现异常: {e}")
            import traceback

            traceback.print_exc()

        return False


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="超星学习通自动答题工具")
    parser.add_argument("-u", "--url", help="作业URL")
    parser.add_argument("-f", "--file", help="本地作业HTML文件路径")
    parser.add_argument("-a", "--answers", help="自定义答案JSON文件路径")
    parser.add_argument("-o", "--output", help="将作业信息保存到JSON文件")
    args = parser.parse_args()

    # 创建超星作业提交器实例
    submitter = ChaoxingHomeworkSubmitter(custom_answers_file=args.answers)

    # 登录超星学习通
    if not submitter.login():
        print("登录失败，尝试继续执行...")

    # 解析作业页面
    if args.url:
        print(f"从URL解析作业: {args.url}")
        submitter.parse_homework_page(url=args.url)
    elif args.file:
        print(f"从本地文件解析作业: {args.file}")
        try:
            with open(args.file, "r", encoding="utf-8") as f:
                html_content = f.read()
            submitter.parse_homework_page(html_content=html_content)
        except Exception as e:
            print(f"读取本地文件失败: {e}")
            return
    else:
        # 默认使用示例URL
        default_url = "https://mooc1.chaoxing.com/mooc-ans/mooc2/work/dowork?courseId=228836418&classId=64009643&cpi=282371395&workId=24875828&answerId=52094560&standardEnc=eba6203955dc26a6781808c842f604b7&enc=efc4c54fbc52fb7e756541b1331f72d2"
        print(f"未指定URL或文件，使用默认URL: {default_url}")
        submitter.parse_homework_page(url=default_url)

    # 保存作业信息到文件
    if args.output:
        try:
            with open(args.output, "w", encoding="utf-8") as f:
                json.dump(submitter.homework_info, f, ensure_ascii=False, indent=2)
            print(f"已保存作业信息到: {args.output}")
        except Exception as e:
            print(f"保存作业信息失败: {e}")

    # 生成答案
    answers = submitter.generate_answers()

    # 保存答案到超星学习通
    if answers:
        submitter.save_answers(answers)
    else:
        print("未生成答案，无法保存")


if __name__ == "__main__":
    main()
