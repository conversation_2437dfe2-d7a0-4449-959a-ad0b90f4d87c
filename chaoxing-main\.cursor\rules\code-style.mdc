---
description: 
globs: 
alwaysApply: false
---
# 代码风格规则

## 命名规范

### 变量和函数命名
- 使用小写字母，单词之间用下划线分隔（snake_case）
- 变量名应该具有描述性，避免使用单字母变量名（除了循环计数器）

```python
# 推荐
user_name = "John"
course_list = []

def get_course_info():
    pass

# 不推荐
userName = "John"
cl = []

def GetCourseInfo():
    pass
```

### 类命名
- 使用驼峰命名法（CamelCase）
- 类名应该是名词或名词短语

```python
# 推荐
class CourseManager:
    pass

# 不推荐
class manage_course:
    pass
```

### 常量命名
- 使用全大写字母，单词之间用下划线分隔
- 常量应该定义在模块或类的顶部

```python
MAX_RETRY_COUNT = 3
DEFAULT_TIMEOUT = 60
```

## 代码格式

### 缩进
- 使用4个空格进行缩进，不使用制表符

### 行长度
- 每行代码不超过79个字符
- 对于长表达式，使用括号进行换行

```python
# 推荐
result = (variable_one + variable_two + variable_three +
          variable_four)

# 不推荐
result = variable_one + variable_two + variable_three + variable_four
```

### 空行
- 顶级函数和类之间用两个空行分隔
- 类内的方法之间用一个空行分隔
- 相关的功能组可以用额外的空行分组

### 导入
- 导入应该分组：标准库、第三方库、本地应用/库
- 每组之间用一个空行分隔
- 每个导入应该单独一行

## 注释规范

### 文档字符串
- 所有公共模块、函数、类和方法都应该有文档字符串
- 使用三重双引号（"""）
- 第一行应该是一行摘要，后面跟一个空行和详细描述

```python
def get_course_list():
    """获取课程列表。
    
    从服务器获取当前用户的所有课程列表，并返回课程对象列表。
    
    Returns:
        list: 课程对象列表
    """
    pass
```

### 行内注释
- 行内注释应该和代码至少相隔两个空格
- 注释应该以大写字母开头，除非是变量名等特殊情况

```python
x = x + 1  # 增加计数器
```

## 其他规范

### 异常处理
- 使用具体的异常类型而不是捕获所有异常
- 异常处理块应该尽可能小

```python
# 推荐
try:
    value = json.loads(data)
except json.JSONDecodeError:
    logger.error("JSON解析错误")

# 不推荐
try:
    value = json.loads(data)
    process_data(value)
except Exception as e:
    logger.error(f"错误: {e}")
```

### 布尔表达式
- 避免使用 `==` 或 `!=` 与 `True` 或 `False` 比较

```python
# 推荐
if is_valid:
    pass

# 不推荐
if is_valid == True:
    pass
```

### 返回语句
- 函数应该有明确的返回值
- 避免在函数中间使用多个返回点，除非能提高代码可读性

### 字符串格式化
- 优先使用 f-strings 进行字符串格式化

```python
name = "John"
age = 30

# 推荐
message = f"Name: {name}, Age: {age}"

# 不推荐
message = "Name: %s, Age: %d" % (name, age)
message = "Name: {}, Age: {}".format(name, age)
```

