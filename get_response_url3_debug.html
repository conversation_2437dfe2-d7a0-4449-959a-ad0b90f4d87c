<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<script>
    _HOST_ = "//mooc1.chaoxing.com";
    _CP_ = "/exam-ans";
    _HOST_CP1_ = "//mooc1.chaoxing.com/exam-ans";
    // _HOST_CP2_ = _HOST_ + _CP_;
    _HOST_CP2_ = _CP_;
</script><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>提示</title>
<link href="//mooc1.chaoxing.com/exam-ans/mooc2/css/common.css?v=2025-0424-1038" type="text/css" rel="stylesheet">
<script type="text/javascript">
  function back() {
	var backUrl = '';
	if (backUrl && backUrl != '') {
		window.location.href = backUrl;
	} else {
		window.history.go(-1);
	}
 }
</script>
</head>
<body style="background:#fff">
<div>
    <div class="nullMain">
        <div class="null_wid700 clearfix">
            <div class="null_img fl"><img width="100%" height="100%" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/mull_pic.png" /></div>
            <div class="fr word_null">
                <p>无权限访问！</p>
				            </div>
        </div>
    </div>
</div>
</body>
</html>