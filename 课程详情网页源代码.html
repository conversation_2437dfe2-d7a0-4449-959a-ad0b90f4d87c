<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>
        工程测量
    </title>
    <link href="/mooc2-ans/style/common.css?v=2025-0527-1457" type="text/css" rel="stylesheet">
    <link href="/mooc2-ans/style/style.css?v=2022-0216-1357" type="text/css" rel="stylesheet">
    <link href="//mooc-res2.chaoxing.com/mooc2-ans/style/pop.css?v=2023-0608-1100" type="text/css" rel="stylesheet">
    <link href="/mooc2-ans/css/course/coursenotices.css?v=2022-1115-1003" type="text/css" rel="stylesheet">
    <link href="/mooc2-ans/css/course/learningCommitment.css" type="text/css" rel="stylesheet">
    <link href="/mooc2-ans/style/microCourseNav.css?v=2024-0529" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="/mooc2-ans/js/ServerHost.js?2020-0923-1630"></script>

    <style type="text/css">
        a {
            text-decoration: none;
            ;
            white-space: nowrap;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
            overflow: hidden
        }

        .fixed-right-btm {
            position: fixed;
            right: 36px;
            bottom: 94px;
        }

        #kf {
            display: block;
            padding-right: 0px;
            height: 40px;
            width: 40px;
            background: #fff url(/mooc2-ans/images/courselist/kfIcon.png) no-repeat 8px center;
            padding-left: 0px;
            border-radius: 4px;
            box-shadow: 0 1px 7px 0 rgba(198, 204, 217, 0.58);
        }

        .kfHover {
            position: absolute;
            right: 50px;
            bottom: 0px;
            width: 82px;
            height: 40px;
            background: rgba(24, 30, 51, 0.80);
            border-radius: 4px;
            margin-left: -28px;
            opacity: 0;
            visibility: hidden;
            -webkit-transition: opacity 0.3s 1s, visibility 0s 0.3s;
            transition: opacity 0.3s 1s, visibility 0s 0.3s;
            transition: opacity 0.3s 1s, visibility 0s 0.3s;
            text-align: center;
            line-height: 40px;
            color: #fff;
        }

        .kfDiv:hover .kfHover {
            display: block;
            opacity: 1;
            visibility: visible;
            -webkit-transition: opacity 0.3s 1s, visibility 0.3s 1s;
            transition: opacity 0.3s 1s, visibility 0.3s 1s;
        }

        .kfHover i {
            display: block;
            width: 0px;
            height: 0px;
            border: solid rgba(24, 30, 51, 0.80) 4px;
            position: absolute;
            right: -8px;
            top: 16px;
            bottom: auto;
            border-color: rgba(24, 30, 51, 0.80) transparent transparent transparent;
            transform: rotate(-90deg);
            -moz-transform: rotate(-90deg);
            -ms-transform: rotate(-90deg);
            -o-transform: rotate(-90deg);
        }

        .kfHover i.active {
            display: block;
            width: 0px;
            height: 0px;
            border: solid rgba(24, 30, 51, 0.80) 4px;
            position: absolute;
            left: 50%;
            margin-left: -4px;
            bottom: 40px;
            top: auto;
            border-color: transparent transparent rgba(24, 30, 51, 0.80) transparent;
        }


        .top-tips {
            margin-top: -20px;
            margin-left: 130px;
            display: inline-block;
            margin-bottom: 14px;
            position: relative;
            padding: 0 14px;
            height: 34px;
            background: #fdf6eb;
            line-height: 34px;
            border-radius: 21px;
            font-size: 0;
            z-index: 10;
            max-width: calc(100% - 400px);
        }

        .top-tips .warn-img {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
            width: 16px;
            height: 16px;
            background: url("/mooc2-ans/images/icon-40-fail.png") no-repeat center center;
            background-size: contain;
        }

        .top-tips .warn-txt {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
            font-size: 14px;
            color: #a18a66;
        }

        .top-tips .top-close {
            cursor: pointer;
            display: inline-block;
            vertical-align: middle;
            font-size: 14px;
            color: #3A8BFF;
        }

        .top-tips .warn-txt {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
            font-size: 14px;
            color: #a18a66;
            white-space: nowrap;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
            overflow: hidden;
            max-width: calc(100% - 40px);
        }

        .nav-content ul li a {
            display: flex;
            align-items: center;
            justify-content: left;
            line-height: 20px;
        }

        .nav_content {
            max-width: 95px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
        }

        @media screen and (max-width: 1300px) {
            .top-tips {
                max-width: 800px
            }

            .top-tips .warn-txt {
                max-width: 750px
            }
        }

        .Header2 {
            width: 100%;
            height: 52px;
            background: #FFFFFF;
            box-shadow: 0 1px 12px 0 rgba(1, 39, 78, 0.12);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 4;
            padding: 0 30px;
            box-sizing: border-box;
        }

        /*.Logo2{height:40px;margin-top:6px;}*/
        .Logo2 img {
            height: 100%;
            float: left
        }

        .Logo2 {
            height: 40px;
            margin-top: 6px;
            display: flex;
            align-items: center;
        }

        /*.Logo2 span {padding-left: 4px;color: #131B26;font-size: 18px;letter-spacing: 1px;float:left;line-height: 40px;}*/


        .xmicroHeader_handle {
            padding-top: 12px;
            display: flex;
            align-items: center;
        }

        .xmicroHeader_handle .xhandle {
            margin-left: 15px;
            height: 28px;
            padding: 0 12px;
            color: #3A8BFF;
            font-size: 14px;
            line-height: 26px;
            text-decoration: none;
            border: 1px solid #8CBDFF;
            border-radius: 18px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }

        .xmicroHeader_handle .xhandle.prev::before {
            margin-right: 2.5px;
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            background: url(/mooc2-ans/images/icon-arrow-prev.png) no-repeat center/ 100% 100%;
        }

        .xmicroHeader_handle .xhandle.next::after {
            margin-left: 2.5px;
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            background: url(/mooc2-ans/images/icon-arrow-next.png) no-repeat center/ 100% 100%;
        }

        .xwk_code {
            position: fixed;
            right: 36px;
            bottom: 137px;
            width: 40px;
            height: 40px;
            border-radius: 4px;
            background: #FFF url(/mooc2-ans/images/icon-code-gray.png) no-repeat center/ 16px 16px;
            box-shadow: 0 0 6px 0 #DDE3ED;
            cursor: pointer;
            z-index: 3
        }

        .xwk_code:hover {
            background: #3A8BFF url(/mooc2-ans/images/icon-code-white.png) no-repeat center/ 16px 16px;
        }

        .xwk_code:hover .xwk_code_pop {
            display: block;
        }

        .xwk_code_pop {
            display: none;
            position: absolute;
            top: -25px;
            right: calc(100% + 8px);
            width: max-content;
            padding: 8.9px 10px 7.85px;
            text-align: center;
            border-radius: 4px;
            box-sizing: border-box;
            background: #FFF;
            box-shadow: 0 0 6px 0 #DDE3ED;
        }

        .xwk_code_pop::before {
            content: '';
            display: block;
            position: absolute;
            right: -8px;
            top: 0;
            bottom: 0;
            width: 8px;
        }

        .xwk_code_pop .ximg {
            width: 90px;
            height: 90px;
        }

        .xwk_code_pop .xtext {
            padding-top: 6.25px;
            color: #474C59;
            font-size: 12px;
            line-height: 17px;
            text-align: center;
        }

        /* 新增样式 */
        .xpopTips {
            position: fixed;
            visibility: hidden;
            opacity: 0;
            max-width: 638px;
            z-index: 999;
            padding: 10px 16px;
            line-height: 17px;
            background: #181E33CC;
            font-size: 12px;
            color: #FFFFFF;
            border-radius: 4px;
            white-space: normal;
            -webkit-transition: all .3s linear;
            transition: all .3s linear;
        }

        .xpopTips::before {
            content: '';
            display: block;
            position: absolute;
            left: 38px;
            bottom: 100%;
            border-width: 7px;
            border-style: solid;
            border-color: transparent transparent #181E33CC transparent;
        }

        .xpopTips.top::before {
            top: 100%;
            bottom: initial;
            border-color: #181E33CC transparent transparent transparent;
        }
    </style>
</head>

<body>
    <input type="hidden" id="hideHead" value="0" />
    <div class="box">
        <script src='//3wfy-ans.chaoxing.com/passport/all-head-new.shtml?hideOldVersion=1' method="get"
            charset="utf-8"></script>



        <input type="hidden" id="microCourseTopicOpenType" value="0" />
        <input type="hidden" id="loadCourseNotice" value="0" />
        <input type="hidden" id="notAgreeCommitment" value="false" />
        <input type="hidden" id="courseid" value="*********" />
        <input type="hidden" id="clazzid" value="91297170" />
        <input type="hidden" id="cpi" value="*********" />
        <input type="hidden" id="microTopicId" value="0" />
        <input type="hidden" id="taskEngine" value="false" />
        <input type="hidden" id="topicModelId" value="0" />
        <input type="hidden" id="chapterId" value="0" />
        <input type="hidden" id="showAiAssistant" value="false" />
        <input type="hidden" id="showAIEnc" value="3cc339fffb8a652a90372e49f4f1ba74" />

        <div class="nav_side  ">
            <input type="hidden" id="isChaoxingClient" value="false" />
            <div class="sideCon" id="boxscrollleft">
                <dl class="classDl">
                    <dt>
                        <a href="javascript:void(0);" onclick="$('#tpsubmit').submit();">
                            <img width="100%" height="100%"
                                src="http://p.ananas.chaoxing.com/star3/138_78c/b34d7695e8fd9ce297ef09b2bcb31608.jpg" />
                            <span style="cursor: pointer;">课程门户<i class="left_join"></i></span>
                        </a>
                        <div class="wisdomBtn" style="display: none;"></div>
                    </dt>


                    <dd class="textHidden colorDeep" title="工程测量">
                        工程测量
                    </dd>
                </dl>
                <div class="nav-content   stuNavigationList">
                    <input type="hidden" id="courseid" name="courseid" value="*********" />
                    <input type="hidden" id="courseBelongSchoolId" value="2790" />
                    <input type="hidden" id="clazzid" name="clazzid" value="91297170" />
                    <input type="hidden" id="cfid" name="cfid" value="2790" />
                    <input type="hidden" id="bbsid" name="bbsid" value="bee20260b71f18a382f79be093c75781" />
                    <input type="hidden" id="cpi" name="cpi" value="*********" />

                    <input type="hidden" id="heardUt" name="heardUt" value="s" />
                    <input type="hidden" id="fid" name="fid" value="2790" />
                    <input type="hidden" id="openc" name="openc" value="7ab42b144b810dc81309302e53df2762" />
                    <input type="hidden" id="enc" name="enc" value="a2c8f215bab22eb1375ea3608d9cdded" />
                    <input type="hidden" id="oldenc" name="oldenc" value="4ef94d2e944bc52ba8101d7e4171c4f6" />
                    <input type="hidden" id="userId" name="userId" value="221880762">
                    <input type="hidden" id="moocDomainName" name="moocDomainName" value="https://mooc1.chaoxing.com">
                    <input type="hidden" id="courseApp" name="courseApp" value="0">
                    <input type="hidden" id="workEnc" name="workEnc" value="e02440de9464d66a3384103dceba890c">
                    <input type="hidden" id="examEnc" name="examEnc" value="98803ab5ceb66474cf7c6bb6bc6cbce4">
                    <input type="hidden" id="v" name="v" value="0">
                    <input type="hidden" id="t" name="t" value="1751634622342" />
                    <input type="hidden" id="bbsUrlSwitch" name="bbsUrlSwitch" value="true" />
                    <input type="hidden" id="learnSilverStartTime" name="learnSilverStartTime" value="" />
                    <input type="hidden" id="learnSilverEndTime" name="learnSilverEndTime" value="" />

                    <input type="hidden" id="courseEvaluateUrl" name="courseEvaluateUrl"
                        value="https://mooc1.chaoxing.com/coursestar?courseId=*********&clazzId=91297170&edit=false">

                    <ul>

                        <li openType="0" dataname="ai_workbench" pageHeader="21" id="nav_121335" data="121335"
                            shownewmark="0">
                            <a href="javascript:void(0);" title="AI助教"
                                data-url="https://mooc1.chaoxing.com/course-ans/ai/getStuAiWorkBench?courseId=*********&clazzId=91297170&cpi=*********&ut=s">
                                <img class="custom-icon"
                                    src="https://p.ananas.chaoxing.com/star3/origin/d9c08c99610e931b801d5f93278e52b1">
                                <span class="tag_new">
                                    <span class="nav_content">AI助教</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="hd" pageHeader="0" id="nav_9914" data="9914" shownewmark="0">
                            <a href="javascript:void(0);" title="任务"
                                data-url="https://mobilelearn.chaoxing.com/page/active/stuActiveList">
                                <i class="hd"></i>
                                <span class="tag_new">
                                    <span class="nav_content">任务</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="zj" pageHeader="1" id="nav_9917" data="9917" shownewmark="0">
                            <a href="javascript:void(0);" title="章节" data-url="/mooc2-ans/mycourse/studentcourse">
                                <i class="zj"></i>
                                <span class="tag_new">
                                    <span class="nav_content">章节</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="tl" pageHeader="2" id="nav_9918" data="9918" shownewmark="0">
                            <a href="javascript:void(0);" title="讨论"
                                data-url="https://groupweb.chaoxing.com/course/topic/topicList">
                                <i class="tl"></i>
                                <span class="tag_new">
                                    <span class="nav_content">讨论</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="zy" pageHeader="8" class="curNav" id="nav_9915" data="9915"
                            shownewmark="0">
                            <a href="javascript:void(0);" title="作业"
                                data-url="https://mooc1.chaoxing.com/mooc2/work/list">
                                <i class="zy"></i>
                                <span class="tag_new">
                                    <span class="nav_content">作业</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="ks" pageHeader="9" id="nav_9916" data="9916" shownewmark="0">
                            <a href="javascript:void(0);" title="考试"
                                data-url="https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list">
                                <i class="ks"></i>
                                <span class="tag_new">
                                    <span class="nav_content">考试</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="zl" pageHeader="3" id="nav_9919" data="9919" shownewmark="0">
                            <a href="javascript:void(0);" title="资料" data-url="/mooc2-ans/coursedata/stu-datalist">
                                <i class="zl"></i>
                                <span class="tag_new">
                                    <span class="nav_content">资料</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="ctj" pageHeader="4" id="nav_9920" data="9920" shownewmark="0">
                            <a href="javascript:void(0);" title="错题集" data-url="/mooc2-ans/wrongque/page">
                                <i class="ctj"></i>
                                <span class="tag_new">
                                    <span class="nav_content">错题集</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="cj" pageHeader="6" id="nav_9921" data="9921" shownewmark="0">
                            <a href="javascript:void(0);" title="学习记录"
                                data-url="https://stat2-ans.chaoxing.com/study-data/index">
                                <i class="cj"></i>
                                <span class="tag_new">
                                    <span class="nav_content">学习记录</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>

                        <li openType="0" dataname="zsd" pageHeader="12" id="nav_13306" data="13306" shownewmark="0">
                            <a href="javascript:void(0);" title="课程图谱"
                                data-url="https://stat2-ans.chaoxing.com/study-knowledge/index">
                                <i class="zsd"></i>
                                <span class="tag_new">
                                    <span class="nav_content">课程图谱</span>
                                    <b class="nvaNewMark" style="display: none;">NEW</b>
                                </span>
                            </a>
                        </li>
                    </ul>

                </div>

            </div>
            <form id="tpsubmit" action="https://mooc1.chaoxing.com/course/*********.html?clazzId=91297170"
                target="_blank" method="post">
            </form>
        </div>
        <div class="fixed-right-btm" style="z-index:3;">
            <div class="kfDiv">
                <a id="kf"
                    href="https://robot.chaoxing.com/chat?unitId=25518&robotId=ad9c9be3fade4280bd71d6389c06be96&referUrl=mooc.chaoxing.com"
                    target="_blank"></a>
                <div class="kfHover">
                    <i></i>
                    在线客服
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="oldVersionFlag" value="true" />

    <div class="maskDiv coursenoticepop" style="display:none;">
        <!-- 课程须知弹窗 -->
    </div>
    <div class="maskDiv maskFadeOut" id="showCommitmentId" style="display:none">
        <!-- 在线学习诚信承诺书弹窗 -->
        <div class="popDiv course-pop" style="display: block">
            <div class="popHead">
                <p class="fl fs18 colorDeep"></p>
            </div>
            <div id="learnCommit-pop-tabindex" tabindex="0" role="dialog" aria-label="在线学习诚信承诺书" class="het60"></div>
            <div class="commitment-content-dialog">
                <div class="commitment-details" style="padding: 24px 30px 30px;height: 420px;overflow: auto">
                    <h2 tabindex="0" role="option">在线学习诚信承诺书</h2>
                    <div class="dec">
                        <p class="p-style1" style="text-indent: 2em;" tabindex="0" role="option">
                            根据《教育部等五部门关于加强普通高等学校在线开放课程教学管理的若干意见》（教高〔2022〕1号，以下简称《意见》）要求，高校学生选修在线课程应签署在线学习诚信承诺书，遵守课程学习纪律和考试纪律。
                        </p>
                        <p class="p-style1" style="text-indent: 2em;" tabindex="0" role="option">
                            我承诺：在个人学习过程中，遵守《意见》和学校相关管理规定，遵守课程学习纪律和考试纪律，诚信学习。不出借个人学习账号给他人使用；不进行通过非法软件或委托第三方提供的人工或技术服务等方式获取学习记录和考试成绩的“刷课”“替课”“刷考”“替考”行为；不以任何形式传播课程考试内容及答案。
                        </p>
                        <p class="p-style1" style="text-indent: 2em;" tabindex="0" role="option">
                            我已知晓：根据《意见》要求，平台将对学生学习过程进行监控，运用人工智能、大数据等技术，依法依规对学生身份认证、讨论记录、学习数据实施监控，识别“刷课”“替课”“刷考”“替考”行为。并根据高校教学需求，对违规违纪学习行为予以记录并通报学生所在高校。违规违纪行为一经查实，学校可根据本校学生管理规定、学生纪律处分管理规定等，取消违规违纪学生课程成绩，视情节给予警告、严重警告、记过、留校察看、开除学籍等相应处分，并记入学生档案。对参与组织“刷课”“替课”“刷考”“替考”并构成违法行为的学生，由有关部门依法追究法律责任。
                        </p>
                        <p class="p-style3" tabindex="0" role="option">
                            承诺人: <br>
                            日期:
                        </p>
                    </div>
                </div>
                <div style="height: 70px;"></div>
                <div class="popBottom">
                    <label class="pCheckbox_label fl fs14 colorIn">
                        <span class="ipt-checkbox fl">
                            <input type="checkbox" class="agreeButton" id="learnCommit-bottom-div">
                            <i class="icon-checkbox"></i>
                        </span>
                        <span>我已阅读, 开始学习</span>
                    </label>
                    <a href="javascript:" class="jb_btn jb_btn_92_disable fr fs14 agreeStartDisable"
                        tabindex="-1">开始学习</a>
                    <a href="javascript:" class="jb_btn jb_btn_92 fr fs14 agreeStart" style="display:none">开始学习</a>
                </div>
            </div>
        </div>
    </div>
    <script>
    </script>
    <script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc2-ans/js/common/jquery.min.js"></script>
    <script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc2-ans/js/common/jquery.nicescroll.min.js"></script>
    <script type="text/javascript" src="/mooc2-ans/js/common.js?v=2023-0606-1826"></script>
    <script type="text/javascript" src="/mooc2-ans/js/backtop.min.js?v=2023-0706-0942" language="javascript"></script>
    <script type="text/javascript" src="/mooc2-ans/js/contentLoader.js?v=2024-0702-1507"></script>
    <script type="text/javascript" src="/mooc2-ans/js/course-stu.js?v=2025-0620-1014"></script>
    <script type="text/javascript"
        src="//mooc-res2.chaoxing.com/mooc2-ans/js/common/poptoast.js?v=2023-0707-0033"></script>
    <script type="text/javascript" src="/mooc2-ans/static/js/domain.js"></script>
    <script type="text/javascript">

        if ("0" == "1") {
            window.onload = function () {
                // 无障碍点击菜单,读取已访问实现
                $("#boxscrollleft .stuNavigationList ul li a").click(function () {
                    var labelName = $(this).attr("aria-label");
                    if (!labelName || labelName.indexOf("已访问") == -1) {
                        $(this).attr("aria-label", "已访问");
                    }
                });

                $("#boxscrollleft .stuNavigationList ul li a").keydown(function (event) {
                    if (event.keyCode == 9 && !event.shiftKey) {
                        if ($(this.parentElement).attr('dataname') === 'cj') {
                            $("#boxscrollleft").focus();
                        }
                    }
                })


                // 无障碍 聚焦点击过的菜单选项
                window.addEventListener("message", function (event) {
                    let data = event.data;
                    if (!data) return;
                    var msgJson = JSON.parse(event.data);
                    if (msgJson.type === "nav_stu_top_focus") {
                        $("#boxscrollleft .stuNavigationList ul li.curNav a").focus();
                    }
                });
            };
        }


        function toOveralScore() {
            var url = 'https://stat2-ans.chaoxing.com' + "/stat2/overall-score/stu-score?courseid=" + "*********" + "&cpi=" + "*********" + "&clazzid=" + "91297170" + "&ut=s";
            window.open(url);
        }
        popTips(".Logo2 span");
        function popTips(userName) {
            $(userName).map(function () {
                if (this.offsetWidth < this.scrollWidth) {
                    $(this).append('<div class="xpopTips">' + $(this).text() + '</div>')
                } else if (this.offsetHeight < this.scrollHeight) {
                    $(this).append('<div class="xpopTips">' + $(this).text() + '</div>')
                }
            })
            $(userName).on('mouseenter', function () {
                const thisTips = $(this).find('.xpopTips'),
                    divH = thisTips.innerHeight(),
                    docHei = $(document).height(),
                    rect = this.getBoundingClientRect()

                thisTips.css({ left: rect.left })
                if (rect.bottom + divH + 10 >= docHei) {
                    thisTips.css({ top: rect.top - divH - 10 })
                    thisTips.addClass('top')
                } else {
                    thisTips.css({ top: rect.bottom + 10 })
                    thisTips.removeClass('top')
                }

                $(this).find('.xpopTips').show()
                $(this).find('.xpopTips').css({ visibility: 'visible', opacity: '1' })
            })
            $(userName).on('mouseleave', function () {
                $(this).find('.xpopTips').hide()
                $(this).find('.xpopTips').css({ visibility: 'hidden', opacity: '0' })
            })
            $(document).on('scroll', function () {
                $(this).find('.xpopTips').hide()
            })
        }
        function closeWindow() {
            window.opener = null;
            window.open('', '_self');
            window.close();
        }
    </script>

</body>

</html>