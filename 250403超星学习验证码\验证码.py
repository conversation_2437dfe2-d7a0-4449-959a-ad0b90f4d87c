"""
[课程内容]:Python实现某学习平台的滑块验证码自动识别与破解
[授课老师]: 青灯教育-自游   [授课时间]: 20:05 可以点歌  可以问问题

[环境使用]:
    Python 3.10
    Pycharm

[模块使用]:
    requests -> pip install requests
    execjs -> pip install pyexecjs
    ddddocr -> pip install ddddocr
    time
    re
win + R 输入cmd 输入安装命令 pip install 模块名 (如果你觉得安装速度比较慢, 你可以切换国内镜像源)
先听一下歌 等一下后面进来的同学, 20:05正式开始讲课 [有什么喜欢听得歌曲 也可以在公屏发一下]
相对应的安装包/安装教程/激活码/使用教程/学习资料/工具插件 可以加木子老师微信 python10080

"""
# 导入数据请求模块
import requests
# 导入时间模块
import time
# 导入编译js代码模块
import execjs
# 导入正则表达式模块
import re
# 导入验证码识别模块
import ddddocr
# 导入系统模块
import os
# 导入JSON模块
import json

"""发送请求"""
# 模拟浏览器
headers = {
    'referer':'https://v8.chaoxing.com/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

# 检查Node.js环境
try:
    # 设置execjs的运行环境
    os.environ["EXECJS_RUNTIME"] = "Node"
    
    # 请求网址
    url = 'https://captcha.chaoxing.com/captcha/get/verification/image'
    """加密参数获取"""
    # 获取当前时间戳
    t = int(time.time() * 1000)
    
    # 读取JS文件内容
    with open('yzm.js', encoding='utf-8') as f:
        js_code = f.read()
    
    # 编译js代码
    ctx = execjs.compile(js_code)
    # 调用js加密函数
    r = ctx.call('GetSign', t)
    print(r)
    
    # 查询参数
    params = {
        'callback': 'cx_captcha_function',
        'captchaId': 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv',
        'type': 'slide',
        'version': '1.1.20',
        'captchaKey': r['captchaKey'],
        'token': r['token'],
        'referer': 'https://v8.chaoxing.com/',
        'iv': r['IV'],
        '_': t,
    }
    
    response = requests.get(url=url, params=params, headers=headers)
    """获取数据"""
    # 获取响应的文本数据
    print(response.text)
    """解析数据"""
    # 提取验证图片 shadeImage(缺口图) cutoutImage(滑块图)
    shadeImage, cutoutImage = re.findall('"shadeImage":"(.*?)","cutoutImage":"(.*?)"', response.text)[0]
    # 提取token
    token = re.findall('"token":"(.*?)"', response.text)[0]
    """验证识别"""
    # 获取缺口图片内容
    shadeImage_content = requests.get(url=shadeImage, headers=headers).content
    with open('bg.jpg', 'wb') as f:
        f.write(shadeImage_content)
    # 获取滑块图片内容
    cutoutImage_content = requests.get(url=cutoutImage, headers=headers).content
    with open('fg.jpg', 'wb') as f:
        f.write(cutoutImage_content)
    # 实例化对象
    det = ddddocr.DdddOcr(det=False, ocr=True)
    # 验证图片识别
    res = det.slide_match(shadeImage_content, cutoutImage_content, simple_target=True)
    # 获取x轴距离
    x = res['target'][0]
    print(res)
    # 验证链接
    link = 'https://captcha.chaoxing.com/captcha/check/verification/result'
    _ = int(time.time() * 1000)
    # 请求参数
    link_data = {
        'callback': 'cx_captcha_function',
        'captchaId': 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv',
        'type': 'slide',
        'token': token,
        'textClickArr': '[{"x":%d}]' % x,
        'coordinate': '[]',
        'runEnv': '10',
        'version': '1.1.20',
        't': 'a',
        'iv': 'dcac328825997752b5099050a3b4c9ea',
        '_': _,
    }
    print(link_data)
    link_response = requests.get(url=link, params=link_data, headers=headers)
    print(link_response)
    print(link_response.text)

except Exception as e:
    print(f"执行出错: {e}")
    print("请确保已安装Node.js并执行以下命令安装crypto-js模块:")
    print("npm install crypto-js")