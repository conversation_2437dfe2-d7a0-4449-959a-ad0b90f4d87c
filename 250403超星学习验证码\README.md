# 超星学习通验证码处理模块

这个项目提供了超星学习通平台滑块验证码的自动识别与处理功能，可以集成到学习通自动化系统中。

## 功能特点

- 自动获取滑块验证码图片
- 使用ddddocr识别滑块位置
- 自动提交验证结果
- 完整的错误处理和日志记录
- 支持会话复用和多次尝试

## 文件说明

- `验证码.py` - 原始验证码处理脚本（依赖Node.js）
- `验证码_python版.py` - 纯Python实现的验证码处理脚本
- `CaptchaHandler.py` - 可集成到自动化系统的验证码处理模块
- `yzm.js` - JavaScript加密函数（仅供参考，Python版本不需要）

## 环境要求

- Python 3.8+
- 依赖库：
  - requests
  - ddddocr
  - loguru
  - 原始版本还需要：pyexecjs, Node.js, crypto-js

## 安装依赖

```bash
pip install requests ddddocr loguru
```

如果使用原始版本（依赖Node.js），还需要：

```bash
pip install pyexecjs
npm install crypto-js
```

## 使用方法

### 1. 作为独立脚本使用

直接运行`验证码_python版.py`：

```bash
python 验证码_python版.py
```

### 2. 作为模块集成到自动化系统

```python
from CaptchaHandler import CaptchaHandler
from loguru import logger

# 配置日志
logger.remove()
logger.add("captcha.log", rotation="10 MB", level="INFO")

# 创建验证码处理器
captcha_handler = CaptchaHandler()

# 解决验证码
result = captcha_handler.solve_captcha()
if result:
    print("验证码处理成功")
else:
    print("验证码处理失败")
```

### 3. 与现有会话集成

```python
import requests
from CaptchaHandler import CaptchaHandler

# 创建会话
session = requests.Session()

# 使用现有会话创建验证码处理器
captcha_handler = CaptchaHandler(session=session)

# 解决验证码
captcha_handler.solve_captcha()

# 继续使用会话进行其他操作
# ...
```

## 常见问题解决

### 1. ddddocr安装问题

如果安装ddddocr时遇到问题，可以尝试：

```bash
pip install ddddocr -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 识别准确率问题

如果识别准确率不高，可以尝试调整识别参数或保存图片进行分析：

```python
# 保存图片进行分析
with open('debug_bg.jpg', 'wb') as f:
    f.write(shade_image_content)
with open('debug_fg.jpg', 'wb') as f:
    f.write(cutout_image_content)
```

### 3. 验证码处理失败

如果验证码处理失败，可能的原因包括：

- 网络连接问题
- 验证码规则变更
- 服务器限制请求频率

可以尝试增加重试次数或添加随机延时：

```python
# 增加重试次数和延时
result = captcha_handler.solve_captcha(max_attempts=5)
```

## 注意事项

- 本模块仅供学习和研究使用
- 过于频繁的请求可能导致IP被临时封禁
- 学习通可能会更新验证码机制，需要及时更新代码

## 集成到自动化系统

在学习通自动化系统中，可以在需要处理验证码的地方调用此模块：

```python
from CaptchaHandler import CaptchaHandler

class Session:
    def __init__(self):
        self.session = requests.Session()
        self.captcha_handler = CaptchaHandler(session=self.session)
        
    def handle_captcha_challenge(self):
        """处理可能出现的验证码挑战"""
        return self.captcha_handler.solve_captcha()
        
    def login(self, username, password):
        # 登录过程中如果遇到验证码
        if "验证码" in response.text:
            if not self.handle_captcha_challenge():
                return False
            # 重新提交登录请求
            # ...
``` 