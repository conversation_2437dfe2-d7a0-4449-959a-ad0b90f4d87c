import requests

url = "https://mooc1.chaoxing.com/mooc2/work/list?courseId=228836418&classId=64009643&cpi=282371395&ut=s&t=1751522271879&stuenc=6596eb99d03ae8a9c824e81f53e86f1a&enc=550114ba5f5bf0c61d3f650fe04350da"

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'sec-ch-ua-platform': "\"Windows\"",
  'Upgrade-Insecure-Requests': "1",
  'Sec-Fetch-Site': "cross-site",
  'Sec-Fetch-Mode': "navigate",
  'Sec-Fetch-User': "?1",
  'Sec-Fetch-Dest': "document",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "fid=2790; k8s=1751517388.223.15092.276767; route=0a65fa708818ad1416475328b69707fd; jrose=6D5E368EFB18EA559AF586C335FADF3D.mooc-2235649221-zbgdw; source=\"\"; _uid=221880762; _d=1751523025429; UID=221880762; vc3=I%2FDdknSDjoR%2BGtSYlcz99wt9xoPQViDuTY5qq55ko1WHlJXKXnYkcMxYG1jKK%2B0dMmHCTYD2FmUvnXBG1EUqKbYEOc%2B%2FjbRxFulZ0IOqQmIhmLSCSMvUYyF11I46AFR6%2FYdE9rTyQX5Zl5hKBlUOkU4RMFuWnf0Kg6yqQ0YMIG4%3D1a6df10f4597e6ca8f572672995af856; uf=da0883eb5260151ed5cd77328d409813eb3dd150f62496a8c8dcfef3f8d40f995356231313def97dee6fc43cf69acaf981a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba0790730943c5c914713028f1ec42bf71b1188854805578cc36add5faa65b01e5f7a43f1910accd7f77c68b175ae3ddc8537193898c32bcd0bb8ea04bf8df79c54df7ff280fcb29d10d8a4c92b12beb4b44156174a80459813047690eaabee3c2560d457d8c24c6dfe7fafd565af53bf2; cx_p_token=2bd43cd8413cd2258f00687c6e0bda13; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyMjE4ODA3NjIiLCJsb2dpblRpbWUiOjE3NTE1MjMwMjU0MzAsImV4cCI6MTc1MjEyNzgyNX0.jvklI28t9jWCQb4AHe7Rpx_fBOU4mDdjF0PdkPrxBR8; xxtenc=c4885b4f5d02c8246c0260b0959ca202; DSSTASH_LOG=C_38-UN_1612-US_221880762-T_1751523025430; spaceFid=2790; spaceRoleId=3; tl=1"
}

response = requests.get(url, headers=headers)

print(response.text)