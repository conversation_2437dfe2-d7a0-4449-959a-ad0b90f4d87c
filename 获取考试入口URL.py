#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通考试入口URL获取脚本
基于章节测验.py，专门用于批量获取课程中所有考试的入口链接
支持导出到CSV和JSON格式

使用方法：
1. 基本使用：python 获取考试入口URL.py -u 用户名 -p 密码
2. 指定课程：python 获取考试入口URL.py -u 用户名 -p 密码 --course-id xxx --clazz-id xxx --person-id xxx
3. 导出格式：python 获取考试入口URL.py -u 用户名 -p 密码 --format csv
"""

import sys
import os
import json
import time
import argparse
import re
import csv
from datetime import datetime
from chaoxing_homework_manager import ChaoxingHomeworkManager

def get_exam_list_html(manager, course_id, clazz_id, person_id):
    """
    获取考试列表HTML（复用章节测验.py的逻辑）
    """
    try:
        # 生成时间戳
        timestamp = int(time.time() * 1000)
        
        # 构建考试列表URL
        url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={timestamp}"
        
        # 获取enc参数
        enc_info = manager.get_course_enc_for_exam(course_id, clazz_id, person_id)
        if enc_info:
            if enc_info.get("stuenc"):
                url += f"&stuenc={enc_info['stuenc']}"
            if enc_info.get("enc"):
                url += f"&enc={enc_info['enc']}"
            if enc_info.get("openc"):
                url += f"&openc={enc_info['openc']}"
        
        # 设置请求头
        headers = manager.base_headers.copy()
        headers["Referer"] = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"
        
        print(f"正在获取考试列表: {url}")
        
        # 发送请求
        response = manager.session.get(url, headers=headers, verify=False)
        
        if response.status_code != 200:
            print(f"获取考试列表失败，状态码: {response.status_code}")
            return None
        
        return response.text
        
    except Exception as e:
        print(f"获取考试列表HTML时出错: {e}")
        return None

def parse_exam_list_html(html, course_id, clazz_id, person_id):
    """
    解析考试列表HTML（复用章节测验.py的逻辑）
    """
    try:
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(html, "html.parser")
        
        # 获取考试列表项
        exam_items = soup.select(".bottomList li")
        
        if not exam_items:
            exam_items = soup.select(".examListItem")
        
        if not exam_items:
            exam_items = soup.select("li:has(div.tag.icon-exam)")
        
        exams = []
        for item in exam_items:
            try:
                # 获取点击div元素
                div_element = item.select_one("div[onclick^='goTest']")
                
                if not div_element:
                    div_element = item.select_one("[onclick^='goTest']")
                
                if not div_element:
                    div_element = item.find(lambda tag: tag.has_attr('onclick') and 'goTest' in tag['onclick'])
                
                if not div_element:
                    # 尝试直接从页面源码中提取goTest函数调用
                    onclick_match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", str(item))
                    if onclick_match:
                        # 获取考试标题
                        title_elem = item.select_one(".overHidden2")
                        if not title_elem:
                            title_elem = item.select_one("p:first-child")
                        title = title_elem.text.strip() if title_elem else "未知考试"
                        
                        # 获取考试状态
                        status_elem = item.select_one(".status")
                        status = status_elem.text.strip() if status_elem else "未知状态"
                        
                        exam_info = {
                            "title": title,
                            "status": status,
                            "course_id": course_id,
                            "clazz_id": clazz_id,
                            "cpi": person_id,
                            "exam_id": onclick_match.group(2),
                            "relation_id": onclick_match.group(3),
                            "enc": onclick_match.group(4),
                            "test_paper_id": onclick_match.group(5),
                            "is_retest": onclick_match.group(6),
                            "end_time": onclick_match.group(7)
                        }
                        exams.append(exam_info)
                        continue
                
                if div_element:
                    onclick = div_element.get("onclick", "")
                    
                    # 解析onclick属性中的goTest函数调用
                    match = re.search(r"goTest\('([^']+)',(\d+),(\d+),'([^']+)',(\d+),(false|true),'([^']+)'\)", onclick)
                    if match:
                        # 获取考试标题
                        title_elem = item.select_one(".overHidden2")
                        if not title_elem:
                            title_elem = item.select_one("p:first-child")
                        title = title_elem.text.strip() if title_elem else "未知考试"
                        
                        # 获取考试状态
                        status_elem = item.select_one(".status")
                        status = status_elem.text.strip() if status_elem else "未知状态"
                        
                        exam_info = {
                            "title": title,
                            "status": status,
                            "course_id": course_id,
                            "clazz_id": clazz_id,
                            "cpi": person_id,
                            "exam_id": match.group(2),
                            "relation_id": match.group(3),
                            "enc": match.group(4),
                            "test_paper_id": match.group(5),
                            "is_retest": match.group(6),
                            "end_time": match.group(7)
                        }
                        exams.append(exam_info)
                        
            except Exception as e:
                print(f"解析考试项时出错: {e}")
                continue
        
        return exams
        
    except Exception as e:
        print(f"解析考试列表HTML时出错: {e}")
        return []

def generate_exam_entrance_url(exam_info):
    """
    生成考试入口URL
    """
    try:
        # 构建考试入口URL
        entrance_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId={exam_info['course_id']}&classId={exam_info['clazz_id']}&examId={exam_info['exam_id']}&cpi={exam_info['cpi']}"
        
        return entrance_url
        
    except Exception as e:
        print(f"生成考试入口URL时出错: {e}")
        return None

def generate_exam_direct_url(exam_info):
    """
    生成考试直接URL（用于直接进入考试）
    """
    try:
        # 构建考试直接URL
        direct_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={exam_info['course_id']}&classId={exam_info['clazz_id']}&examId={exam_info['exam_id']}&examAnswerRelationId={exam_info['relation_id']}&endTime={exam_info['end_time']}&testPaperId={exam_info['test_paper_id']}&isRetest={exam_info['is_retest']}&enc={exam_info['enc']}"
        
        return direct_url
        
    except Exception as e:
        print(f"生成考试直接URL时出错: {e}")
        return None

def export_to_csv(exams, filename):
    """
    导出考试信息到CSV文件
    """
    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['考试标题', '考试状态', '考试ID', '入口URL', '直接URL', '课程ID', '班级ID', '个人ID']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for exam in exams:
                writer.writerow({
                    '考试标题': exam['title'],
                    '考试状态': exam['status'],
                    '考试ID': exam['exam_id'],
                    '入口URL': exam['entrance_url'],
                    '直接URL': exam['direct_url'],
                    '课程ID': exam['course_id'],
                    '班级ID': exam['clazz_id'],
                    '个人ID': exam['cpi']
                })
        
        print(f"考试信息已导出到CSV文件: {filename}")
        return True
        
    except Exception as e:
        print(f"导出CSV文件时出错: {e}")
        return False

def export_to_json(exams, filename):
    """
    导出考试信息到JSON文件
    """
    try:
        export_data = {
            "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_exams": len(exams),
            "exams": exams
        }

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

        print(f"考试信息已导出到JSON文件: {filename}")
        return True

    except Exception as e:
        print(f"导出JSON文件时出错: {e}")
        return False

def get_all_exam_urls(username, password, course_id=None, clazz_id=None, person_id=None):
    """
    获取所有考试的入口URL
    """
    # 创建作业管理器
    manager = ChaoxingHomeworkManager(username, password)

    # 登录
    print("正在登录学习通...")
    if not manager.login():
        print("登录失败，请检查账号密码")
        return None

    print("登录成功！")

    # 如果没有提供课程ID、班级ID和个人ID，则获取所有课程的考试列表
    if not course_id or not clazz_id or not person_id:
        # 获取课程列表
        print("正在获取课程列表...")
        courses = manager.get_course_list()
        if not courses:
            print("未找到课程")
            return None

        # 打印课程列表
        print(f"\n找到 {len(courses)} 门课程:")
        for i, course in enumerate(courses):
            print(f"{i+1}. {course['course_name']} - {course['teacher_name']}")

        # 选择课程
        try:
            course_index = int(input("\n请选择要获取考试URL的课程编号: ")) - 1
            if course_index < 0 or course_index >= len(courses):
                print("无效的课程编号")
                return None
        except ValueError:
            print("请输入有效的数字")
            return None

        selected_course = courses[course_index]
        course_id = selected_course["course_id"]
        clazz_id = selected_course["clazz_id"]
        person_id = selected_course["person_id"]

        print(f"\n已选择课程: {selected_course['course_name']}")
        print(f"课程ID: {course_id}")
        print(f"班级ID: {clazz_id}")
        print(f"个人ID: {person_id}")

    # 获取考试列表
    print("\n正在获取考试列表...")

    # 获取考试列表HTML
    exam_list_html = get_exam_list_html(manager, course_id, clazz_id, person_id)
    if not exam_list_html:
        print("获取考试列表失败")
        return None

    # 解析考试列表HTML
    exams = parse_exam_list_html(exam_list_html, course_id, clazz_id, person_id)

    # 如果解析HTML没有结果，尝试使用manager的get_exam_list方法
    if not exams:
        print("通过HTML解析获取考试列表失败，尝试通过API获取...")
        exams = manager.get_exam_list(course_id, clazz_id, person_id)

    if not exams:
        print("未找到考试")
        return None

    print(f"\n找到 {len(exams)} 个考试:")

    # 为每个考试生成URL
    for i, exam in enumerate(exams):
        print(f"\n{i+1}. {exam['title']} - {exam['status']}")

        # 生成入口URL
        entrance_url = generate_exam_entrance_url(exam)
        exam['entrance_url'] = entrance_url

        # 生成直接URL
        direct_url = generate_exam_direct_url(exam)
        exam['direct_url'] = direct_url

        print(f"   考试ID: {exam['exam_id']}")
        print(f"   入口URL: {entrance_url}")
        print(f"   直接URL: {direct_url}")

    return exams

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="超星学习通考试入口URL获取脚本")
    parser.add_argument("-u", "--username", help="超星学习通用户名")
    parser.add_argument("-p", "--password", help="超星学习通密码")
    parser.add_argument("--course-id", help="课程ID")
    parser.add_argument("--clazz-id", help="班级ID")
    parser.add_argument("--person-id", help="个人ID")
    parser.add_argument("--format", choices=['csv', 'json', 'both'], default='both', help="导出格式")
    parser.add_argument("--output", help="输出文件名前缀（不包含扩展名）")

    args = parser.parse_args()

    # 如果没有提供用户名和密码，则提示输入
    username = args.username
    password = args.password

    if not username:
        username = input("请输入超星学习通用户名: ")

    if not password:
        password = input("请输入超星学习通密码: ")

    if not username or not password:
        print("用户名和密码不能为空")
        return

    # 获取考试URL
    exams = get_all_exam_urls(username, password, args.course_id, args.clazz_id, args.person_id)

    if not exams:
        print("未获取到考试信息")
        return

    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_prefix = args.output if args.output else f"考试入口URL_{timestamp}"

    # 导出结果
    print(f"\n正在导出考试信息...")

    if args.format in ['csv', 'both']:
        csv_filename = f"{output_prefix}.csv"
        export_to_csv(exams, csv_filename)

    if args.format in ['json', 'both']:
        json_filename = f"{output_prefix}.json"
        export_to_json(exams, json_filename)

    print(f"\n✓ 成功获取 {len(exams)} 个考试的入口URL")
    print("✓ 结果已导出到文件")

    # 显示汇总信息
    print(f"\n考试汇总:")
    for exam in exams:
        print(f"- {exam['title']} ({exam['status']})")

if __name__ == "__main__":
    main()
