#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通作业参数获取工具
用于获取所有未完成作业的参数，格式为"课程ID|班级ID|个人ID|作业ID|答案ID|enc参数"
"""

import sys
import os
import time
import re
import argparse
from batch_fill_homework import get_all_homeworks_with_credentials


def main():
    """
    主函数，处理命令行参数并执行相应操作
    """
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="超星学习通作业参数获取工具")

    # 添加命令行参数
    parser.add_argument("-u", "--username", help="超星学习通账号")
    parser.add_argument("-p", "--password", help="超星学习通密码")
    parser.add_argument(
        "-t", "--threads", type=int, default=3, help="并发线程数 (默认: 3)"
    )
    parser.add_argument(
        "--include-finished", action="store_true", help="包含已完成的作业"
    )
    parser.add_argument(
        "--include-ended-courses", action="store_true", help="包含已结束的课程"
    )
    parser.add_argument("--with-title", action="store_true", help="显示作业标题")
    parser.add_argument("--with-status", action="store_true", help="显示作业状态")

    # 解析命令行参数
    args = parser.parse_args()

    # 如果没有提供账号密码，则交互式获取
    username = args.username
    password = args.password

    if not username:
        username = input("请输入超星学习通账号: ")

    if not password:
        import getpass

        password = getpass.getpass("请输入超星学习通密码: ")

    try:
        # 获取所有作业列表，使用安静模式
        all_homeworks = get_all_homeworks_with_credentials(
            username,
            password,
            max_workers=args.threads,
            retry_count=5,
            retry_delay=10,
            include_finished=args.include_finished,
            include_ended_courses=args.include_ended_courses,
        )

        if all_homeworks:
            # 输出作业参数
            for course_name, homeworks in all_homeworks.items():
                if homeworks:  # 只显示有作业的课程
                    for homework in homeworks:
                        title = homework.get("title", "未知作业")
                        status = homework.get("status", "未知状态")
                        link = homework.get("link", "")
                        work_id = homework.get("work_id", "")
                        answer_id = homework.get("answer_id", "")
                        course_id = homework.get("course_id", "")
                        clazz_id = homework.get("clazz_id", "")
                        cpi = homework.get("cpi", "")

                        # 从链接中提取enc参数
                        enc = ""
                        if link:
                            enc_match = re.search(r"enc=([^&]+)", link)
                            if enc_match:
                                enc = enc_match.group(1)

                        # 输出作业参数格式
                        if course_id and clazz_id and cpi and work_id and enc:
                            # 如果answer_id为0（未交作业），也输出
                            homework_params = f"{course_id}|{clazz_id}|{cpi}|{work_id}|{answer_id}|{enc}"

                            # 根据参数决定是否显示标题和状态
                            output = ""
                            if args.with_title:
                                output += f"{title}: "
                            if args.with_status:
                                output += f"[{status}] "

                            output += homework_params
                            print(output)
        else:
            print("未获取到任何作业信息")
            return 1
    except KeyboardInterrupt:
        print("\n用户中断操作，程序退出")
        return 1
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
