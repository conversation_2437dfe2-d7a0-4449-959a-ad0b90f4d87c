---
description:
globs:
alwaysApply: false
---
# Python 代码风格规范

## 命名规范

- **类名**：使用驼峰命名法（CamelCase）
  ```python
  class ChaoxingHomeworkManager:
      pass
  ```

- **函数和变量名**：使用小写字母和下划线（snake_case）
  ```python
  def get_course_list():
      pass
      
  student_name = "张三"
  ```

- **常量**：使用大写字母和下划线
  ```python
  MAX_RETRY_COUNT = 3
  DEFAULT_TIMEOUT = 30
  ```

- **私有属性和方法**：使用单下划线前缀
  ```python
  class User:
      def __init__(self):
          self._password = None
          
      def _encrypt_password(self):
          pass
  ```

## 代码格式

- **缩进**：使用4个空格进行缩进，不使用制表符
- **行长度**：每行不超过100个字符
- **空行**：
  - 顶级函数和类定义之间空两行
  - 类中的方法定义之间空一行
- **导入**：按照import-order.mdc中的规则组织导入语句

## 注释规范

- **文档字符串**：为模块、类、函数编写文档字符串
  ```python
  def get_homework_list(course_id, clazz_id, person_id):
      """
      获取作业列表
      :param course_id: 课程ID
      :param clazz_id: 班级ID
      :param person_id: 个人ID
      :return: 作业列表
      """
      pass
  ```

- **行注释**：使用`#`后跟一个空格
  ```python
  # 这是一个行注释
  x = x + 1  # 增加计数器
  ```

## 异常处理

- 使用具体的异常类型而非捕获所有异常
- 提供有意义的错误信息
- 使用try/except/else/finally结构

```python
try:
    response = session.get(url, timeout=5)
    response.raise_for_status()
except requests.Timeout:
    print("请求超时，请检查网络连接")
except requests.HTTPError as e:
    print(f"HTTP错误: {e}")
except Exception as e:
    print(f"发生未知错误: {e}")
else:
    # 处理成功的响应
    return response.json()
finally:
    # 无论成功与否都执行的清理代码
    session.close()
```

## 最佳实践

- **显式优于隐式**：避免使用魔法方法和隐式行为
- **函数设计**：函数应该做一件事，并且做好
- **参数验证**：验证函数参数，提供有意义的错误信息
- **返回值**：保持返回值类型的一致性
- **日志记录**：使用日志而非print语句进行调试和信息记录
