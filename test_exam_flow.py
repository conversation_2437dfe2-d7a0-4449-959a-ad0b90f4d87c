#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试章节测验.py的HTTP请求模式流程
验证修改后的代码是否能正确处理考试入口流程
"""

import sys
import os
import requests
from bs4 import BeautifulSoup

def test_exam_entrance_parsing():
    """测试考试入口页面解析"""
    print("=" * 50)
    print("测试考试入口页面解析")
    print("=" * 50)
    
    # 读取保存的考试入口页面
    try:
        with open("exam_entrance.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        print("✓ 成功读取exam_entrance.html文件")
    except FileNotFoundError:
        print("✗ exam_entrance.html文件不存在")
        return False
    
    # 解析HTML
    soup = BeautifulSoup(html_content, "html.parser")
    
    # 检查"我已阅读并同意"按钮
    agree_btn = soup.select_one(".face_agreement")
    if agree_btn:
        print("✓ 找到'我已阅读并同意'按钮")
        onclick = agree_btn.get("onclick", "")
        print(f"  - onclick: {onclick}")
    else:
        print("✗ 未找到'我已阅读并同意'按钮")
    
    # 检查"进入考试"按钮
    start_btn = soup.select_one("#startBtn")
    if start_btn:
        print("✓ 找到'进入考试'按钮")
        data_url = start_btn.get("data", "")
        onclick = start_btn.get("onclick", "")
        print(f"  - data URL: {data_url}")
        print(f"  - onclick: {onclick}")
        
        # 检查URL格式
        if data_url and "enc=?" not in data_url:
            print("✓ 考试URL格式正常")
        else:
            print("✗ 考试URL格式异常，包含enc=?")
    else:
        print("✗ 未找到'进入考试'按钮")
    
    # 检查确认对话框
    confirm_btn = soup.select_one("#tabIntoexam2")
    if confirm_btn:
        print("✓ 找到确认对话框中的'进入考试'按钮")
        onclick = confirm_btn.get("onclick", "")
        print(f"  - onclick: {onclick}")
    else:
        print("✗ 未找到确认对话框中的'进入考试'按钮")
    
    # 检查隐藏参数
    hidden_inputs = soup.select("input[type='hidden']")
    print(f"✓ 找到 {len(hidden_inputs)} 个隐藏参数")
    
    important_params = ["examId", "answerId", "examEnc", "openc"]
    for param in important_params:
        input_elem = soup.select_one(f"#{param}")
        if input_elem:
            value = input_elem.get("value", "")
            print(f"  - {param}: {value}")
        else:
            print(f"  - {param}: 未找到")
    
    return True

def test_url_construction():
    """测试URL构建逻辑"""
    print("\n" + "=" * 50)
    print("测试URL构建逻辑")
    print("=" * 50)
    
    # 模拟从页面提取的参数
    course_id = "253891757"
    class_id = "124398558"
    exam_id = "7689933"
    relation_id = "166273372"
    
    # 测试原始URL构建
    original_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={course_id}&classId={class_id}&examId={exam_id}&examAnswerRelationId={relation_id}&endTime=2025-07-28 23:59:59.0&testPaperId=453977760&isRetest=false&enc=ab8e8c339deb440e1485f6c949d5495e"
    print(f"原始URL: {original_url}")
    
    # 测试从页面data属性提取的URL
    data_url = "/exam-ans/exam/test/reVersionTestStartNew?courseId=253891757&classId=124398558&tId=7689933&id=166273372&p=1&tag=1&enc=?&cpi=404722142&openc=32b95692130f772c1f237eb6146cf0bc&newMooc=true"
    full_data_url = f"https://mooc1.chaoxing.com{data_url}"
    print(f"页面data URL: {full_data_url}")
    
    # 检查URL差异
    print("\nURL差异分析:")
    print("- 原始URL使用examId和examAnswerRelationId参数")
    print("- 页面URL使用tId和id参数")
    print("- 页面URL包含额外的p、tag、cpi、openc、newMooc参数")
    print("- 页面URL的enc参数为'?'，需要从隐藏输入获取")
    
    return True

def test_flow_simulation():
    """测试流程模拟"""
    print("\n" + "=" * 50)
    print("测试HTTP请求流程模拟")
    print("=" * 50)
    
    print("模拟的HTTP请求流程:")
    print("1. 访问考试入口页面")
    print("2. 解析页面，提取参数")
    print("3. 检查'我已阅读并同意'按钮（前端状态控制）")
    print("4. 获取'进入考试'按钮的data URL")
    print("5. 直接访问考试URL")
    print("6. 处理可能出现的滑块验证码")
    print("7. 成功进入考试页面")
    
    print("\n与浏览器模式的对比:")
    print("浏览器模式:")
    print("- 实际点击按钮")
    print("- 执行JavaScript函数")
    print("- 处理页面跳转")
    print("- 在浏览器中拖拽滑块")
    
    print("\nHTTP请求模式:")
    print("- 解析页面获取URL")
    print("- 直接访问目标URL")
    print("- 通过API处理验证码")
    print("- 添加validate参数重新请求")
    
    return True

def main():
    """主函数"""
    print("章节测验.py HTTP请求模式流程测试")
    
    # 运行测试
    test1 = test_exam_entrance_parsing()
    test2 = test_url_construction()
    test3 = test_flow_simulation()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"考试入口页面解析: {'✓ 通过' if test1 else '✗ 失败'}")
    print(f"URL构建逻辑测试: {'✓ 通过' if test2 else '✗ 失败'}")
    print(f"流程模拟测试: {'✓ 通过' if test3 else '✗ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n✓ 所有测试通过，HTTP请求模式应该可以正常工作")
    else:
        print("\n✗ 部分测试失败，可能需要进一步调试")
    
    print("\n建议:")
    print("1. 确保验证码处理模块正常工作")
    print("2. 检查网络连接和请求头设置")
    print("3. 如果HTTP模式失败，可以使用 --use-browser 参数")
    print("4. 查看控制台输出了解详细的执行流程")

if __name__ == "__main__":
    main()
