# 超星学习通作业列表API服务

这是一个基于Flask的API服务，用于获取超星学习通的作业列表。服务通过接收用户的超星学习通账号和密码，返回所有课程的作业列表信息。

## 功能特点

- 使用AES-CBC模式加密实现超星学习通的登录机制
- 提供API接口接收用户账号密码，返回作业列表
- 支持过滤已完成作业和已结束课程
- 返回简化的作业信息，包含标题、状态和作业参数

## 安装依赖

服务运行需要以下依赖库：

```bash
pip install flask requests beautifulsoup4 pyaes
```

## 启动服务

执行以下命令启动API服务：

```bash
python chaoxing_api_server.py
```

服务将在本地127.0.0.1:666端口运行。

## API使用说明

### 获取作业列表

**请求URL**: `http://127.0.0.1:666/get_homeworks`

**请求方法**: POST

**请求参数**:

```json
{
    "username": "你的超星账号",
    "password": "你的超星密码",
    "include_finished": false,
    "include_ended_courses": false
}
```

参数说明：
- `username`: 超星学习通账号（必填）
- `password`: 超星学习通密码（必填）
- `include_finished`: 是否包含已完成的作业（可选，默认false）
- `include_ended_courses`: 是否包含已结束的课程（可选，默认false）

**响应格式**:

成功响应：
```json
{
    "success": true,
    "homeworks": [
        {
            "title": "作业标题",
            "status": "作业状态",
            "params": "课程ID|班级ID|个人ID|作业ID|答案ID|enc参数"
        },
        ...
    ]
}
```

失败响应：
```json
{
    "error": "错误信息"
}
```

## 使用示例

使用Python请求示例：

```python
import requests
import json

url = "http://127.0.0.1:666/get_homeworks"
data = {
    "username": "你的超星账号",
    "password": "你的超星密码",
    "include_finished": False,
    "include_ended_courses": False
}

response = requests.post(url, json=data)
result = response.json()

if "success" in result and result["success"]:
    print(f"找到 {len(result['homeworks'])} 个作业")
    for homework in result["homeworks"]:
        print(f"标题: {homework['title']}")
        print(f"状态: {homework['status']}")
        print(f"参数: {homework['params']}")
        print("-" * 30)
else:
    print(f"获取作业列表失败: {result.get('error', '未知错误')}")
```

## 注意事项

1. 本服务仅供学习和研究使用，请勿用于非法用途
2. 请勿频繁请求API，避免触发超星学习通的反爬机制
3. 账号密码仅在本地加密处理，不会上传到其他服务器

## 安全性说明

- 账号密码使用AES-CBC模式加密，确保安全传输
- 服务仅在本地运行，不对外网开放
- 不保存任何用户凭据信息

## 技术实现

- 使用Flask框架提供API服务
- 使用requests库进行HTTP请求
- 使用BeautifulSoup库解析HTML内容
- 使用pyaes库实现AES加密 